<script setup lang="ts">
import ColorPick from "@/helpers/colorpick";
import placeholder from "@/assets/icons/logo.png";
import { useI18n } from "vue-i18n";
import { computed, onMounted } from "vue";
import AppConfig from "@/appconfig";
import type { CartProductsDto } from "@/dto/cart_summary.dto";

// Language
const { t } = useI18n();

interface Props {
  orderId: string | undefined;
  orderDate: string | undefined;
  totalPrice: string | undefined;
  orderType: string | undefined;
  status: string | undefined;
  productList?: CartProductsDto;
}
const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "click"): void;
}>();

const onClick = () => {
  emit("click");
};

onMounted(() => {});
</script>

<template>
  <div class="order-item pointer" @click="onClick()">
    <!-- Header (Status & Order ID) -->
    <div class="order-header">
      <!-- Left- content -->
      <div class="order-info">
        <div class="order-type">{{ orderType }}</div>
        <span class="order-id-title">{{ `${t("order_id")}: ` }}</span>
        <span class="order-id">#{{ orderId }}</span>
      </div>

      <!-- Right- status -->
      <div class="order-status">
        {{ status }}

        <VIcon size="18" :color="ColorPick.primaryColor"
          >mdi-chevron-right</VIcon
        >
      </div>
    </div>

    <div style="height: 10px" />

    <!-- Order Date & Time -->
    <div class="order-date">{{ orderDate }}</div>

    <!-- Product list -->
    <div v-for="product in productList?.normal">
      <div class="product">
        <div class="product-image">
          <VImg
            :src="product.image || placeholder"
            :cover="true"
            rounded="lg"
            height="80"
            width="80"
          />
        </div>

        <div style="width: 20px" />

        <div class="product-details">
          <div class="flex">
            <h3 class="product-title">{{ product.name }}</h3>
            <VSpacer />
            <div style="width: 10px" />
          </div>

          <div v-if="product.unit_weight !== null" class="product-desc">
            {{ t("weight") }}: {{ product.unit_weight }}
          </div>
          <VSpacer />

          <div class="flex">
            <div
              class="product-price"
              :style="{
                fontSize: '11px',
              }"
            >
              {{ AppConfig.currencyMark }}
              {{ product.total_price ?? "0.00" }}
            </div>
            <VSpacer />
            <div style="width: 10px" />
            <div class="product-count">x{{ product.quantity }}</div>
          </div>
        </div>
      </div>
      <div style="height: 10px" />
    </div>

    <!-- Total Price -->
    <div class="order-total">
      <span>{{ t("total_cap") }}:</span>
      <span> {{ AppConfig.currencyMark }} {{ totalPrice }} </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.flex {
  display: flex;
}

.pointer {
  cursor: pointer;
}

// Order Item
.order-item {
  color: v-bind("ColorPick.primaryColor");
  background-color: v-bind("ColorPick.secondaryColor");
  position: relative;
  border-radius: 2px;
  margin: 15px 0;
  padding: 15px 20px;
}

/* Header */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.order-info {
  display: flex;
  align-items: center;
  gap: 8px; /* Adds spacing between Order Type and Order ID */
}

.order-type {
  border-radius: 20px;
  padding: 2px 10px;
  font-size: 8px;
  color: white;
  background-color: v-bind("ColorPick.primaryColor");
}

.order-status {
  font-size: 12px;
  cursor: pointer;
}

.order-id-title {
  font-size: 15px;
  font-weight: 400;
  margin-left: 5px;
}

.order-id {
  font-size: 9px;
  margin-left: 6px;
}

/* Date */
.order-date {
  display: flex;
  font-size: 11px;
  margin-top: 5px;
}
// Order Item

// Order Details - product item
.product {
  display: flex;
  margin: 5px 0;
}

.product-image {
  max-width: 80px;
}

.product-details {
  display: flex;
  flex-direction: column;
  text-align: left;
  flex-grow: 1;
}

.product-title {
  font-size: 12px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}

.product-desc {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: v-bind("ColorPick.primaryColor");
  font-size: 10px;
  font-weight: 400;
}

.product-price {
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}

.product-count {
  color: v-bind("ColorPick.primaryColor");
  font-size: 11px;
  font-weight: 400;
}

// Order total
.order-total {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 13px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
  gap: 12px;
}
</style>
