<script setup lang="ts">
import ButtonPlus from '@/components/custom_control/ButtonPlus.vue';
import { useI18n } from 'vue-i18n';
import ColorPick from '@/helpers/colorpick';

//Translations
const { t } = useI18n()

//Variables
const currentPath = window.location

//Check if current path is dashboard redirect to dashboard
function goBackToHome(): string {
  return '/'
}

</script>

<template>
  <div style="height: 70px;"></div>
  <LogoWithContent>
    <ErrorHeader statusCode="404" :title="t('page_not_found')"
      :description="t('no_webpage_was_found') + ' ' + currentPath" :style="{ color: ColorPick.fontColorWhite }" />
    <div class="error-back-to-home-button">
      <ButtonPlus :to="goBackToHome()" @click="() => { }" class="mt-10 max-width-handle" variant="flat"
        :color="ColorPick.buttonColorAuth">
        {{ t('back_to_home') }}
      </ButtonPlus>
    </div>
  </LogoWithContent>
  <div style="height: 70px;"></div>
</template>

<style lang="scss" scoped>
.max-width-handle {
  inline-size: 100%;
  max-inline-size: 600px;
  text-align: center;
}

.error-back-to-home-button {
  display: flex;
  justify-content: center;
  padding: 10px;
}
</style>
