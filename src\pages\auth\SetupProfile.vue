<script setup lang="ts">
import logo from "@/assets/icons/logo.png";
import ColorPick from "@/helpers/colorpick";
import ValidationHelper from "@/helpers/validation_helper";
import { useI18n } from "vue-i18n";
import { ref, computed, onMounted } from "vue";
import RaceEnum from "@/enums/RaceEnum";
import PreferredLanguageEnum from "@/enums/PreferredLanguageEnum";
import SocialMediaEnum from "@/enums/SocialMediaEnum";
import RoutersHelper from "@/helpers/routes_helper";
import { useRoute, useRouter } from "vue-router";
import type { VForm } from "vuetify/components";
import AuthPageApi from "@/services/auth_api";
import success from "@/assets/images/success.png";
import localStorageHelper from "@/helpers/localstorage_helper";
import { UserDto } from "@/dto/user.dto";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const isLoading = ref<boolean>(false);
const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");
const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>("");
const openSuccessDialog = ref<boolean>(false);

//Values
const phone = ref<string>("");
const refForm = ref<VForm>();

//Variables
const name = ref<string>("");
const email = ref<string>("");
const showDateDOBPicker = ref<boolean>(false);
const tempDOB = ref<Date>(new Date("1990"));

const dobValue = computed(() => {
  const formatDate = (date: Date) => {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Month is 0-based
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };
  if (tempDOB.value) {
    return formatDate(tempDOB.value);
  }
  return;
});

const referralCode = ref<string>();

//Functions
async function onSubmit() {
  const validated = await refForm.value?.validate();

  if (validated?.valid) {
    try {
      //If Loading will no do anything
      if (isLoading.value) {
        snackBarMsg.value = t("please_wait...");
        openSnackBar.value = true;
        return;
      }

      //Show Loader
      isLoading.value = true;

      var tempdata = await AuthPageApi.register(
        name.value,
        email.value,
        tempDOB.value,
        phone.value,
      );

      const tempUserDto = new UserDto(tempdata);

      if (tempUserDto.token) {
        localStorageHelper.setUserToken(tempUserDto.token);
      }

      openSuccessDialog.value = true;
    } catch (e) {
      dialogMsg.value = `${e}`;
      openDialog.value = true;
    } finally {
      isLoading.value = false;
    }
  }
}

onMounted(() => {
  if (route.query.phoneNumber) {
    phone.value = route.query.phoneNumber as string;
  } else {
    router.push(RoutersHelper.login);
  }

  if (route.query.referralCode) {
    referralCode.value = route.query.referralCode as string;
  }
});
</script>

<template>
  <!-- Success Dialog -->
  <DialogPlus
    persistent
    v-model="openSuccessDialog"
    :onClickConfirm="
      () => {
        openSuccessDialog = false;
        router.push(RoutersHelper.home);
      }
    "
  >
    <div class="success-dialog">
      <VImg :src="success" cover height="85" width="85" />

      <div style="height: 30px" />

      <div class="dialog-title" :style="{ color: ColorPick.fontColor }">
        {{ t("register_success") }}
      </div>
      <div style="height: 10px" />
      <div class="dialog-desc" :style="{ color: ColorPick.dialogDescription }">
        {{ t("register_success_desc") }}
      </div>
    </div>
  </DialogPlus>

  <DialogPlus
    v-model="openDialog"
    :title="t('set_up_your_profile')"
    :confirmText="t('okay')"
    :onClickConfirm="
      () => {
        openDialog = false;
      }
    "
  >
    {{ dialogMsg }}
  </DialogPlus>
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>
  <!-- Loading Plus -->
  <LoadingPlus v-if="isLoading" />
  <div class="setup-profile">
    <VForm
      class="setup-profile-container"
      @submit.prevent="onSubmit"
      ref="refForm"
    >
      <div style="height: 50px"></div>
      <div class="setup-profile-logo">
        <VImg :src="logo" :cover="false" height="60" width="60" />
      </div>
      <h1 class="setup-profile-title">
        {{ t("set_up_your_profile") }}
      </h1>
      <div class="setup-profile-description">
        {{ t("setup_profile_description") }}
      </div>
      <div style="height: 20px"></div>
      <VCard class="setup-profile-card">
        <VCardText>
          <TextFieldPlus
            v-model="name"
            :placeholder="t('name')"
            :rules="[ValidationHelper.required]"
          >
          </TextFieldPlus>
          <TextFieldPlus
            v-model="email"
            :placeholder="t('email')"
            :rules="[ValidationHelper.required, ValidationHelper.email]"
          ></TextFieldPlus>
          <!-- DOB use datepicker and textfield -->
          <VMenu
            v-model="showDateDOBPicker"
            :close-on-content-click="false"
            transition="scale-transition"
          >
            <template v-slot:activator="{ props }">
              <TextFieldPlus
                v-model="dobValue"
                :placeholder="t('date_of_birth')"
                readonly
                v-bind="props"
                class="date-input"
                :rules="[ValidationHelper.required]"
              />
            </template>
            <VDatePicker v-model="tempDOB">
              <template v-slot:header>
                <div class="date-picker-header">
                  <div class="d-flex justify-end align-center px-4 py-2">
                    <div class="d-flex gap-2">
                      <VBtn
                        color="primary"
                        size="small"
                        @click="showDateDOBPicker = false"
                      >
                        {{ t("okay") }}
                      </VBtn>
                    </div>
                  </div>
                </div>
              </template>
            </VDatePicker>
          </VMenu>
          <!-- <SelectPlus v-model="race" :items="raceList" item-value="value" item-title="name"
                        :placeholder="t('race')" :rules="[ValidationHelper.required]">
                    </SelectPlus>
                    <SelectPlus v-model="preferredLanguage" :items="preferredLanguageList" item-value="value"
                        item-title="name" :placeholder="t('preferred_language')" :rules="[ValidationHelper.required]">
                    </SelectPlus>
                    <SelectPlus v-model="socialMedia" :items="socialMediaList" item-value="value" item-title="name"
                        :placeholder="t('how_did_you_hear_about_us')" :rules="[ValidationHelper.required]">
                    </SelectPlus>
                    <TextFieldPlus v-model="referralCode" :placeholder="t('referral_code')"></TextFieldPlus> -->
          <div style="height: 20px"></div>
        </VCardText>
        <VCardActions>
          <ButtonPlus
            class="setup-profile-continue"
            :color="ColorPick.buttonColorAuth"
            type="submit"
            @click="refForm?.validate"
          >
            {{ t("continue") }}
          </ButtonPlus>
        </VCardActions>
      </VCard>
    </VForm>
  </div>
</template>

<style lang="scss" scoped>
.setup-profile {
  width: 100%;
  position: absolute;
  justify-items: center;
  text-align: -webkit-center;
  overflow: auto;
  height: 100%;
}

.setup-profile-container {
  padding: 20px;
  max-width: 600px;
}

.setup-profile-logo {
  padding: 10px;
}

.setup-profile-title {
  font-size: 32px;
  font-weight: 700;
  text-align: center;
  padding: 3px;
  color: v-bind("ColorPick.fontColorWhite");
}

.setup-profile-description {
  font-size: 17px;
  font-weight: 500;
  text-align: center;
  padding: 3px;
  color: v-bind("ColorPick.fontColorWhite");
}

.setup-profile-card {
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
  padding: 15px;
  overflow: visible;
}

.setup-profile-continue {
  width: 100%;
}

.success-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.dialog-title {
  font-size: 24px;
  font-weight: 600;
}

.dialog-desc {
  font-size: 16px;
  font-weight: 500;
}
</style>
