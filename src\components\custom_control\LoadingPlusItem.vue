<script setup lang="ts">
import Logo from '@/assets/icons/logo.png';
interface Props {
    loaderWidth?: number,
    loaderSized?: number,
    loaderSpeed?: number,
}

const props = defineProps<Props>();


// Style that can be customized by passing params to this component
const loaderStyle = {
    'width': `${props.loaderSized || 50}px`,
    'height': `${props.loaderSized || 50}px`,
    'animation': `spin ${props.loaderSpeed || 5}s linear infinite`,
}

</script>

<template>
    <div :style="loaderStyle">
        <img :src="Logo" style="width: 100%; height: 100% "></img>
    </div>
</template>

<style lang="scss">
// Spin animation
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    37.5% {
        // 0.3s / (0.3s + 0.5s) = 37.5%
        transform: rotate(360deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>