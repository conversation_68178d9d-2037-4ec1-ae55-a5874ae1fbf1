<script setup lang="ts">
// Components
import LoadingPlus from "@/components/custom_control/LoadingPlus.vue";
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";

// Helpers
import ColorPick from "@/helpers/colorpick";
import RoutersHelper from "@/helpers/routes_helper";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import { ref, onMounted, onUnmounted, watch } from "vue";

// Dtos
import type { PaginationDto } from "@/dto/pagination.dto";
import { NewsAnnouncementDto } from "@/dto/news_announcement.dto";

// Apis
import NewsAnnouncementApi from "@/services/news_announcement_api";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// News announcement list
const page = ref<number>(1);
const hasMore = ref(true);
let doneCopy: (status: "error" | "loading" | "empty" | "ok") => void;
// const page = ref<number>(+(route.query?.page ?? 1));
const newsPagination = ref<PaginationDto>();

const isLoading = ref<boolean>(false);
const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>("");

const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

const data = ref<NewsAnnouncementDto[]>([]);

// Fetch Screen Width
const screenWidth = ref(window.innerWidth);
const screenHeight = ref(window.innerHeight);

const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth;
};
const updateScreenHeight = () => {
  screenHeight.value = window.innerHeight;
};

// Function
function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

function toNewsAnnouncementDetails(id: string) {
  router.push(`${RoutersHelper.newsAnnouncementDetails}/${id}`);
}

async function loadNewsAnnouncementList(): Promise<PaginationDto | undefined> {
  try {
    const response = await NewsAnnouncementApi.list(page.value);
    return response;
  } catch (_) {}
  return undefined;
}

// News Announcement List - Infinite Scroll
async function infiniteload({
  done,
}: {
  done: (status: "error" | "loading" | "empty" | "ok") => void;
}) {
  if (!hasMore.value) {
    done("empty");
    return;
  }

  try {
    const res = await loadNewsAnnouncementList();

    if (res?.data !== undefined) {
      data.value.push(...res.data);

      const lastPage = res.last_page ?? 1;

      if (page.value >= lastPage) {
        hasMore.value = false;

        // For refresh the page
        doneCopy = done;
      } else {
        hasMore.value = true;

        page.value += 1;
      }
    } else {
      hasMore.value = false;
      done("empty");
      doneCopy = done;
    }
  } catch (e) {
  } finally {
    done("ok");
  }
}

onMounted(() => {
  window.scrollTo(0, 0);
  window.addEventListener("resize", updateScreenWidth);
  window.addEventListener("resize", updateScreenHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", updateScreenWidth);
  window.removeEventListener("resize", updateScreenHeight);
});

// watch(page, (value) => {
//   var tempPage: number = isNaN(value) ? 1 : value;

//   // Update query param
//   router.push({
//     path: RoutersHelper.newsAnnouncement,
//     query: { page: tempPage },
//   });

//   // Reload data
//   loadNewsAnnouncementList();
// });

// watch(route, (value) => {
//   page.value = isNaN(+(value.query?.page ?? 1)) ? 1 : +(value.query?.page ?? 1);

//   loadNewsAnnouncementList();
// });
</script>

<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading" />

  <!-- Snack Bar -->
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>

  <AppBarPlus
    :title="t('news_announcement')"
    :back="true"
    :currentPage="page"
  />

  <!-- AppBarPlus Spacing -->
  <div style="height: 60px"></div>

  <!-- Body  -->
  <div class="news-announcement">
    <div
      class="news-announcement-container"
      :class="screenWidth < 615 ? 'pad' : ''"
      :style="{ height: `${screenHeight - 60}px`, width: `${screenWidth}px` }"
    >
      <div class="news-announcement-content">
        <VInfiniteScroll
          :height="'100%'"
          :items="data"
          @load="infiniteload"
          v-model="hasMore"
        >
          <template v-slot:empty>
            <NoData v-if="data?.length <= 0" />
            <span v-else>{{ t("no_more") }}</span>
          </template>
          <div
            v-if="data && data?.length > 0"
            class="news-announcement-image"
            @click="toNewsAnnouncementDetails(item.id.toString())"
            v-for="item in data"
          >
            <VImg
              width="600"
              height="250"
              :cover="true"
              :src="item.single_image_url"
              :style="{ borderRadius: '10px', cursor: 'pointer' }"
            />
            <div style="height: 20px"></div>
          </div>
        </VInfiniteScroll>

        <!-- <NoData v-else-if="!isLoading" /> -->

        <VSpacer />

        <div style="height: 70px"></div>

        <!-- <div class="pagination">
          <VPagination
            v-model="page"
            :length="newsPagination?.last_page ?? 1"
            rounded="circle"
          /> -->
        <!-- </div> -->
      </div>
    </div>
  </div>

  <!-- Dialog -->
  <DialogPlus
    persistent
    v-model="openDialog"
    :confirmText="t('back')"
    :onClickConfirm="
      () => {
        openDialog = false;
        goBack();
      }
    "
  >
    {{ dialogMsg }}
  </DialogPlus>
</template>

<style lang="scss" scoped>
.pad {
  padding: 5px 10px;
}

.news-announcement {
  width: 100%;
  display: inline-table;
  text-align: -webkit-center;
}

.news-announcement-container {
  height: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.news-announcement-content {
  display: flex;
  flex-direction: column;
  text-align: left;
  flex-grow: 1;
}

.news-announcement-image {
  max-width: 600px;
  width: "100%";
  padding: 5px 0 5px 0;
}

.pagination {
  position: fixed;
  width: 100%;
  max-width: 600px;
  bottom: 0;
  background-color: v-bind("ColorPick.backgroundColor");
  padding: 10px 0;
}
</style>
