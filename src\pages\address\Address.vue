<script setup lang="ts">
import { useI18n } from "vue-i18n";
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";
import AddressList from "@/components/address/AddressList.vue";

const { t } = useI18n();
</script>

<template>
  <div class="address">
    <div class="address-container">
      <AddressList />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.address {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
}

.address-container {
  padding: 15px;
  max-width: 600px;
  width: 100%;
  height: 100%;
}
</style>
