<script setup lang="ts">
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";

interface Props {
  title?: string | undefined;
  htmlContent?: string | undefined;
}
const props = defineProps<Props>();
const { t } = useI18n();
</script>

<template>
  <div class="general-content">
    <div class="general-content-container">
      <AppBarPlus :title="props.title" :back="true" />

      <!-- AppBarPlus Spacing -->
      <div style="height: 70px"></div>

      <div
        class="general-content-html-content"
        v-html="props.htmlContent"
      ></div>
      <slot></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.general-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.general-content-container {
  max-width: 600px;
  width: 100%;
  padding: 10px;
}

.general-content-html-content {
  text-align: left;
  width: 100%;
  color: v-bind("ColorPick.fontColor");
}
</style>
