<script setup lang="ts">
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { defineProps, defineEmits, computed } from "vue";
import ColorPick from "@/helpers/colorpick";

const props = defineProps<{
  modelValue: boolean;
  isBack: boolean;
}>();

const emit = defineEmits(["update:modelValue"]);

const router = useRouter();
const { t } = useI18n();

// Model binding so v-model works
const modelValue = computed({
  get: () => props.modelValue,
  set: (val: boolean) => emit("update:modelValue", val),
});

const goToLogin = () => {
  router.push("/login"); // or RoutersHelper.login if you're using a helper
};

const toHomePage = () => {
  props.isBack ? emit("update:modelValue", false) : router.push("/home");
};
</script>

<template>
  <VDialog v-model="modelValue" max-width="600px" scrollable persistent>
    <VCard class="login-card">
      <div class="login-title">{{ t("required_to_login") }}</div>
      <div style="height: 10px"></div>

      <VCardActions>
        <ButtonPlus
          class="login-next"
          :color="ColorPick.primaryColor"
          @click="goToLogin"
        >
          {{ t("login") }}
        </ButtonPlus>
      </VCardActions>

      <div style="height: 20px"></div>

      <div class="divider">
        <span>{{ t("or") }}</span>
      </div>

      <VContainer class="guest-container" @click="toHomePage">
        <VBtn variant="text" class="guess-title">
          {{ t("continue_as_guest") }}
        </VBtn>
      </VContainer>
    </VCard>
  </VDialog>
</template>

<style scoped>
.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
  padding: 15px;
  overflow: visible;
  margin: auto;
}

.login-title {
  margin: auto;
  font-size: 20px;
  font-weight: bold;
}

.login-next {
  width: 100%;
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

.divider::before,
.divider::after {
  content: "";
  flex: 1;
  height: 1px;
  background-color: gray;
  margin: 0 10px;
}

.guest-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  text-align: center;
}

.guess-title {
  font-size: 12px;
  color: v-bind("ColorPick.primaryColor");
  font-weight: 500;
  margin: 0 auto;
  padding: 8px 12px;
  text-transform: uppercase;
}
</style>
