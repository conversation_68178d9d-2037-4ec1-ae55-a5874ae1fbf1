export class CouponDto {
    id: number;
    total_discount: string;
    name: string;
    type: string;
    coupon_value: string;


    constructor(data: any) {
        this.id = data.id;
        this.total_discount = data.total_discount;
        this.name = data.name;
        this.type = data.type;
        this.coupon_value = data.coupon_value;
    }
}

export class CreateCouponDto {
    coupon_id: number;
    discount_amount: string;
    sub_amount: string;
    total_amount: string;

    constructor(data: any) {
        this.coupon_id = data.coupon_id;
        this.discount_amount = data.discount_amount;
        this.sub_amount = data.sub_amount;
        this.total_amount = data.total_amount;
    }
}