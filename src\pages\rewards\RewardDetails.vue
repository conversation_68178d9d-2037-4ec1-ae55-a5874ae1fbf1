<script setup lang="ts">
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";
import ButtonPlus from "@/components/custom_control/ButtonPlus.vue";
import ColorPick from "@/helpers/colorpick";
import ThousandsHelper from "@/helpers/thousands_helper";
import moment from "moment";
import RewardApi from "@/services/reward_api";
import { useI18n } from "vue-i18n";
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import type { RewardsDto } from "@/dto/rewards.dto";

import { VPullToRefresh } from "vuetify/labs/VPullToRefresh";
import localStorageHelper from "@/helpers/localstorage_helper";
import RoutersHelper from "@/helpers/routes_helper";
import LoadingPlus from "@/components/custom_control/LoadingPlus.vue";


// language
const { t } = useI18n();

// Route
const route = useRoute();
const router = useRouter();

const isLoading = ref<boolean>(false);

const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>("");
var clickConfirmDialog: () => void;

const pullDownThreshold = ref(60);

// Login Dialog
const openLoginDialog = ref<boolean>(false);

//Comfirmation Dialog
const openConfirmationDialog = ref<boolean>(false);
const confirmationDialogMsg = ref<string>("");
var confirmRedeemOrUse: () => void;

// Values
var token: string | null = null;

// data
const rewardDetail = ref<RewardsDto>();

// Functions
function onClickRedeemOrUse() {
  if (route.query.isMyReward === 'true') {
    confirmationDialogMsg.value = t('are_you_sure_you_are_in_the_store_and_using_this_voucher_in_the_store');
    confirmRedeemOrUse = () => {
      openConfirmationDialog.value = false;
      useVoucher();
    };
  } else {
    confirmationDialogMsg.value = t('are_you_sure_you_want_to_redeem_this_voucher');
    confirmRedeemOrUse = () => {
      openConfirmationDialog.value = false;
      redeemVoucher();
    };
  }

  openConfirmationDialog.value = true;

};


async function loadRedeemableVoucherDetails() {
  //If Loading will no do anything
  if (isLoading.value) {
    snackBarMsg.value = t("please_wait...");
    openSnackBar.value = true;
    return;
  }

  try {
    isLoading.value = true;

    if (route.query.isMyReward === 'true') {
      rewardDetail.value = await RewardApi.redeemedDetail(
        route.params.id as string
      );
    } else {
      rewardDetail.value = await RewardApi.redeemableDetail(
        route.params.id as string
      );
    }

  } catch (error) {
    dialogMsg.value = `${error}`;
    openDialog.value = true;
    clickConfirmDialog = () => {
      openDialog.value = false;
      goBack();
    }
  } finally {
    isLoading.value = false;
  }
}

// Confirmation Dialog


// Redeem Voucher
async function redeemVoucher() {
  //If Loading will no do anything
  if (isLoading.value) {
    snackBarMsg.value = t("please_wait...");
    openSnackBar.value = true;
    return;
  }

  try {
    if (
      rewardDetail.value?.can_redeem !== undefined &&
      rewardDetail.value?.can_redeem !== null &&
      !rewardDetail.value.can_redeem
    ) {
      snackBarMsg.value = t("unable_to_redeem");
      openSnackBar.value = true;
      return;
    }

    isLoading.value = true;
    // Redeem voucher api
    var result = await RewardApi.redeemVoucher(route.params.id as string);

    if (result) {
      dialogMsg.value = t('voucher_redeemed_successfully');
      clickConfirmDialog = () => {
        openDialog.value = false;
      }
      openDialog.value = true;
    }

  } catch (error) {
    dialogMsg.value = `${error}`;
    openDialog.value = true;
    clickConfirmDialog = () => {
      openDialog.value = false;
      goBack();
    }

  } finally {
    isLoading.value = false;
  }
}

async function useVoucher() {
  //If Loading will no do anything
  if (isLoading.value) {
    snackBarMsg.value = t("please_wait...");
    openSnackBar.value = true;
    return;
  }

  try {
    if (
      rewardDetail.value?.can_redeem !== undefined &&
      rewardDetail.value?.can_redeem !== null &&
      !rewardDetail.value.can_redeem
    ) {
      snackBarMsg.value = t("unable_to_redeem");
      openSnackBar.value = true;
      return;
    }

    isLoading.value = true;
    // Redeem voucher api
    var result = await RewardApi.voucherUseNow(route.params.id as string);


    if (result) {
      dialogMsg.value = t('voucher_used_successfully');

      clickConfirmDialog = () => {
        openDialog.value = false;
        goBack();
      }
      openDialog.value = true;
    }

  } catch (error) {
    dialogMsg.value = `${error}`;
    openDialog.value = true;
    clickConfirmDialog = () => {
      openDialog.value = false;
      goBack();
    }

  } finally {
    isLoading.value = false;
  }
}

function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

// onRefresh For Pull To Refresh
async function refresh({ done }: { done: () => void }) {

  try {
    await loadRedeemableVoucherDetails();
  } catch (error) {
  } finally {
    done();
  }
}


onMounted(() => {
  window.scrollTo(0, 0);
  token = localStorageHelper.getUserToken();

  loadRedeemableVoucherDetails();
});

</script>

<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading && !rewardDetail" />

  <!-- Snack Bar -->
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>

  <!-- Dialog -->
  <DialogPlus persistent v-model="openDialog" :confirmText="t('back')" :onClickConfirm="clickConfirmDialog
    ">
    {{ dialogMsg }}
  </DialogPlus>

  <div class="reward-details">
    <div class="reward-details-container">
      <AppBarPlus :title="t('reward_details')" :back="true" />
      <div style="height: 70px" />

      <VPullToRefresh :pullDownThreshold="pullDownThreshold" @load="refresh">
        <div :style="{ padding: '10px 15px' }">
          <div v-if="rewardDetail?.list_image_url" class="reward-details-image"
            :style="{ backgroundImage: 'url(' + rewardDetail?.list_image_url + ')' }">
            <!-- <div :style="{ padding: '15px', height: '100%', width: '100%' }">
            <VImg :src="rewardDetail?.image" :cover="false" />
          </div> -->
          </div>
          <div style="height: 10px" />
        </div>
        <div :style="{ padding: '10px 15px' }">
          <div v-if="rewardDetail?.name || rewardDetail?.description" class="reward-details-section-container">
            <div v-if="rewardDetail?.name" class="reward-details-title">{{ rewardDetail?.name }}</div>
            <div v-if="rewardDetail?.name" style="height: 5px" />
            <div v-if="rewardDetail?.description" class="reward-details-text">
              {{ rewardDetail?.description }}
            </div>
          </div>

          <div v-if="rewardDetail?.name || rewardDetail?.description" style="height: 30px" />

          <div
            v-if="rewardDetail?.voucher_value || rewardDetail?.free_product || rewardDetail?.terms_and_condition || rewardDetail?.how_to_use || rewardDetail?.available_end_date || rewardDetail?.min_spend || rewardDetail?.redeem_points"
            class="reward-details-section-container">
            <!-- Your Total Order -->
            <div v-if="rewardDetail?.voucher_value" class="reward-details-sub-title">
              {{ `${rewardDetail?.voucher_value} ${t('off')} ${t("your_total_order")}` }}
            </div>
            <!-- <div style="height: 5px" /> -->
            <div v-if="rewardDetail?.voucher_value" class="reward-details-text">
              {{ t('voucher_description_save').replace('[voucher]', rewardDetail?.voucher_value ?? '') }}
            </div>
            <!-- Your Total Order -->

            <div v-if="rewardDetail?.voucher_value" style="height: 20px" />

            <div v-if="rewardDetail?.free_product && rewardDetail?.free_product.length > 0" class="reward-details-text">
              <div v-for="product in rewardDetail?.free_product"
                :style="{ display: 'flex', alignItems: 'center', width: '100%', gap: '10px', padding: '5px 0px' }">
                <VImg v-if="product?.image" :src="product?.image" :width="25" :height="25" class="rounded-lg"
                  :cover="true" />
                <div :style="{ width: '100%' }">
                  <span :style="{ fontWeight: 600 }">
                    {{ product?.product_name ?? '' }}
                  </span>
                  <span v-if="product?.variation_name">
                    ({{ product?.variation_name }})
                  </span>
                  <span v-if="product?.quantity">
                    x{{ ThousandsHelper.format(product?.quantity ?? '0') }}
                  </span>
                </div>
              </div>
            </div>

            <div v-if="rewardDetail?.free_product && rewardDetail?.free_product.length > 0" style="height: 20px;" />


            <!-- How To Use -->
            <div v-if="rewardDetail?.how_to_use" class="reward-details-sub-title">
              {{ t("how_to_use") }}
            </div>
            <div v-if="rewardDetail?.how_to_use" style="height: 5px" />
            <div v-if="rewardDetail?.how_to_use" v-html="rewardDetail?.how_to_use" class="reward-details-text" />
            <!-- How To Use -->

            <div v-if="rewardDetail?.how_to_use" style="height: 20px" />

            <!-- Term and Condition -->
            <div v-if="rewardDetail?.terms_and_condition" class="reward-details-sub-title">
              {{ t("terms_and_condition") }}
            </div>
            <div v-if="rewardDetail?.terms_and_condition" style="height: 5px" />
            <div v-if="rewardDetail?.terms_and_condition" v-html="rewardDetail?.terms_and_condition"
              class="reward-details-text" />
            <!-- Term and Condition -->

            <div v-if="rewardDetail?.terms_and_condition" style="height: 20px" />


            <div
              v-if="rewardDetail?.min_spend !== undefined && rewardDetail?.min_spend !== null && rewardDetail.min_spend > 0"
              class="reward-details-sub-title">
              {{ t('minimum_spend') }}
            </div>


            <div
              v-if="rewardDetail?.min_spend !== undefined && rewardDetail?.min_spend !== null && rewardDetail.min_spend > 0"
              class="reward-details-text">
              {{ t('minumum_spend_of_amount_required').replace('[amount]',
                rewardDetail.min_spend.toString()) }}
            </div>
            <div
              v-if="rewardDetail?.min_spend !== undefined && rewardDetail?.min_spend !== null && rewardDetail.min_spend > 0"
              style="height: 20px;" />

            <div v-if="rewardDetail?.min_spend" style="height: 20px" />

            <div v-if="rewardDetail?.redeem_points !== undefined && rewardDetail?.redeem_points !== null"
              class="reward-details-sub-title">
              {{ t('points_required_to_redeem_this_voucher') }}
            </div>
            <div v-if="rewardDetail?.redeem_points !== undefined && rewardDetail?.redeem_points !== null"
              class="reward-details-text">
              {{ ThousandsHelper.format(rewardDetail.redeem_points) }} {{ t('points') }}
            </div>

            <div v-if="rewardDetail?.redeem_points !== undefined && rewardDetail?.redeem_points !== null"
              style="height: 20px" />


            <!-- Valid Until -->
            <div v-if="route.query.isMyReward !== 'true' && rewardDetail?.effective_end_date"
              class="reward-details-row">
              <div v-if="rewardDetail?.effective_end_date" class="reward-dedtails-sub-title">
                {{ t("valid_until") }}
              </div>
              <div v-if="rewardDetail?.effective_end_date" class="reward-details-text">{{
                moment(rewardDetail?.effective_end_date).format('DD.MM.YYYY, h:mm a').toUpperCase() }}</div>
            </div>
            <!-- Valid Until -->

            <div v-if="route.query.isMyReward !== 'true' && rewardDetail?.effective_end_date" style="height: 20px" />

            <!-- Effective Until -->
            <div v-if="route.query.isMyReward === 'true' && rewardDetail?.effective_end_date"
              class="reward-details-row">
              <div v-if="rewardDetail?.effective_end_date" class="reward-dedtails-sub-title">
                {{ t("effective_until") }}
              </div>
              <div v-if="rewardDetail?.effective_end_date" class="reward-details-text">{{
                moment(rewardDetail?.effective_end_date).format('DD.MM.YYYY, h:mm a').toUpperCase() }}</div>
            </div>
            <!-- Valid Until -->


          </div>
          <div style="height: 100px" />
        </div>

        <!-- Btn -->
        <div v-if="isLoading && rewardDetail" class="reward-details-bottom-button-container">
          <VProgressCircular indeterminate>
          </VProgressCircular>
        </div>

        <div v-if="!isLoading && rewardDetail" class="reward-details-bottom-button-container">
          <ButtonPlus v-if="!(route.query.isMyReward === 'true') && token" class="reward-details-bottom-button"
            @click="onClickRedeemOrUse()" :color="true ? ColorPick.primaryColor : ColorPick.onSurfaceColor"
            :disable="!rewardDetail?.can_redeem">
            {{  t("redeem_voucher") }}
          </ButtonPlus>

          <ButtonPlus v-else-if="token && rewardDetail.status.toLowerCase() === 'active'.toLowerCase()" class="reward-details-bottom-button" @click="onClickRedeemOrUse()"
            :color="true ? ColorPick.primaryColor : ColorPick.onSurfaceColor">
            {{ t("use_voucher_at_store") }}
          </ButtonPlus>


          <ButtonPlus v-if="!token" class="reward-details-bottom-button" @click="openLoginDialog = true"
            :color="true ? ColorPick.primaryColor : ColorPick.onSurfaceColor">
            {{ t("login") }}
          </ButtonPlus>
        </div>

        <!-- Btn -->
      </VPullToRefresh>
    </div>
  </div>
  <!-- Confirmation Dialog -->
  <DialogPlus v-model="openConfirmationDialog" :title="t('reward_details')" :confirmText="t('yes')"
    :cancelText="t('no')" :onClickConfirm="confirmRedeemOrUse" :onClickCancel="() => {
      openConfirmationDialog = false;
    }
      ">
    {{ confirmationDialogMsg }}
  </DialogPlus>
  <!-- Required login dialog -->
  <VDialog v-model="openLoginDialog" max-width="600px" scrollable :persistent="true">
    <VCard class="login-card">
      <div class="login-title">Required to Login</div>
      <div style="height: 10px"></div>

      <VCardActions>
        <ButtonPlus class="login-next" :color="ColorPick.primaryColor" @click="
          () => {
            router.push(RoutersHelper.login);
          }
        ">
          {{ t("login") }}
        </ButtonPlus>
      </VCardActions>

      <div style="height: 20px"></div>

      <div class="divider">
        <span>{{ t("or") }}</span>
      </div>

      <VContainer class="guest-container" @click="openLoginDialog = false">
        <VBtn variant="text" class="guess-title">
          {{ t("continue_as_guest") }}
        </VBtn>
      </VContainer>
    </VCard>
  </VDialog>
</template>

<style lang="scss" scoped>
.reward-details {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.reward-details-container {
  max-width: 600px;
  width: 100%;
}

.reward-details-section-container {
  background-color: v-bind("ColorPick.secondaryColor");
  padding: 10px 20px;
}

.reward-details-image {
  width: 100%;
  aspect-ratio: 11/4;
  background-size: cover;
  border-radius: 10px;
}

.reward-details-title {
  font-size: 24px;
  font-weight: 400;
  color: v-bind("ColorPick.fontColor");
  text-align: left;
}

.reward-details-sub-title {
  font-size: 15px;
  font-weight: 400;
  color: v-bind("ColorPick.fontColor");
  text-align: left;
}

.reward-details-text {
  font-size: 12px;
  font-weight: 400;
  color: v-bind("ColorPick.greyFontColor03");
  text-align: left;
}

.reward-details-bottom-button-container {
  padding: 20px;
  position: fixed;
  bottom: 0px;
  left: 0px;
  width: 100%;
  text-align: -webkit-center;
  background-color: v-bind("ColorPick.backgroundColor");
}

.reward-details-bottom-button {
  width: 100%;
  max-width: 600px;
  border-radius: 40px;
}

.reward-details-row {
  display: flex;
  align-items: center;
  gap: 15px;
}

// Dialog - Login
.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
  padding: 15px;
  overflow: visible;
  margin: auto;
}

.login-title {
  margin: auto;
  font-size: 20px;
  font-weight: bold;
}

.login-next {
  width: 100%;
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

.divider::before,
.divider::after {
  content: "";
  flex: 1;
  height: 1px;
  background-color: gray;
  margin: 0 10px;
}

.guest-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  text-align: center;
}

.guess-title {
  font-size: 12px;
  color: v-bind("ColorPick.primaryColor");
  font-weight: 500;
  margin: 0 auto;
  padding: 8px 12px;
  text-transform: uppercase;
}

// Dialog - Login</style>
