<script setup lang="ts">
import RoutersHelper from '@/helpers/routes_helper';
import { computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useDisplay } from 'vuetify';
import ColorPick from '@/helpers/colorpick';
import { useI18n } from 'vue-i18n';
import ValidationHelper from '@/helpers/validation_helper';
import TextFieldPlus from '../custom_control/TextFieldPlus.vue';
import TelInputPlus from '../custom_control/TelInputPlus.vue';
import ButtonPlus from '../custom_control/ButtonPlus.vue';
import { AxiosError } from 'axios';
import AddressApi from '@/services/address_api';
import type { AddressDto } from '@/dto/address.dto';
import type { VForm } from 'vuetify/components/VForm';
import GeneralPageApi from '@/services/general_page_api';
import GoogleMapPicker from '../custom_control/GoogleMapPicker.vue';
import "@/interfaces/getphonenumber";

interface Props {
    id?: string,
    canRoute?: boolean,
    refreshData?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
    canRoute: true,
});

const model = defineModel<boolean>();

const { t } = useI18n();

//Handle Route
const router = useRouter();

const { smAndDown } = useDisplay();

const isLoading = ref<boolean>(false);
const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>('');

//Values
const label = ref<string>();
const recipientName = ref<string>();
const phone = ref<string>();
const phoneNumberError = ref<string>();
const address = ref<string>();
const city = ref<string>();
const postalCode = ref<string>();
const stateSelected = ref();
const countrySelected = ref();
const stateLists = ref<any[]>([]);
const countryLists = ref<any[]>();
const latitude = ref<number>();
const longitude = ref<number>();

const refForm = ref<VForm>();

// Handle Google Map Picker
const isMapLoading = ref<boolean>(false);
const addressFull = computed(() => {
    if (address.value && city.value && postalCode.value && stateSelected.value) {
        return `${address.value}, ${city.value}, ${postalCode.value}, ${stateSelected.value}`;
    } else {
        return undefined;
    }
})
// Handle Google Map Picker


async function onInit() {
    // Reset
    label.value = undefined;
    recipientName.value = undefined;
    phone.value = undefined;
    phoneNumberError.value = undefined;
    address.value = undefined;
    city.value = undefined;
    postalCode.value = undefined;
    countrySelected.value = undefined;
    stateSelected.value = undefined;
    latitude.value = undefined;
    longitude.value = undefined;

    try {
        getStateList();
        getCountryList();

        if (props.id !== undefined && props.id !== null && props.id !== '') {
            await loadAddressDetails();
        }
    } catch (e) {
        if (e instanceof AxiosError) {
            var message: string = e.response?.data?.message ?? e['message'] ?? e.toString()

            snackBarMsg.value = message.toString();
            openSnackBar.value = true;
        } else if (e instanceof Error) {
            snackBarMsg.value = e.message.toString();
            openSnackBar.value = true;
        } else {
            snackBarMsg.value = t('oops');
            openSnackBar.value = true;
        }

        model.value = false;
    }
}

async function loadAddressDetails() {
    try {
        isLoading.value = true;

        let response: AddressDto = await AddressApi.details(props.id?.toString() ?? '');
        if (response !== undefined && response !== null) {
            label.value = response.label;
            recipientName.value = response.recipient;
            phone.value = response.phone_number;
            address.value = response.address;
            city.value = response.city;
            postalCode.value = response.postcode;
            countrySelected.value = response.country;
            stateSelected.value = response.state;
            latitude.value = response.latitude;
            longitude.value = response.longitude;
        }
    } catch (e) {
        if (e instanceof AxiosError) {
            var message: string = e.response?.data?.message ?? e['message'] ?? e.toString()

            snackBarMsg.value = message.toString();
            openSnackBar.value = true;
        } else if (e instanceof Error) {
            snackBarMsg.value = e.message.toString();
            openSnackBar.value = true
        } else {
            snackBarMsg.value = t('oops');
            openSnackBar.value = true;
        }
        router.push(RoutersHelper.address);
    } finally {
        isLoading.value = false;
    }
}

async function getStateList() {
    try {
        // Fetch only if not stored
        var tempStates = await GeneralPageApi.getStateList();

        stateLists.value = Object.entries(tempStates).map(([key, value]) => ({
            value: key,
            name: value,
        }));
    } catch (e) {
        throw e;
    }
}

async function getCountryList() {
    try {
        // Fetch only if not stored
        var tempStates = await GeneralPageApi.getCountryList();
        countryLists.value = Object.entries(tempStates).map(([key, value]) => ({
            value: Number(key),
            name: value,
        }));
    } catch (e) {
        throw e;
    }
}

async function onSave() {
    const validated = await refForm.value?.validate();
    phoneNumberError.value = undefined;

    if (phone.value === undefined || phone.value === null || phone.value === '') {
        phoneNumberError.value = t('fill_is_required');
        return;
    }

    // Is Map Loading
    if (isMapLoading.value) {
        snackBarMsg.value = t("please_wait_map_is_loading...");
        openSnackBar.value = true;
        return;
    }

    // Check Latitude and Longitude
    if (latitude.value === undefined || longitude.value === undefined || latitude.value === null || longitude.value === null) {
        snackBarMsg.value = t('unable_to_get_your_location');
        openSnackBar.value = true;
        return;
    }

    if (validated?.valid) {
        try {//If Loading will no do anything
            if (isLoading.value) {
                snackBarMsg.value = t("please_wait...");
                openSnackBar.value = true;
                return;
            }

            //Show Loader
            isLoading.value = true;

            if (props.id !== undefined && props.id !== null && props.id !== '') {
                var result = await AddressApi.edit(
                    props.id?.toString() ?? '',
                    label.value!,
                    recipientName.value!,
                    `${phone.value}`.getPhoneNumber(),
                    address.value!,
                    city.value!,
                    postalCode.value!,
                    countrySelected.value!,
                    stateSelected.value!,
                    latitude.value!,
                    longitude.value!
                );

                if (result) {
                    snackBarMsg.value = t('address_updated_successfully');
                } else {
                    snackBarMsg.value = result;
                }
            }
            else {
                var result = await AddressApi.create(
                    label.value!,
                    recipientName.value!,
                    `${phone.value}`.getPhoneNumber(),
                    address.value!,
                    city.value!,
                    postalCode.value!,
                    countrySelected.value!,
                    stateSelected.value!,
                    latitude.value!,
                    longitude.value!
                );

                if (result) {
                    snackBarMsg.value = t('address_created_successfully');
                } else {
                    snackBarMsg.value = result;
                }
            }

            model.value = false;
            openSnackBar.value = true;

            if (!props.canRoute) {
                props.refreshData?.();
            }
        } catch (e) {
            if (e instanceof AxiosError) {
                var message: string = e.response?.data?.message ?? e['message'] ?? e.toString()

                snackBarMsg.value = message.toString();
                openSnackBar.value = true;
            } else if (e instanceof Error) {
                snackBarMsg.value = e.message.toString();
                openSnackBar.value = true
            } else {
                snackBarMsg.value = t('oops');
                openSnackBar.value = true;
            }
        }
        finally {
            isLoading.value = false;
        }
    }
}

watch(model, (newValue) => {
    if (newValue) {
        onInit();
    } else {
        if (props.canRoute) {
            router.push({ path: RoutersHelper.address });
        }
    }
});

watch(countrySelected, () => {
    if(countrySelected.value === 96){
        postalCode.value = '000'
    }
});

</script>

<template>
    <!-- Loading -->
    <LoadingPlus v-if="isLoading" />
    <!-- Snack Bar -->
    <SnackBarPlus v-model="openSnackBar">
        {{ snackBarMsg }}
    </SnackBarPlus>

    <VDialog v-model="model" max-width="800px" scrollable persistent>
        <VCard max-width="800" min-width="300">
            <VCardTitle>
                <VRow justify="space-between">
                    <VCol>
                        <div class="address-details-title"
                            :style="{ 'font-size': '20px', 'color': ColorPick.fontColor, }">
                            {{ label === undefined || label === null || label === '' ? t('address_details') : label }}
                        </div>
                    </VCol>
                    <VSpacer />
                    <div @click="model = false" class="close-icon-btn" background="none">
                        <VIcon icon="mdi-close" :size="'26px'" :color="ColorPick.fontColor" />
                    </div>
                </VRow>
            </VCardTitle>
            <VCardText class="address-details-content">

                <VForm @submit.prevent="onSave" ref="refForm">
                    <div>
                        <div class="px-5">
                            <TextFieldPlus :title="t('label')" v-model="label" :placeholder="t('label')"
                                :rules="[ValidationHelper.required]" :style="{ color: ColorPick.fontColor }" required />
                        </div>
                        <div class="px-5">
                            <TextFieldPlus :title="t('recipient_name')" v-model="recipientName"
                                :placeholder="t('recipient_name')" :rules="[ValidationHelper.required]"
                                :style="{ color: ColorPick.fontColor }" required />
                        </div>

                        <div class="px-5">
                            <div class="address-details-item-title" :style="{ color: ColorPick.fontColor }">
                                {{ t('phone_number') }} <span class="text-required-field">*</span>
                            </div>
                            <TelInputPlus v-model="phone"
                                :style-classes="{ 'vue-tel-input--error': phoneNumberError !== undefined }" />
                            <div class="phone-error">{{ phoneNumberError }}</div>
                        </div>

                        <div class="px-5">
                            <div class="address-details-item-title" :style="{ color: ColorPick.fontColor }">
                                {{ t('address') }} <span class="text-required-field">*</span>
                            </div>
                            <VTextarea v-model="address" :placeholder="t('address')" no-resize
                                :rules="[ValidationHelper.required]" :style="{ color: ColorPick.fontColor }" />
                        </div>

                        <div class="px-5" :style="{ display: 'flex' }">
                            <div :style="{ flex: 1, }">
                                <TextFieldPlus :title="t('city')" v-model="city" :placeholder="t('city')"
                                    :rules="[ValidationHelper.required]" :style="{ color: ColorPick.fontColor, }"
                                    required />
                            </div>
                            <div :style="{ width: '10px', }"></div>
                            <div :style="{ flex: 1, }">
                                <TextFieldPlus :title="t('postal_code')" v-model="postalCode"
                                    :placeholder="t('postal_code')"
                                    :rules="[ValidationHelper.required, ValidationHelper.number]"
                                    :style="{ color: ColorPick.fontColor, }" required />
                            </div>
                        </div>
                        <div class="px-5">
                            <SelectPlus v-model="countrySelected" :items="countryLists" item-value="value" item-title="name"
                                :title="t('country')" :titleColor="ColorPick.fontColor" :placeholder="t('select_state')"
                                :rules="[ValidationHelper.required]" :style="{ color: ColorPick.fontColor }" required @onChange="stateSelected = null"/>
                        </div>
                        <div v-if="countrySelected === 129" class="px-5">
                            <SelectPlus v-model="stateSelected" :items="stateLists" item-value="value" item-title="name"
                                :title="t('state')" :titleColor="ColorPick.fontColor" :placeholder="t('select_state')"
                                :rules="[ValidationHelper.required]" :style="{ color: ColorPick.fontColor }" required />
                        </div>
                        <div v-else class="px-5">
                            <TextFieldPlus :title="t('state')" v-model="stateSelected" :placeholder="t('state')"
                                :rules="[ValidationHelper.required]" :style="{ color: ColorPick.fontColor }" required />
                        </div>

                        <div :style="{ height: '10px', }"></div>

                        <div class="px-5">
                            <div class="text-field-plus-title" :style="{ color: ColorPick.fontColor }">
                                {{ t('location_pin') }} <span class="text-required-field">*</span>
                            </div>
                            <div class="text-field-plus-text" :style="{ color: ColorPick.fontColor }">
                                <span class="text-required-field">*</span> {{ t('if_the_location_deviation') }}
                            </div>
                            <GoogleMapPicker v-if="!isLoading" v-model="addressFull"
                                :style="{ width: '100%', height: '300px', }"
                                :defaultLocation="{ lat: latitude, lng: longitude }"
                                @update:onLoading="(value) => { isMapLoading = value; }"
                                @update:location="(event) => { latitude = event.lat; longitude = event.lng; }"
                                :isEdit="props.id !== undefined && props.id !== null && props.id !== ''" />
                        </div>

                        <div :style="{ height: '10px', }"></div>

                    </div>
                </VForm>
            </VCardText>
            <VCardActions>
                <div :style="{ display: 'flex', width: '100%' }">
                    <ButtonPlus :style="{ flex: 1 }" @click="model = false" variant="outlined">
                        {{ t('cancel') }}
                    </ButtonPlus>
                    <div :style="{ width: '10px', }"></div>
                    <ButtonPlus :style="{ flex: 1 }" @click="onSave()">
                        {{ t('save') }}
                    </ButtonPlus>
                </div>

            </VCardActions>
        </VCard>
    </VDialog>
</template>

<style scoped lang="scss">
.text-field-plus-title {
    font-size: 16px;
    font-weight: 600;
    padding-block: 10px 5px;
    text-align: start;
    color: v-bind('ColorPick.fontColor');
}

.text-field-plus-text {
    font-size: 14px;
    font-weight: 400;
    padding-block: 0px 5px;
    text-align: start;
    color: v-bind('ColorPick.fontColor');
}

.address-details-title {
    width: 100%;
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    max-lines: 1;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    overflow-wrap: break-word;
}

.address-details-content {
    padding: 10px;
}

.close-icon-btn {
    padding: 10px;
    block-size: 50px;
    inline-size: 50px;
}

.address-details-item-title {
    font-size: 16px;
    font-weight: 600;
    padding-block: 10px 5px;
    text-align: start;

}

.phone-error {
    padding: 6px 16px 0;
    font-size: 12px;
    color: #ff4c51;
}

.tel-input :deep(.vue-tel-input--error) {
    border-color: #ff4c51;
}
</style>