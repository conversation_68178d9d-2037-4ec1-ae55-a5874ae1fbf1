<script setup lang="ts">
interface Props {
  disable?: boolean;
  variant?: "flat" | "text" | "elevated" | "tonal" | "outlined" | "plain" | undefined;
}

const props = defineProps<Props>()
</script>

<template>
  <VBtn :props="props" class="buttonplus" :variant="props.variant ?? 'elevated'" :disabled="props.disable">
    <slot></slot>
  </VBtn>

</template>

<style lang="scss" scoped>
.buttonplus {
  text-transform: none;
  block-size: 45px;
  font-size: 16px;
  font-weight: 400;
}
</style>
