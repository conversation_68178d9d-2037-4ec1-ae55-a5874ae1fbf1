<script setup lang="ts">
interface Props {
  statusCode?: string | number;
  title?: string;
  description?: string
}

const props = defineProps<Props>()
</script>

<template>
  <div class="text-center mb-4">
    <!-- 👉 Title and subtitle -->
    <h2 v-if="props.statusCode" class="status-code">
      {{ props.statusCode }}
    </h2>
    <h3 v-if="props.title" class="title">
      {{ props.title }}
    </h3>
    <p v-if="props.description" class="description">
      {{ props.description }}
    </p>
  </div>
</template>

<style lang="scss" scoped>
.status-code {
  display: -webkit-box;
  overflow: hidden;
  padding: 10px;
  -webkit-box-orient: vertical;
  font-size: 32px;
  font-weight: 600;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
}

.title {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  font-size: 16px;
  font-weight: 700;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
}

.description {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  font-size: 16px;
  font-weight: 400;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
}
</style>
