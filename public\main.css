body {
  margin: 0;
}

html {
  overflow: hidden scroll;
}

.logo {
  /* stylelint-disable-next-line liberty/use-logical-spec */
  max-width: 150px;
  object-fit: contain;
}

.location {
  position: absolute;
  display: grid;
  box-sizing: border-box;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  block-size: 100%;
  inline-size: 100%;
  background-color: #F7F5F3;
}

.blink {
  animation: blink 3s infinite both;
}

@keyframes blink {
  0%,
  50%,
  100% {
    opacity: 1;
  }

  25%,
  75% {
    opacity: 0;
  }
}

@keyframes blink {
  0%,
  50%,
  100% {
    opacity: 1;
  }

  25%,
  75% {
    opacity: 0;
  }
}
