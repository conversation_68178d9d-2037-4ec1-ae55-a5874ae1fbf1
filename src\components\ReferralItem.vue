<script setup lang="ts">
import ColorPick from '@/helpers/colorpick';
import { useI18n } from "vue-i18n";

const { t } = useI18n();

interface Props {
    name?: string;
    date?: string;
    status?: string;
    rewardStatus?: string;
}


const props = defineProps<Props>()
</script>

<template>
    <div class="referral-item">
        <div v-if="props.name" class="referral-item-text">
            <Span class="referral-item-title">{{ t("name") }}: </Span>
            <Span class="referral-item-data">{{ props.name }}</Span>
        </div>
        <div v-if="props.date" class="referral-item-text">
            <Span class="referral-item-title">{{ t("date") }}: </Span>
            <Span class="referral-item-data">{{ props.date }}</Span>
        </div>
        <div v-if="props.status" class="referral-item-text">
            <Span class="referral-item-title">{{ t("status") }}: </Span>
            <Span class="referral-item-data">{{ props.status }}</Span>
        </div>

        <div v-if="props.rewardStatus" class="referral-item-text">
            <Span class="referral-item-title">{{ t("reward_status") }}: </Span>
            <Span class="referral-item-data">{{ props.rewardStatus }}</Span>
        </div>

        <div style="height: 10px"></div>

    </div>
    <VDivider></VDivider>
</template>

<style lang="scss" scoped>
.referral-item {
    height: 120px;
    padding: 10px;
    text-align: left;
    align-content: center;
}

.referral-item-title {
    font-size: 14px;
    font-weight: 600;
    color: v-bind("ColorPick.greyFontColor02");
}

.referral-item-data {
    font-size: 14px;
    font-weight: 400;
    color: v-bind("ColorPick.greyFontColor07");
}

.referral-item-text{
    width: 100%;
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    color: v-bind('ColorPick.fontColor');
    max-lines: 1;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}
</style>