<script lang="ts" setup>
import upload from '@/assets/icons/upload.png';
import ColorPick from '@/helpers/colorpick';
import RandomHelper from '@/helpers/random_helper';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import ButtonPlus from './ButtonPlus.vue';

// Handle language
const { t } = useI18n();

//Color
interface Props {
    modelValue?: {
        webUrls: { url: string; name: string; }[];
        localUrls: { id: string; name: string; url: string }[];
    };
    multiple?: boolean;
    maxFiles?: number;
    errorMessage?: string;
    webUrlsDelete?: (values: { url: string }[]) => void;
    disabled?: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits(['update:modelValue']);

const webUrlsDeleteList = ref<{ url: string }[]>([]);

const localModelValue = ref(props.modelValue || { webUrls: [], localUrls: [] });

// Watch for changes to the model value
watch(() => props.modelValue, (newValue) => {
    if (newValue) {
        localModelValue.value = newValue;
    }
}, { deep: true });

watch(localModelValue, (newValue) => {
    emit('update:modelValue', newValue);
}, { deep: true });


watch(webUrlsDeleteList, (newValue) => {
    if (props.webUrlsDelete) {
        props.webUrlsDelete(newValue);
    }
}, { deep: true });


// Variables File Input
const refInputEl = ref<HTMLInputElement | null>(null);
const isDragging = ref(false);
const errorMessageSystem = ref<string | undefined>(props.errorMessage);

watch(() => props.errorMessage, (newValue) => {
    errorMessageSystem.value = newValue ?? '';
}, { immediate: true });


// Computed Properties (Get All Urls)
const allUrls = computed(() => {
    return [...localModelValue.value.webUrls ?? [], ...localModelValue.value.localUrls ?? []];
});

// Computed Properties (Check Can Add More Files)
const canAddMoreFiles = computed(() => {
    return !props.maxFiles || allUrls.value.length < props.maxFiles;
});

// Functions

// Delete File
const deleteFile = (index?: number) => {
    if (props.multiple) {
        if (index !== undefined && index !== null) {
            if (index < (localModelValue.value.webUrls?.length || 0)) {
                webUrlsDeleteList.value.push(localModelValue.value.webUrls[index]);
                localModelValue.value.webUrls?.splice(index, 1);
            } else {
                const localIndex = index - (localModelValue.value.webUrls?.length || 0);
                localModelValue.value.localUrls?.splice(localIndex, 1);
            }
        }
    } else {
        if (localModelValue.value.localUrls != null && localModelValue.value.localUrls?.length > 0) {
            localModelValue.value.localUrls = [];
        } else {
            webUrlsDeleteList.value = localModelValue.value.webUrls;
            localModelValue.value.webUrls = [];
        }
    }
    errorMessageSystem.value = '';
};

// Check if file is an image
const isNotValidImageFile = (file: File): boolean => {
    return !['image/jpeg', 'image/jpg', 'image/png'].includes(file.type);
};

//Process Files
const processFiles = (files: FileList) => {
    errorMessageSystem.value = '';

    var tempFiles = Array.from(files);

    if (tempFiles && tempFiles.length) {
        //show error message if not
        const notValidImageFiles = Array.from(tempFiles).filter(isNotValidImageFile);
        if (notValidImageFiles.length > 0) {
            errorMessageSystem.value = t('only_image_files_allowed');

            //Filter non image files
            for (var i = 0; i < notValidImageFiles.length; i++) {
                //Remove non image files
                const index = Array.from(tempFiles).indexOf(notValidImageFiles[i]);
                if (index !== -1) {
                    tempFiles.splice(index, 1);
                }
            }
        }

        if (props.multiple) {
            const remainingSlots = props.maxFiles ? props.maxFiles - allUrls.value.length : tempFiles.length;
            const filesToProcess = Math.min(remainingSlots, tempFiles.length);

            for (let i = 0; i < filesToProcess; i++) {
                localModelValue.value.localUrls?.push({
                    id: RandomHelper.makeRandomString(),
                    name: tempFiles[i].name,
                    url: URL.createObjectURL(tempFiles[i]),
                });
            }

            if (filesToProcess < tempFiles.length) {
                errorMessageSystem.value = t('max_files_limit_reached', { max: props.maxFiles });
            }
        } else {
            localModelValue.value.localUrls = [{
                id: RandomHelper.makeRandomString(),
                name: tempFiles[0].name,
                url: URL.createObjectURL(tempFiles[0]),
            }];
        }
    }
};

// Handle File Select
const onSelectFile = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const files = target.files;

    if (files) {
        if (canAddMoreFiles.value) {
            processFiles(files);
        } else {
            errorMessageSystem.value = t('max_files_limit_reached', { max: props.maxFiles });
        }
    }

    // Reset the input value to allow selecting the same file again
    if (refInputEl.value) {
        refInputEl.value.value = '';
    }
};

// Handle Drag and Drop
const onDrag = (event: DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    if (event.type === 'dragenter' || event.type === 'dragover') {
        isDragging.value = true;
    } else if (event.type === 'dragleave') {
        isDragging.value = false;
    }
};

const onDrop = (event: DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    isDragging.value = false;
    const files = event.dataTransfer?.files;
    if (files) {
        if (canAddMoreFiles.value) {
            processFiles(files);
        } else {
            errorMessageSystem.value = t('max_files_limit_reached', { max: props.maxFiles });
        }
    }
};

//Viewer Options
const viewerOptions = {
    zIndex: 9999,
    inline: false,
    button: true,
    navbar: true,
    title: false,
    tooltip: false,
    movable: true,
    zoomable: true,
    rotatable: false,
    scalable: false,
    transition: true,
    fullscreen: false,
    keyboard: false,
};
</script>

<template>
    <!-- File input area -->
    <div v-if="!disabled" class="upload-plus-border-radius" :class="{ 'dragging': isDragging }" @dragenter="onDrag"
        @dragover="onDrag" @dragleave="onDrag" @drop="onDrop">
        <div class="upload-plus-border-input" @click="refInputEl?.click()">
            <VImg :src="upload" :cover="false" height="40" width="40" />
            <div style="padding: 15px;">
                <span class="upload-plus-border-input-text">{{ t('drag_your_files_to_start_uploading') }}</span>
            </div>
            <div class="d-flex" :style="{ alignItems: 'center', padding: '0 20px', }">
                <VDivider></VDivider>
                <span class="px-2">{{ t('or') }} </span>
                <VDivider></VDivider>
            </div>
            <div style="padding: 15px;">
                <ButtonPlus variant="outlined" style="font-size: 14px">
                    {{ t('browse_files') }}
                </ButtonPlus>
            </div>
        </div>
    </div>

    <div v-else-if="localModelValue.localUrls.length === 0 && localModelValue.webUrls.length === 0">
        <span class="no-image">{{ t('no_image') }}</span>
    </div>

    <div v-if="errorMessage || errorMessageSystem" class="error-message mt-2">{{ errorMessage ?? errorMessageSystem }}
    </div>

    <div class="d-flex flex-wrap">
        <!-- Multiple Files -->

        <viewer v-if="multiple" :options="viewerOptions" :images="allUrls.map(item => item.url)"
            class="upload-plus-image-preview-viewer-container">
            <div v-for="(item, index) in allUrls" :key="item.url" class="upload-plus-image-preview-container">
                <img class="upload-plus-image-preview-img" height="150" width="150" :src="item.url" :alt="item['name']"
                    :key="item.url" />
                <div class="upload-plus-image-delete-button" @click="deleteFile(index)">
                    <VIcon v-if="!disabled" class="ri-delete-bin-6-line" size="x-large" />
                </div>
            </div>
        </viewer>

        <!-- Single File -->
        <viewer v-else-if="localModelValue.localUrls?.length > 0 || localModelValue.webUrls?.length > 0"
            class="upload-plus-image-preview-viewer-container" :options="viewerOptions"
            :images="localModelValue.localUrls?.length > 0 ? [localModelValue.localUrls[0].url] : [localModelValue.webUrls[0].url]">
            <div class="upload-plus-image-preview-container">
                <img class="upload-plus-image-preview-img" height="150" width="150"
                    v-if="localModelValue.localUrls?.length > 0" :src="localModelValue.localUrls[0].url"
                    :alt="localModelValue.localUrls[0].name" :key="localModelValue.localUrls[0].url" />
                <img class="upload-plus-image-preview-img" height="150" width="150"
                    v-else-if="localModelValue.webUrls?.length > 0" :src="localModelValue.webUrls[0].url"
                    :alt="localModelValue.webUrls[0]['name']" :key="localModelValue.webUrls[0].url" />
                <div class="upload-plus-image-delete-button" @click="deleteFile()">
                    <VIcon v-if="!disabled" class="ri-delete-bin-6-line" size="x-large" />
                </div>
            </div>

        </viewer>



    </div>

    <input v-if="!disabled" ref="refInputEl" type="file" name="file"
        accept="image/jpg,image/jpeg,image/png,image/svg,image/svg+xml" @change="onSelectFile" :multiple="multiple"
        hidden>
</template>

<style lang="scss" scoped>
.upload-plus-image-preview-viewer-container {
    display: contents !important;
}

.upload-plus-image-preview-container {
    position: relative;
    padding: 0;
    margin: 12px;
    block-size: 150px;
    inline-size: 150px;
}

.upload-plus-image-preview-img {
    border-radius: 10px;
    cursor: pointer;
    object-fit: cover;
}

.upload-plus-image-delete-button {
    position: absolute;
    color: v-bind("ColorPick.primaryColor");
    cursor: pointer;
    inset-block-start: 10px;
    inset-inline-end: 10px;
}

.upload-plus-border-radius {
    border-width: 2px;
    border-style: dashed;
    border-color: rgba(var(--v-theme-grey-300));
    border-radius: 10px;
    inline-size: 100%;
    transition: border-color 0.3s ease;

    &.dragging {
        border-color: rgba(var(--v-theme-primary));
    }
}

.upload-plus-border-input {
    cursor: pointer;
    padding: 20px;
    text-align: -webkit-center;
}

.upload-plus-border-input-text {
    font-size: 14px;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: v-bind("ColorPick.fontColor");
    text-align: center;
}

.error-message {
    color: v-bind("ColorPick.errorColor");
}

.no-image {
    font-size: 16px;
    font-weight: 600;
    padding-block: 0;
}
</style>
