<script setup lang="ts">
// Components
import TextFieldPlus from "@/components/custom_control/TextFieldPlus.vue";
import AddressList from "@/components/address/AddressList.vue";

// Helpers
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import { ref, onMounted, watch, nextTick } from "vue";
import RoutersHelper from "@/helpers/routes_helper";
import AppConfig from "@/appconfig";
import ColorPick from "@/helpers/colorpick";
import moment from "moment";
import CapitalizeHelper from "@/helpers/capitalize_helper";

// Apis
import CartsApi from "@/services/carts_api";
import GeneralPageApi from "@/services/general_page_api";
import RewardApi from "@/services/reward_api";

// Dtos
import { PaginationDto } from "@/dto/pagination.dto";
import { RedeemedRewardsDto } from "@/dto/rewards.dto";
import type { PaymentMethodDto } from "@/dto/paymentMethod.dto";
import type { CartSummaryDto } from "@/dto/cart_summary.dto";

// Images
import placeholder from "@/assets/icons/logo.png";
import locationIcon from "@/assets/icons/location.png";

import localStorageHelper from "@/helpers/localstorage_helper";
import OrderTypeEnum from "@/enums/OrderTypeEnum";
import type { CouponDto, CreateCouponDto } from "@/dto/coupon.dto";

// Variables
const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// General Variable
const isLoading = ref<boolean>(false);
const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");
const openApiErrorDialog = ref<boolean>(false);
const apiErrorDialogMsg = ref<string>("");
const openDeleteDialog = ref<boolean>(false);
const paymentMethod = ref<PaymentMethodDto[]>([]);
const paymentMethodSelected = ref<string>();
const openPaymentSheet = ref<boolean>(false);
const openLocationDialog = ref<boolean>(false);

const openVoucherSnackBar = ref<boolean>(false);
const voucherSnackBarMsg = ref<string>("");

const openCouponSnackBar = ref<boolean>(false);
const couponSnackBarMsg = ref<string>("");

const openCartSummaryErrorDialog = ref<boolean>(false);
const cartSummaryErrorDialogMsg = ref<string>("");

// Cart Order Summary
const data = ref<CartSummaryDto>();
const productItemDeleteId = ref<number>();

const promoCode = ref<string>("");

// Reward (Voucher)
// My Reward Pagination
const myRewardPage = ref(1);
const myRewardhasMore = ref(true);
let myRewardDoneCopy: (status: "error" | "loading" | "empty" | "ok") => void;
const myRewardsListKey = ref(0);
const myInfiniteScrollRef = ref();

const myRewardList = ref<RedeemedRewardsDto[]>([]);

const openVoucherSheet = ref<boolean>(false);
const selectedReward = ref<RedeemedRewardsDto>();
const rewardToDelete = ref<RedeemedRewardsDto>();
const isRewardStackable = ref<number | undefined>();
const openDeleteVoucherDialog = ref<boolean>(false);

const couponToDelete = ref<CouponDto>();
const openDeleteCouponDialog = ref<boolean>(false);

const applyPromoCodeSnackBar = ref<boolean>(false);
const applyPromoCodeSnackBarMsg = ref<string>("");

const remarks = ref<string>("");

const selectedAddressId = ref<string>("0");

// Open Address List
function toLocation() {
  openLocationDialog.value = true;
}

// User selected voucher Item
const selectVoucherItem = (reward: RedeemedRewardsDto | undefined) => {
  selectedReward.value =
    selectedReward.value?.id === reward?.id ? undefined : reward;
};

// Open Voucher Sheet
function selectOpenVoucherSheet() {
  if (isRewardStackable.value === 1 || isRewardStackable.value === undefined) {
    // Call api to get voucher
    myRewardPage.value = 1;
    myRewardList.value = [];
    myRewardhasMore.value = true;
    myRewardsListKey.value += 1;

    nextTick(() => {
      if (
        myInfiniteScrollRef.value &&
        typeof myInfiniteScrollRef.value.reset === "function"
      ) {
        myInfiniteScrollRef.value.reset();
      }
    });

    // Open voucher sheet
    openVoucherSheet.value = true;
  }
}

// Close Vouhcer Sheet
function closeVoucherSheet() {
  selectedReward.value = undefined; // Reset selected item when closing
  openVoucherSheet.value = false;
}

function selectPaymentMethod() {
  // Call api to get payment method

  // Open payment method sheet

  if (localStorageHelper.getOrderType() === OrderTypeEnum.delivery) {
    if (data.value?.address) {
      getPaymentMethod();
    } else {
      snackBarMsg.value = t("please_select_address");
      openSnackBar.value = true;
    }
  } else {
    getPaymentMethod();
  }
}

function proceedPayment() {
  // Close payment method sheet
  openPaymentSheet.value = false;

  if (paymentMethodSelected !== undefined && paymentMethod.value.length > 0) {
    getPaymentLink();
    // router.push(RoutersHelper.orders);
  } else {
    apiErrorDialogMsg.value = t("payment_method_not_selected");
    openApiErrorDialog.value = true;
  }
}

// Get Cart Summary
async function getCartSummary() {
  try {
    isLoading.value = true;

    var outletId = localStorageHelper.getOutletId();

    if (localStorageHelper.getOrderType() === OrderTypeEnum.pickup) {
      selectedAddressId.value = "0";
      await CartsApi.updateAddress(parseInt(selectedAddressId.value));
    } else {
    }

    data.value = await CartsApi.summary(
      parseInt(outletId ?? ""),
      data.value?.address !== null
        ? data.value?.address?.id
        : parseInt(selectedAddressId.value ?? "")
    );

    if (data.value.vouchers.length > 0) {
      isRewardStackable.value =
        data.value.vouchers[0].voucher_data!.is_stackable === true ? 1 : 0;

      // getVoucherList();
    } else {
      isRewardStackable.value = undefined;
    }

    // Erro Msg
    var msg: string = "";

    // Payment Failed Msg
    if (data.value.show_msg) {
      if (msg !== "") {
        msg += "\n";
      }
      msg += t("payment_failed");
    }
    // Payment Failed Msg

    // Product Removed Msg
    if (data.value.removed_products.length > 0) {
      var productMsg: string = "";

      for (var i = 0; i < data.value.removed_products.length; i++) {
        if (productMsg !== "") {
          if (i >= data.value.removed_products.length - 1) {
            productMsg += ` ${t("and")} `;
          } else {
            productMsg += ", ";
          }
        }

        productMsg += data.value.removed_products[i];
      }

      if (msg !== "") {
        msg += "\n";
      }
      msg += t("item_removed_from_platform").replace("[product]", productMsg);
    }
    // Product Removed Msg

    // Voucher Removed Msg
    if (data.value.removed_vouchers.length > 0) {
      var voucherMsg: string = "";

      for (var i = 0; i < data.value.removed_vouchers.length; i++) {
        if (voucherMsg !== "") {
          if (i >= data.value.removed_vouchers.length - 1) {
            voucherMsg += ` ${t("and")} `;
          } else {
            voucherMsg += ", ";
          }
        }

        voucherMsg += data.value.removed_vouchers[i];
      }

      if (msg !== "") {
        msg += "\n";
      }

      msg += t("voucher_removed_from_platform").replace(
        "[voucher]",
        voucherMsg
      );
    }
    // Voucher Removed Msg

    if (msg !== "") {
      apiErrorDialogMsg.value = msg;
      openApiErrorDialog.value = true;
    }
  } catch (e) {
    data.value = undefined;
    cartSummaryErrorDialogMsg.value = `${e}`;
    openCartSummaryErrorDialog.value = true;
    // getCartSummary();
  } finally {
    isLoading.value = false;
  }
}

// Update Cart Address
async function updateAddress(id: number) {
  try {
    isLoading.value = true;

    await CartsApi.updateAddress(id);

    openLocationDialog.value = false;

    getCartSummary();
  } catch (error) {
    apiErrorDialogMsg.value = `${error}`;
    openApiErrorDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}

// Open Delete Dialog
function promptDeleteProduct(id: number) {
  productItemDeleteId.value = id;
  openDeleteDialog.value = true;
}

// Confirm delete product
const confirmDeleteProduct = async () => {
  if (productItemDeleteId.value !== 0) {
    await deleteProduct(productItemDeleteId.value!);
  }
  openDeleteDialog.value = false;
};

// Confirm Apply Voucher

// Check is Stackable
const checkStackable = () => {
  return isRewardStackable.value === 1 || isRewardStackable.value === undefined;
};

// Open delete voucher dialog
const promptDeleteVoucher = (voucher: RedeemedRewardsDto | undefined) => {
  rewardToDelete.value = voucher;
  openDeleteVoucherDialog.value = true;
};

// Confirm delete voucher
const confirmDeleteVoucher = async () => {
  if (rewardToDelete.value) {
    await removeVoucherFunc(rewardToDelete.value);
  }
  openDeleteVoucherDialog.value = false;
};

// Remove Voucher
async function removeVoucherFunc(voucher: RedeemedRewardsDto) {
  try {
    isLoading.value = true;

    // Remove voucher
    await CartsApi.removeVoucher(voucher.id.toString());
    await getCartSummary();
    openVoucherSnackBar.value = true;
    voucherSnackBarMsg.value = t("voucher_removed_successfully");
  } catch (error) {
    openApiErrorDialog.value = true;
    apiErrorDialogMsg.value = `${error}`;
  } finally {
    isLoading.value = false;
  }
}

async function confirmApplyVoucher() {
  try {
    isLoading.value = true;

    if (selectedReward.value) {
      // Apply voucher
      await CartsApi.applyVoucher(
        selectedReward.value.id.toString(),
        data.value?.payment_details.subtotal.toString() ?? "0.00"
      );

      await getCartSummary();
      openVoucherSheet.value = false;
      selectedReward.value = undefined;
      openSnackBar.value = true;
      snackBarMsg.value = t("voucher_applied_successfully");
    }
  } catch (error) {
    openApiErrorDialog.value = true;
    apiErrorDialogMsg.value = `${error}`;
  } finally {
    isLoading.value = false;
  }
}

// Promo Code
async function applyPromoCodeSnackBarFunc() {
  try {
    isLoading.value = true;

    if (promoCode.value) {
      // Apply coupon
      const result = await CartsApi.applyCoupon(
        promoCode.value,
        data.value?.payment_details.subtotal.toString() ?? "0.00"
      );

      await getCartSummary();

      applyPromoCodeSnackBarMsg.value = t("promo_code_applied_successfully");
      applyPromoCodeSnackBar.value = true;
      promoCode.value = "";
    }
  } catch (error) {
    openApiErrorDialog.value = true;
    apiErrorDialogMsg.value = `${error}`;
  } finally {
    isLoading.value = false;
  }
}

// Open delete Coupon dialog
const promptDeleteCoupon = (coupon: CouponDto | undefined) => {
  couponToDelete.value = coupon;
  openDeleteCouponDialog.value = true;
};

// Confirm delete Coupon
const confirmDeleteCoupon = async () => {
  if (couponToDelete.value) {
    await removeCouponFunc(couponToDelete.value);
  }
  openDeleteCouponDialog.value = false;
};

// Remove Coupon
async function removeCouponFunc(coupon: CouponDto) {
  try {
    isLoading.value = true;

    // Remove Coupon
    await CartsApi.removeCoupon(coupon.id.toString());
    await getCartSummary();
    openCouponSnackBar.value = true;
    couponSnackBarMsg.value = t("coupon_removed_successfully");
  } catch (error) {
    openApiErrorDialog.value = true;
    apiErrorDialogMsg.value = `${error}`;
  } finally {
    isLoading.value = false;
  }
}

// Get Voucher List
// Fetch My Rewards List
async function loadRedeemedVoucher() {
  const response = await RewardApi.redeemedList(
    myRewardPage.value,
    undefined,
    1
  );
  return response;
}

// My Rewards - Infinite Scroll
async function myRewardInfiniteload({
  done,
}: {
  done: (status: "error" | "loading" | "empty" | "ok") => void;
}) {
  if (!myRewardhasMore.value) {
    done("empty");
    return;
  }

  try {
    const res = await loadRedeemedVoucher();

    if (res.data !== undefined) {
      const newData = res.data ?? [];
      const existingIds = new Set(myRewardList.value.map((item) => item.id));
      const filteredData = newData.filter(
        (item: any) => !existingIds.has(item.id)
      );

      myRewardList.value.push(...filteredData);

      const lastPage = res.last_page ?? 1;

      if (myRewardPage.value >= lastPage) {
        myRewardhasMore.value = false;

        // For refresh the page, when change the tab
        myRewardDoneCopy = done;
      } else {
        myRewardhasMore.value = true;

        myRewardPage.value += 1;
      }
    } else {
      myRewardhasMore.value = false;
      done("empty");
      myRewardDoneCopy = done;
    }
  } catch (e) {
  } finally {
    done("ok");
  }
}

//Get payment method
async function getPaymentMethod() {
  try {
    isLoading.value = true;

    paymentMethod.value = await GeneralPageApi.getPaymentMethods();

    if (paymentMethod.value.length > 0) {
      paymentMethodSelected.value = paymentMethod.value[0].id;
    }

    openPaymentSheet.value = true;
  } catch (error) {
    apiErrorDialogMsg.value = `${error}`;
    openApiErrorDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}

//To payment page
async function getPaymentLink() {
  try {
    isLoading.value = true;

    if (paymentMethodSelected?.value) {
      let paymentUrl: string = await CartsApi.orderCreate(
        paymentMethodSelected.value?.toString(),
        remarks.value,
        localStorageHelper.getOrderType()?.toString(),
        parseInt(localStorageHelper.getOutletId() ?? "0")
      );
      window.location.href = paymentUrl;
    } else {
      throw t("payment_method_not_selected");
    }
  } catch (error) {
    apiErrorDialogMsg.value = `${error}`;
    openApiErrorDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}

// Delete Product
async function deleteProduct(id: number) {
  try {
    isLoading.value = true;

    await CartsApi.deleteProduct(id ?? 0);

    openSnackBar.value = true;
    snackBarMsg.value = t("item_removed_from_cart");

    // If product remove, voucher will auto remove
    if ((data.value?.vouchers?.length ?? 0) > 0) {
      data.value!.vouchers.forEach(async (item: RedeemedRewardsDto) => {
        await removeVoucherFunc(item);
      });
    }

    if (data.value?.coupon !== null) {
      removeCouponFunc(data.value?.coupon!);
    }

    getCartSummary();
  } catch (error) {
    apiErrorDialogMsg.value = `${error}`;
    openApiErrorDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}

onMounted(() => {
  window.scrollTo(0, 0);

  // Temporary Change to Delivery (for those who have selected order type pickup)
  if (localStorageHelper.getOrderType() === OrderTypeEnum.pickup) {
    localStorageHelper.setOrderType(OrderTypeEnum.delivery);
  }

  router.push(RoutersHelper.orderConfirmation);

  getCartSummary();
  // loadRedeemedVoucher();
  // getVoucherList();
});
</script>

<template>
  <LoadingPlus v-if="isLoading" />

  <AppBarPlus :title="t('order_confirmation')" :back="true" />

  <!-- AppBarPlus Spacing -->
  <div style="height: 60px" />

  <div v-if="data && data?.products.normal.length > 0" class="order">
    <div class="order-container">
      <!-- Location -->
      <div
        v-if="localStorageHelper.getOrderType() === OrderTypeEnum.delivery"
        class="order-location pointer"
        @click="toLocation()"
      >
        <VImg :src="locationIcon" height="18" width="18" :inline="true"></VImg>
        <!-- <VIcon size="26">mdi-map-marker-outline</VIcon> -->
        <div style="width: 15px" />
        <div class="location-detail">
          <div class="location-title">
            {{
              data?.address !== null
                ? data?.address?.label
                : t("select_address")
            }}
          </div>
          <div v-if="data?.address !== null" style="height: 5px" />
          <p v-if="data?.address !== null" class="full-location">
            {{
              [
                data?.address?.address ?? "",
                `${data?.address?.postcode ?? ""}
                        ${data?.address?.city ?? ""}`,
                CapitalizeHelper.capitalize(data?.address?.state ?? ""),
              ].join(", ")
            }}
          </p>
        </div>
        <VSpacer />
        <VIcon size="18">mdi-chevron-right</VIcon>
      </div>

      <div style="height: 5px" />

      <!-- Your Order -->
      <div class="order-details">
        <!-- Cart Item -->
        <div class="order-details-title">{{ t("your_order_cap") }}</div>

        <div style="height: 10px" />

        <!-- Product list -->

        <div class="product" v-for="product in data?.products.normal">
          <VImg class="product-image" :src="product.image ?? placeholder" />

          <div class="product-details">
            <div class="flex">
              <h3 class="product-title">{{ product.name }}</h3>
              <VSpacer />
              <div style="width: 10px" />

              <div
                v-if="!product.is_free"
                class="product-delete pointer"
                @click="promptDeleteProduct(product.cart_product_id)"
              >
                {{ t("delete") }}
              </div>
            </div>
            <div
              :style="{
                height: product.unit_weight !== null ? '10px' : '15px',
              }"
            />
            <div v-if="product.unit_weight !== null" class="product-desc">
              {{ t("weight") }}: {{ product.unit_weight }}
            </div>
            <div
              :style="{
                height: product.unit_weight !== null ? '15px' : '30px',
              }"
            />
            <div class="flex">
              <div
                class="product-price"
                :style="{
                  fontSize: '14px',
                }"
              >
                {{ AppConfig.currencyMark }}
                {{ product.total_price ?? "0.00" }}
              </div>
              <VSpacer />
              <div style="width: 10px" />
              <div class="product-count">x{{ product.quantity }}</div>
            </div>
          </div>
        </div>
        <div style="height: 5px" />
        <!-- Product list -->
      </div>

      <div style="height: 5px" />

      <!-- Voucher -->
      <div class="voucher">
        <div class="flex pointer" @click="selectOpenVoucherSheet()">
          <div class="voucher-title">{{ t("vouchers_cap") }} *</div>
          <VSpacer />
          <div v-if="checkStackable()" class="selected-voucher-btn">
            {{ t("select") }}
          </div>
          <VIcon v-if="checkStackable()" size="18">mdi-chevron-right</VIcon>
        </div>

        <div style="height: 10px" />

        <!-- Show Voucher -->
        <div
          v-if="data?.vouchers.length ?? 0 > 0"
          v-for="voucher in data?.vouchers"
          class="selected-voucher"
        >
          <div class="selected-voucher-image">
            <VImg
              :src="voucher.voucher_data?.image_url ?? placeholder"
              :cover="true"
              rounded="lg"
              height="100%"
            />
          </div>

          <div style="width: 20px" />

          <div class="selected-voucher-details">
            <h3 class="selected-voucher-details-title">
              {{ voucher?.voucher_data?.name }}
            </h3>
            <div style="height: 5px" />

            <div class="selected-voucher-details-desc">
              {{ voucher?.voucher_data?.description }}
            </div>

            <div style="height: 20px" />

            <VSpacer />
            <div class="selected-voucher-details-exp">
              {{ t("expired_on") }}
              {{ moment(voucher?.effective_end_date).format("D MMMM YYYY") }}
            </div>
          </div>

          <!-- Remove Button -->
          <VIcon
            class="remove-icon"
            size="20"
            @click="promptDeleteVoucher(voucher)"
            >mdi-delete</VIcon
          >
        </div>
        <!-- Show Voucher -->

        <div style="height: 30px" />

        <div class="add-promo-code">{{ t("add_promo_code") }}</div>
        <div class="d-flex align-end">
          <TextFieldPlus
            v-model="promoCode"
            :placeholder="t('enter_code')"
            variant="underlined"
            class="flex-grow-1"
          />

          <div style="width: 30px" />

          <VBtn
            variant="flat"
            height="20"
            width="80"
            :loading="isLoading"
            :disabled="isLoading"
            rounded="xl"
            class="btn-apply"
            style="font-size: 11px"
            @click="applyPromoCodeSnackBarFunc()"
          >
            {{ t("apply") }}
          </VBtn>
        </div>

        <div style="height: 20px" />

        <div
          v-if="data?.coupon !== null"
          class="coupon-container"
          style="font-size: 13px"
        >
          <VIcon size="15" class="">mdi-tag-outline</VIcon>

          <div style="width: 10px" />

          <div class="flex-grow-1 text-subtitle-2 font-weight-medium">
            {{ data?.coupon?.name ?? "" }}
          </div>

          <!-- Remove Button -->
          <VIcon
            class="remove-icon"
            size="15"
            @click="promptDeleteCoupon(data!.coupon!)"
            >mdi-delete</VIcon
          >
        </div>
      </div>
      <!-- Voucher -->

      <!-- SubTotal -->
      <div class="subtotal-container">
        <div class="subtotal-title">{{ t("subtotal") }}</div>
        <VSpacer />
        <div class="subtotal-price">
          {{ AppConfig.currencyMark }} {{ data?.payment_details.subtotal }}
        </div>
      </div>
      <!-- SubTotal -->

      <!-- Remarks -->
      <div class="remarks">
        <div class="remarks-title">{{ t("special_request_cap") }}</div>
        <div style="height: 10px" />
        <VTextarea
          v-model="remarks"
          :placeholder="t('ask_your_request')"
          rows="2"
          no-resize
          class="remarks-textarea"
          rounded="0"
          variant="plain"
        />
      </div>
      <!-- Remarks -->

      <div style="height: 5px" />

      <!-- Payment details -->
      <div class="payment-details">
        <div class="payment-details-title">{{ t("payment_details_cap") }}</div>

        <div style="height: 20px" />

        <!-- Amount -->
        <div class="flex">
          <div class="payment-details-item">{{ t("amount") }}:</div>
          <VSpacer />
          <div class="payment-details-price">
            {{ AppConfig.currencyMark }} {{ data?.payment_details.subtotal }}
          </div>
        </div>

        <div style="height: 5px" />

        <!-- Subtotal -->
        <div class="flex">
          <div class="payment-details-item">{{ t("subtotal") }}:</div>
          <VSpacer />
          <div class="payment-details-price">
            {{ AppConfig.currencyMark }} {{ data?.payment_details.subtotal }}
          </div>
        </div>

        <div style="height: 5px" />

        <!-- Delivery Fee -->
        <div v-if="data?.payment_details.shipping_fee" class="flex">
          <div class="payment-details-item">{{ t("delivery_fee") }}:</div>
          <VSpacer />
          <div class="payment-details-price">
            {{ AppConfig.currencyMark }} {{ data.payment_details.shipping_fee }}
          </div>
        </div>

        <div style="height: 5px" />

        <!-- Delivery Discount -->
        <div
          v-if="
            Number(data?.payment_details.delivery_discount) > 0 ||
            String(data?.payment_details.delivery_discount).toLowerCase() ===
              'free'
          "
          class="flex"
        >
          <div class="payment-details-item">{{ t("delivery_discount") }}:</div>
          <VSpacer />
          <div class="payment-details-price">
            - {{ AppConfig.currencyMark }}
            {{
              String(data?.payment_details.delivery_discount).toLowerCase() ===
              "free"
                ? data.payment_details.shipping_fee
                : data.payment_details.delivery_discount
            }}
          </div>
        </div>

        <div
          v-if="
            Number(data?.payment_details.delivery_discount) > 0 ||
            String(data?.payment_details.delivery_discount).toLowerCase() ===
              'free'
          "
          style="height: 5px"
        />

        <!-- Voucher Discount Value -->
        <div
          v-if="Number(data?.payment_details.coupon_discount) > 0"
          class="flex"
        >
          <div class="payment-details-item">{{ t("coupon_discount") }}:</div>
          <VSpacer />
          <div class="payment-details-price">
            - {{ AppConfig.currencyMark }}
            {{ data?.payment_details.coupon_discount }}
          </div>
        </div>

        <div
          v-if="Number(data?.payment_details.coupon_discount) > 0"
          style="height: 5px"
        />

        <div
          v-if="Number(data?.payment_details.voucher_discount) > 0"
          class="flex"
        >
          <div class="payment-details-item">{{ t("voucher_discount") }}:</div>
          <VSpacer />
          <div class="payment-details-price">
            - {{ AppConfig.currencyMark }}
            {{ data?.payment_details.voucher_discount }}
          </div>
        </div>

        <div
          v-if="Number(data?.payment_details.voucher_discount) > 0"
          style="height: 5px"
        />

        <!-- Tax -->
        <div class="flex">
          <div class="payment-details-item">{{ t("tax") }}:</div>
          <VSpacer />
          <div class="payment-details-price">
            {{ AppConfig.currencyMark }} {{ data?.payment_details.tax }}
          </div>
        </div>

        <div style="height: 10px" />
        <VDivider opacity="0.6" color="#201747" />
        <div style="height: 10px" />

        <!-- Grand Total -->
        <div class="flex">
          <div class="payment-details-grand-item">{{ t("grand_total") }}:</div>
          <VSpacer />
          <div class="payment-details-grand-price">
            {{ AppConfig.currencyMark }} {{ data?.payment_details.grand_total }}
          </div>
        </div>

        <div style="height: 5px" />

        <!-- Earn Points -->
        <div class="flex">
          <div style="font-size: 11px; font-weight: 400; color: '#201747'">
            {{ t("points_earned") }}
          </div>
          <VSpacer />
          <div class="payment-details-price">
            {{ data?.payment_details.points_earned }} {{ t("pts") }}
          </div>
        </div>
      </div>
      <!-- Payment details -->

      <!-- Check out -->
      <div style="height: 10px" />
      <VDivider />
      <div style="height: 10px" />

      <div class="order-action d-flex align-center justify-space-between">
        <div class="d-flex align-center gap-2">
          <div class="total">{{ t("total_cap") }}</div>
          <div style="width: 20px" />
          <div class="total-price">
            {{ AppConfig.currencyMark }} {{ data?.payment_details.grand_total }}
          </div>
        </div>

        <ButtonPlus
          style="font-size: 16px; font-weight: 400; height: 34px"
          rounded="xl"
          class="order-btn"
          @click="selectPaymentMethod()"
        >
          {{ t("order_now") }}
        </ButtonPlus>
      </div>
      <!-- Check out -->
    </div>
  </div>

  <!-- Empty Cart -->
  <div v-else class="empty-cart">
    <VIcon size="48">mdi-cart-outline</VIcon>
    <div style="height: 20px" />
    <p>{{ t("your_cart_is_empty") }}</p>
  </div>

  <!-- Choose Address Dialog -->
  <VDialog v-model="openLocationDialog" max-width="800px" scrollable>
    <VCard max-width="800" min-width="300">
      <VCardTitle>
        <VRow justify="space-between">
          <VCol>
            <div
              class="address-dialog-title"
              :style="{ 'font-size': '20px', color: ColorPick.fontColor }"
            >
              <span style="margin-left: 5px">{{ t("select_address") }}</span>
            </div>
          </VCol>
          <VSpacer />
          <div
            @click="openLocationDialog = false"
            class="close-icon-btn"
            background="none"
          >
            <VIcon
              icon="mdi-close"
              :size="'26px'"
              :color="ColorPick.fontColor"
            />
          </div>
        </VRow>
      </VCardTitle>
      <VCardText>
        <AddressList
          :canSelect="true"
          :canRoute="false"
          :onSelect="
            (id) => {
              selectedAddressId = id;
              updateAddress(parseInt(id));
            }
          "
        />
      </VCardText>
    </VCard>
  </VDialog>

  <!-- Voucher Bottomsheet -->
  <BottomSheetPlus
    v-model="openVoucherSheet"
    :title="t('vouchers')"
    :confirm-text="t('confirm')"
    :onClickConfirm="confirmApplyVoucher"
    :onClickClose="closeVoucherSheet"
    :maxHeight="500"
  >
    <div class="voucher-sheet-title" :style="{ color: ColorPick.fontColor }">
      {{ t("select_voucher") }}
    </div>

    <!-- Voucher List -->
    <v-container style="padding: 0px">
      <VInfiniteScroll
        ref="myInfiniteScrollRef"
        :key="myRewardsListKey"
        :height="'100%'"
        :items="myRewardList"
        @load="myRewardInfiniteload"
        v-model="myRewardhasMore"
        :empty-text="
          myRewardList?.length ?? 0 > 0 ? t('no_more') : t('no_data_found')
        "
      >
        <div
          v-for="reward in myRewardList"
          :key="reward.id"
          class="selected-voucher"
          :class="{ selected: selectedReward?.id === reward.id }"
          @click="
            () => {
              selectVoucherItem(reward);
            }
          "
        >
          <div class="selected-voucher-image">
            <VImg
              :src="reward?.voucher_data?.image ?? placeholder"
              :cover="true"
              rounded="lg"
              height="100%"
            />
          </div>

          <div style="width: 20px" />

          <div class="selected-voucher-details">
            <h3
              class="selected-voucher-details-title"
              :style="{ color: ColorPick.fontColor }"
            >
              {{ reward?.voucher_data?.name }}
            </h3>

            <div style="height: 5px" />

            <div class="selected-voucher-details-desc">
              {{ reward?.voucher_data?.description }}
            </div>
            <div style="height: 20px" />
            <div
              class="selected-voucher-details-exp"
              :style="{ color: `${'ColorPick.voucherExpireColor'}` }"
            >
              {{ t("expired_on") }}
              {{ moment(reward?.effective_end_date).format("D MMMM YYYY") }}
            </div>
          </div>
        </div>
      </VInfiniteScroll>
    </v-container>

    <!-- <NoData v-else="!isLoading" /> -->
  </BottomSheetPlus>
  <!-- Payment Method Bottomsheet -->

  <!-- Payment Method Bottomsheet -->
  <BottomSheetPlus
    v-model="openPaymentSheet"
    :title="
      paymentMethod?.length > 0
        ? t('select_payment_method')
        : t('order_confirmation')
    "
    :confirm-text="t('proceed_to_payment')"
    :onClickConfirm="proceedPayment"
  >
    <!-- Payment method -->
    <div
      v-if="paymentMethod?.length > 0"
      class="payment-sheet-title"
      :style="{ color: ColorPick.fontColor }"
    >
      {{ t("payment_methods") }}
    </div>

    <div v-if="paymentMethod?.length > 0" style="height: 10px" />

    <VRadioGroup
      v-if="paymentMethod?.length > 0"
      v-model="paymentMethodSelected"
    >
      <VRadio
        v-for="item in paymentMethod"
        :key="item.id"
        :value="item.id"
        :label="item.label"
        :style="{ color: ColorPick.fontColor }"
      />
    </VRadioGroup>

    <div v-if="paymentMethod?.length > 0" style="height: 10px" />
    <VDivider v-if="paymentMethod?.length > 0" />
    <div v-if="paymentMethod?.length > 0" style="height: 10px" />

    <div class="flex">
      <div class="total">{{ t("total") }}</div>
      <VSpacer />
      <div class="total-price" :style="{ color: ColorPick.fontColor }">
        {{ AppConfig.currencyMark }} {{ data?.payment_details.grand_total }}
      </div>
    </div>
  </BottomSheetPlus>
  <!-- Payment Method Bottomsheet -->

  <!-- Error Dialog -->
  <DialogPlus
    persistent
    v-model="openCartSummaryErrorDialog"
    :onClickConfirm="
      () => {
        openCartSummaryErrorDialog = false;
        router.back();
      }
    "
  >
    {{ cartSummaryErrorDialogMsg }}
  </DialogPlus>
  <!-- Error Dialog -->

  <!-- Error Dialog -->
  <DialogPlus
    persistent
    v-model="openApiErrorDialog"
    :onClickConfirm="
      () => {
        openApiErrorDialog = false;
      }
    "
  >
    {{ apiErrorDialogMsg }}
  </DialogPlus>
  <!-- Error Dialog -->

  <!-- Delete Product Snackbar -->
  <SnackBarPlus v-model="openSnackBar"> {{ snackBarMsg }} </SnackBarPlus>

  <!-- Delete Product Dialog -->
  <DialogPlus
    v-model="openDeleteDialog"
    :title="t('product')"
    :confirmText="t('yes')"
    :cancelText="t('no')"
    :onClickConfirm="confirmDeleteProduct"
    :onClickCancel="
      () => {
        openDeleteDialog = false;
      }
    "
  >
    {{ t("are_you_sure_you_want_to_delete") }}
  </DialogPlus>

  <!-- Delete Voucher Snackbar -->
  <SnackBarPlus v-model="openVoucherSnackBar">
    {{ voucherSnackBarMsg }}
  </SnackBarPlus>

  <!-- Delete Voucher Dialog -->
  <DialogPlus
    v-model="openDeleteVoucherDialog"
    :title="t('rewards')"
    :confirmText="t('yes')"
    :cancelText="t('no')"
    :onClickConfirm="confirmDeleteVoucher"
    :onClickCancel="
      () => {
        openDeleteVoucherDialog = false;
      }
    "
  >
    {{ t("are_you_sure_you_want_to_delete") }}
  </DialogPlus>

  <!-- Apply Promo Code Dialog -->
  <SnackBarPlus v-model="applyPromoCodeSnackBar">
    {{ applyPromoCodeSnackBarMsg }}
  </SnackBarPlus>

  <!-- Delete Coupon Dialog -->
  <DialogPlus
    v-model="openDeleteCouponDialog"
    :title="t('coupon')"
    :confirmText="t('yes')"
    :cancelText="t('no')"
    :onClickConfirm="confirmDeleteCoupon"
    :onClickCancel="
      () => {
        openDeleteCouponDialog = false;
      }
    "
  >
    {{ t("are_you_sure_you_want_to_delete") }}
  </DialogPlus>
</template>

<style lang="scss" scoped>
.flex {
  display: flex;
}

.pointer {
  cursor: pointer;
}

// Order Page
.order {
  width: 100%;
  display: inline-table;
  text-align: -webkit-center;
}

.order-container {
  max-width: 600px;
}
// Order Page

// Order User Location
.order-location {
  display: flex;
  justify-content: space-between;
  align-items: start;
  position: relative;
  background-color: v-bind("ColorPick.secondaryColor");
  border-radius: 2px;
  padding: 20px 10px;
  margin: 10px 10px;
  min-width: 315px;
}

.location-detail {
  text-align: left;
  font-size: 11px;
  font-weight: 400;
  color: v-bind("ColorPick.orderFontColor01");
}

.location-title {
  font-size: 16px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}
// Order User Location

// Order Details
.order-details {
  background-color: v-bind("ColorPick.secondaryColor");
  border-radius: 2px;
  padding: 15px 20px 10px 20px;
  margin: 10px;
  min-width: 315px;
  text-align: left;
}

.order-details-title {
  font-size: 16px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
}

// Order Details - product item
.product {
  display: flex;
  align-items: center;
  height: 90px;
  margin-bottom: 10px;
}

.product-image {
  margin-right: 10px;
  height: 90px;
  width: 90px;
}

.product-details {
  width: 100%;
}

.product-title {
  font-size: 12px;
  font-weight: 600;
  color: v-bind("ColorPick.primaryColor");
  max-lines: 1;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.product-delete {
  font-size: 11px;
  font-weight: 500;
  color: v-bind("ColorPick.deleteFontColor");
}

.product-desc {
  font-size: 11px;
  color: v-bind("ColorPick.primaryColor");
  max-lines: 2;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.product-price {
  font-size: 12px;
  font-weight: 600;
  color: v-bind("ColorPick.primaryColor");
  max-lines: 1;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.product-count {
  color: v-bind("ColorPick.primaryColor");
  font-size: 12px;
  font-weight: 500;
}
// Order Details - product item
// Order Details

// Voucher
.voucher {
  border-radius: 2px;
  padding: 20px 20px 20px 20px;
  margin: 10px;
  min-width: 315px;
  text-align: left;
  background-color: v-bind("ColorPick.secondaryColor");
}

.voucher-title {
  font-size: 16px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}

// Select Voucher Text button
.selected-voucher-btn {
  font-size: 13px;
  font-weight: 400;
  align-items: center;
  color: v-bind("ColorPick.orderFontColor01");
}

.add-promo-code {
  font-size: 13px;
  font-weight: 400;
  align-items: center;
  color: v-bind("ColorPick.primaryColor");
}

// Selected Voucher
.selected-voucher {
  display: flex;
  margin: 10px 0px;
  align-items: center;
  padding: 10px 10px;
  background-color: white;
  height: 100px;
  cursor: pointer;
}

.selected-voucher-details {
  display: flex;
  flex-direction: column;
  text-align: left;
  flex-grow: 1;
}

.selected-voucher-details-title {
  font-size: 14px;
  font-weight: 400;
}

.selected-voucher-image {
  height: 100%;
  aspect-ratio: 6 / 4;
}

.selected-voucher-details-desc {
  font-size: 9px;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  color: v-bind("ColorPick.greyFontColor02");
}

.selected-voucher-details-exp {
  font-size: 7px;
  font-weight: 600;
  color: v-bind("ColorPick.voucherExpireColor");
}
// Selected Voucher

.btn-apply {
  background-color: white !important;
  color: v-bind("ColorPick.primaryColor") !important;
  border: 2px solid v-bind("ColorPick.primaryColor") !important;
  padding: 0px 30px !important;
  text-align: center !important;
  text-transform: none !important;
  height: 30px;

  &:hover {
    background-color: v-bind("ColorPick.primaryColor") !important;
    // border-color: white !important;
    color: white !important;
  }
}

.selected-voucher.selected {
  border-color: #ff9900;
  /* Change this color to your desired highlight */
  background-color: rgba(255, 153, 0, 0.1);
  // border-radius: 10px;
}
// Voucher

// Subtotal
.subtotal-container {
  padding: 10px 25px;
  display: flex;
}

.subtotal-title {
  font-size: 16px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
}

.subtotal-price {
  font-size: 16px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
}
// Subtotal

// Remark
.remarks {
  border-radius: 2px;
  padding: 15px 20px;
  margin: 10px;
  min-width: 315px;
  text-align: left;
  background-color: v-bind("ColorPick.secondaryColor");
}

.remarks-title {
  font-size: 16px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}

.remarks-textarea {
  background-color: white !important;
  :placeholder-shown {
    font-size: 11px;
    font-weight: 400;
  }
  padding: 0px 10px;
}
// Remark

// Payment Details
.payment-details {
  border-radius: 2px;
  padding: 15px 20px;
  margin: 10px;
  min-width: 315px;
  text-align: left;
  background-color: v-bind("ColorPick.secondaryColor");
}

.payment-details-title {
  font-size: 16px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}

.payment-details-item {
  font-size: 12px;
  font-weight: 400;
  color: "#344054";
}

.payment-details-price {
  font-size: 11px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}

.payment-details-grand-item {
  font-size: 15px;
  font-weight: 400;
  color: "#344054";
}

.payment-details-grand-price {
  font-size: 14px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}
// Payment Details

// CheckOut
.order-action {
  padding: 15px 15px;
  text-align: left;
}

.total {
  font-size: 17px;
  font-weight: 600;
  color: v-bind("ColorPick.primaryColor");
}

.total-price {
  font-size: 14px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
}

.order-btn {
  color: white !important;
  background-color: v-bind("ColorPick.primaryColor") !important;
  padding: 0px 20px !important;
  text-align: center !important;
  text-transform: none !important;
}
// CheckOut

// Payment Bottom Sheet
.payment-sheet-title {
  font-size: 18px;
  font-weight: 500;
}
// Payment Bottom Sheet

// Address Dialog
.address-dialog-title {
  width: 100%;
  font-size: 16px;
  font-weight: 600;
  text-align: left;
  max-lines: 1;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  overflow-wrap: break-word;
}

.close-icon-btn {
  padding: 10px;
  block-size: 50px;
  inline-size: 50px;
}

.coupon-container {
  padding: 5px;
  display: flex;
  border: 1px solid v-bind("ColorPick.primaryColor");
  background-color: white;
  align-items: center;
  max-width: 100%;
  border-radius: 2px;
}

.remove-icon:hover {
  color: red;
}

// Empty Cart
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  /* Adjust as needed */
  text-align: center;
  font-size: 18px;
  font-weight: 500;
  color: #777;
}
</style>
