
export class TotalPriceDto {
  price: string;
  bundle_id?: number;
  gwp?: TotalPriceGWPDto[];
  pwp?: TotalPricePWPDto[];

  constructor(data: any) {
    this.price = data.price;
    this.bundle_id = data.bundle_id;

    try {
      this.gwp = data.gwp.map((item: any) => new TotalPriceGWPDto(item));

      if (
        (this.bundle_id === undefined || this.bundle_id === null) &&
        this.gwp &&
        this.gwp.length > 0
      ) {
        this.bundle_id = this.gwp[0].bundle_id;
      }
    } catch (_) {}

    try {
      this.pwp = data.pwp.map((item: any) => new TotalPricePWPDto(item));

      if (
        (this.bundle_id === undefined || this.bundle_id === null) &&
        this.pwp &&
        this.pwp.length > 0
      ) {
        this.bundle_id = this.pwp[0].bundle_id;
      }
    } catch (_) {}
  }
}

export class TotalPriceGWPDto {
  bundle_id: number;
  id: number;
  quantity: number;
  variation: TotalPriceVariantDto[];
  image: string;
  name: string;
  threshold: string;
  type: string;
  count: number;
  

  constructor(data: any) {
    try {
      this.variation = data.variation.map(
        (item: any) => new TotalPriceVariantDto(item)
      )
    } catch (_) {
      this.variation = [];
    }

    this.id = data.id;
    this.image = data.image;

    this.bundle_id = data.bundle_id;
    this.quantity = data.quantity;
    this.name = data.name;
    this.threshold = data.threshold;
    this.type = data.type;
    this.count = data.quantity; //Only for GWP
  }
}

export class TotalPricePWPDto {
  bundle_id: number;
  id: number;
  max_quantity: number;
  price: number;
  variation?: TotalPriceVariantDto[];
  image: string | undefined;
  name: string;
  count: number;

  constructor(data: any) {
    try {
      this.variation = data.variation.map(
        (item: any) => new TotalPriceVariantDto(item)
      );
    } catch (_) {}

    this.id = data.id;
    this.image = data.image;
    this.bundle_id = data.bundle_id;
    this.max_quantity = data.max_quantity;
    this.price = data.price;
    this.name = data.name;
    this.count = 0;
  }
}

export class TotalPriceVariantDto {
  id: number;
  name: string;
  image?: string;
  count: number;

  constructor(data: any) {
    this.id = data.id;
    this.name = data.name;
    this.image = data.image;
    this.count = 0;
  }
}
