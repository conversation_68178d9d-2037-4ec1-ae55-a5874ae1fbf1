import AppConfig from "@/appconfig";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/helpers/api_helper";

class CollectPointsApi {
  // Purchase Receipt Upload
   public static async uploadPurchasedReceipt(
    image: any,
  ): Promise<any> {
try {
   let body: any = {};

    if(image !=null){
      for(let i = 0; i < image.length; i++){
        let item = image[i];
        body[`image[${i}]`] = item;
      }
    }

    var response = await ApiHelper.post(AppConfig.apiPurchaseReceiptUploadUrl, undefined, body, "multipart/form-data");

    return response.data.status;
    } catch (error) {
      throw error;
    }
  }

}


export default CollectPointsApi;
