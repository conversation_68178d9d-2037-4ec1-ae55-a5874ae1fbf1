import RoutersHelper from "@/helpers/routes_helper";
import { translate } from "../i18n";
import localStorageHelper from "@/helpers/localstorage_helper";

//User Token
//Check User Token Found Redirect To Home
function checkUserTokenFound(to: any, from: any, next: any) {
  var userToken: string | null = localStorageHelper.getUserToken();
  if (userToken) {
    next(RoutersHelper.home);
  } else {
    next();
  }
}

//Check User Token not Found Redirect To Login
function checkUserTokenNotFound(to: any, from: any, next: any) {
  var userToken: string | null = localStorageHelper.getUserToken();
  if (userToken === null) {
    next(RoutersHelper.login);
  } else {
    next();
  }
}

export var routes = [
  // { path: '/', redirect: '/dashboard' },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("../../layouts/image_bg_frame.vue"),
    children: [
      {
        path: "",
        meta: { title: translate("page_not_found") },
        component: () => import("../../pages/error/NotFound.vue"),
      },
    ],
  },
  {
    path: RoutersHelper.maintenance,
    component: () => import("../../layouts/image_bg_frame.vue"),
    children: [
      {
        path: "",
        meta: { title: translate("under_maintenance") },
        component: () => import("../../pages/error/Maintenance.vue"),
      },
    ],
  },
  { path: RoutersHelper.referral, redirect: RoutersHelper.login },
  {
    path: "/",
    component: () => import("../../layouts/blank.vue"),
    children: [
      {
        path: "test",
        component: () => import("../../pages/test.vue"),
      },
      {
        path: "",
        beforeEnter: checkUserTokenFound,
        meta: { title: translate("splash_screen_title") },
        component: () => import("../../pages/SplashScreen.vue"),
      },
      {
        path: "",
        component: () => import("../../layouts/image_bg_frame.vue"),
        beforeEnter: checkUserTokenFound,
        children: [
          {
            path: RoutersHelper.login,
            meta: { title: translate("login") },
            component: () => import("../../pages/auth/Login.vue"),
          },
          {
            path: RoutersHelper.otp,
            meta: { title: translate("otp_validation") },
            component: () => import("../../pages/auth/Otp.vue"),
          },
          {
            path: RoutersHelper.setupProfile,
            meta: { title: translate("set_up_your_profile") },
            component: () => import("../../pages/auth/SetupProfile.vue"),
          },
        ],
      },
      {
        path: "",
        component: () => import("../../layouts/home_base.vue"),
        // beforeEnter: checkUserTokenNotFound,
        children: [
          {
            path: RoutersHelper.home,
            meta: { title: translate("home") },
            component: () => import("../../pages/dashboard/Home.vue"),
          },
          {
            path: RoutersHelper.product,
            meta: { title: translate("product") },
            component: () => import("../../pages/dashboard/Product.vue"),
          },
          {
            path: RoutersHelper.orders,
            meta: { title: translate("orders") },
            component: () => import("../../pages/dashboard/Orders.vue"),
          },
          {
            path: RoutersHelper.rewards,
            meta: { title: translate("rewards") },
            component: () => import("../../pages/dashboard/Rewards.vue"),
          },

          {
            path: RoutersHelper.account,
            meta: { title: translate("account") },
            component: () => import("../../pages/dashboard/Profile.vue"),
          },
        ],
      },
      {
        path: `${RoutersHelper.productDetail}/:id`,
        // beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("product_details") },
        component: () => import("../../pages/shop/ProductDetails.vue"),
      },
      {
        path: `${RoutersHelper.orderDetails}/:id`,
        beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("order_details") },
        component: () => import("../../pages/shop/OrderDetails.vue"),
      },
      {
        path: RoutersHelper.orderConfirmation,
        beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("order_confirmation") },
        component: () => import("../../pages/shop/OrderConfirmation.vue"),
      },
      {
        path: RoutersHelper.newsAnnouncement,
        // beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("news_announcement") },
        component: () =>
          import("../../pages/newsAnnouncement/NewsAnnouncementList.vue"),
      },
      {
        path: `${RoutersHelper.newsAnnouncementDetails}/:id`,
        // beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("news_announcement_details") },
        component: () =>
          import("../../pages/newsAnnouncement/NewsAnnouncementDetails.vue"),
      },
      {
        path: RoutersHelper.storeLocator,
        // beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("store_location") },
        component: () =>
          import("../../pages/storeLocator/StoreLocatorList.vue"),
      },
      {
        path: `${RoutersHelper.storeLocatorDetails}/:id`,
        // beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("store_location") },
        component: () =>
          import("../../pages/storeLocator/StoreLocatorDetails.vue"),
      },
      {
        path: `${RoutersHelper.rewardDetails}/:id`,
        // beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("reward_details") },
        component: () => import("../../pages/rewards/RewardDetails.vue"),
      },
      // {
      //   path: `${RoutersHelper.myRewardDetails}/:id`,
      //   beforeEnter: checkUserTokenNotFound,
      //   meta: { title: translate("reward_details") },
      //   component: () => import("../../pages/rewards/MyRewardDetails.vue"),
      // },
      {
        path: RoutersHelper.aboutUs,
        // beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("about_us") },
        component: () => import("../../pages/profile/AboutUs.vue"),
      },
      {
        path: RoutersHelper.privacyPolicy,
        // beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("privacy_policy") },
        component: () => import("../../pages/profile/PrivacyPolicy.vue"),
      },
      {
        path: RoutersHelper.termsAndCondition,
        // beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("terms_and_condition") },
        component: () => import("../../pages/profile/TermsAndConditions.vue"),
      },
      {
        path: RoutersHelper.myProfile,
        beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("my_profile") },
        component: () => import("../../pages/profile/MyProfile.vue"),
      },
      {
        path: RoutersHelper.faq,
        // beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("faq") },
        component: () => import("../../pages/profile/Faq.vue"),
      },
      {
        path: RoutersHelper.settings,
        beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("settings") },
        component: () => import("../../pages/profile/Settings.vue"),
      },
      {
        path: RoutersHelper.referralProgram,
        beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("referral_program") },
        component: () => import("../../pages/profile/ReferralProgram.vue"),
      },
      {
        path: RoutersHelper.myPoints,
        beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("my_points") },
        component: () => import("../../pages/rewards/MyPoints.vue"),
      },
      {
        path: RoutersHelper.address,
        beforeEnter: checkUserTokenNotFound,
        meta: { title: translate("address") },
        children: [
          {
            path: "",
            component: () => import("../../pages/address/Address.vue"),
          },
          {
            path: ":id",
            component: () => import("../../pages/address/Address.vue"),
          },
        ],
      },
      {
        path: RoutersHelper.memberBenefits,
        meta: { title: translate("member_benefits") },
        component: () => import("../../pages/profile/MemberBenefits.vue"),
      },
      {
        path: RoutersHelper.myWishlist,
        meta: { title: translate("my_wishlist") },
        component: () => import("../../pages/profile/MyWishlist.vue"),
      },
      {
        path: `${RoutersHelper.orderPay}/:order_no`,
        meta: { title: translate("order_pay") },
        component: () => import("../../pages/shop/OrderPay.vue"),
      },
      {
        path: `${RoutersHelper.orderPayResult}`,
        meta: { title: translate("order_pay_result") },
        component: () => import("../../pages/shop/OrderPayResult.vue"),
      },
    ],
  },
];
