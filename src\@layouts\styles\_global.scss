*,
::before,
::after {
  box-sizing: inherit;
  background-repeat: no-repeat;
}

html {
  box-sizing: border-box;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: Nunito Sans;
}

// /* Archivo Regular */
// @font-face {
//   font-family: 'Archivo';
//   font-style: normal;
//   font-weight: normal;
//   src: url('/fonts/archivo/Archivo-Regular.ttf') format('truetype');
// }

// /* Archivo Italic */
// @font-face {
//   font-family: 'Archivo';
//   font-style: italic;
//   font-weight: normal;
//   src: url('/fonts/archivo/Archivo-Italic.ttf') format('truetype');
// }

// /* Archivo Bold */
// @font-face {
//   font-family: 'Archivo';
//   font-style: normal;
//   font-weight: bold;
//   src: url('/fonts/archivo/Archivo-Bold.ttf') format('truetype');
// }

// /* Archivo Bold Italic */
// @font-face {
//   font-family: 'Archivo';
//   font-style: italic;
//   font-weight: bold;
//   src: url('/fonts/archivo/Archivo-BoldItalic.ttf') format('truetype');
// }

// /* Archivo Black */
// @font-face {
//   font-family: 'Archivo';
//   font-style: normal;
//   font-weight: 900;
//   src: url('/fonts/archivo/Archivo-Black.ttf') format('truetype');
// }

// /* Archivo Black Italic */
// @font-face {
//   font-family: 'Archivo';
//   font-style: italic;
//   font-weight: 900;
//   src: url('/fonts/archivo/Archivo-BlackItalic.ttf') format('truetype');
// }

// /* Archivo ExtraBold */
// @font-face {
//   font-family: 'Archivo';
//   font-style: normal;
//   font-weight: 800;
//   src: url('/fonts/archivo/Archivo-ExtraBold.ttf') format('truetype');
// }

// /* Archivo ExtraBold Italic */
// @font-face {
//   font-family: 'Archivo';
//   font-style: italic;
//   font-weight: 800;
//   src: url('/fonts/archivo/Archivo-ExtraBoldItalic.ttf') format('truetype');
// }

// /* Archivo SemiBold */
// @font-face {
//   font-family: 'Archivo';
//   font-style: normal;
//   font-weight: 600;
//   src: url('/fonts/archivo/Archivo-SemiBold.ttf') format('truetype');
// }

// /* Archivo SemiBold Italic */
// @font-face {
//   font-family: 'Archivo';
//   font-style: italic;
//   font-weight: 600;
//   src: url('/fonts/archivo/Archivo-SemiBoldItalic.ttf') format('truetype');
// }

// /* Archivo Medium */
// @font-face {
//   font-family: 'Archivo';
//   font-style: normal;
//   font-weight: 500;
//   src: url('/fonts/archivo/Archivo-Medium.ttf') format('truetype');
// }

// /* Archivo Medium Italic */
// @font-face {
//   font-family: 'Archivo';
//   font-style: italic;
//   font-weight: 500;
//   src: url('/fonts/archivo/Archivo-MediumItalic.ttf') format('truetype');
// }

// /* Archivo Light */
// @font-face {
//   font-family: 'Archivo';
//   font-style: normal;
//   font-weight: 300;
//   src: url('/fonts/archivo/Archivo-Light.ttf') format('truetype');
// }

// /* Archivo Light Italic */
// @font-face {
//   font-family: 'Archivo';
//   font-style: italic;
//   font-weight: 300;
//   src: url('/fonts/archivo/Archivo-LightItalic.ttf') format('truetype');
// }

// /* Archivo ExtraLight */
// @font-face {
//   font-family: 'Archivo';
//   font-style: normal;
//   font-weight: 200;
//   src: url('/fonts/archivo/Archivo-ExtraLight.ttf') format('truetype');
// }

// /* Archivo ExtraLight Italic */
// @font-face {
//   font-family: 'Archivo';
//   font-style: italic;
//   font-weight: 200;
//   src: url('/fonts/archivo/Archivo-ExtraLightItalic.ttf') format('truetype');
// }

// /* Archivo Thin */
// @font-face {
//   font-family: 'Archivo';
//   font-style: normal;
//   font-weight: 100;
//   src: url('/fonts/archivo/Archivo-Thin.ttf') format('truetype');
// }

// /* Archivo Thin Italic */
// @font-face {
//   font-family: 'Archivo';
//   font-style: italic;
//   font-weight: 100;
//   src: url('/fonts/archivo/Archivo-ThinItalic.ttf') format('truetype');
// }

@font-face {
  font-family: "NoyhRW01-Medium";
  src: url("https://db.onlinewebfonts.com/t/ccfdf10ba24124f7c35e43827aec5344.eot");
  src: url("https://db.onlinewebfonts.com/t/ccfdf10ba24124f7c35e43827aec5344.eot?#iefix") format("embedded-opentype"),
    url("https://db.onlinewebfonts.com/t/ccfdf10ba24124f7c35e43827aec5344.woff2") format("woff2"),
    url("https://db.onlinewebfonts.com/t/ccfdf10ba24124f7c35e43827aec5344.woff") format("woff"),
    url("https://db.onlinewebfonts.com/t/ccfdf10ba24124f7c35e43827aec5344.ttf") format("truetype"),
    url("https://db.onlinewebfonts.com/t/ccfdf10ba24124f7c35e43827aec5344.svg#Noyh R W01 Medium") format("svg");
}

/* Nunito Sans Regular */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: normal;
  src: url('/fonts/nunitoSans/NunitoSans-Regular.ttf') format('truetype');
}

/* Nunito Sans Italic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: normal;
  src: url('/fonts/nunitoSans/NunitoSans-Italic.ttf') format('truetype');
}

/* Nunito Sans Bold */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: bold;
  src: url('/fonts/nunitoSans/NunitoSans-Bold.ttf') format('truetype');
}

/* Nunito Sans Bold Italic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: bold;
  src: url('/fonts/nunitoSans/NunitoSans-BoldItalic.ttf') format('truetype');
}

/* Nunito Sans Black */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 900;
  src: url('/fonts/nunitoSans/NunitoSans-Black.ttf') format('truetype');
}

/* Nunito Sans Black Italic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 900;
  src: url('/fonts/nunitoSans/NunitoSans-BlackItalic.ttf') format('truetype');
}

/* Nunito Sans ExtraBold */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 800;
  src: url('/fonts/nunitoSans/NunitoSans-ExtraBold.ttf') format('truetype');
}

/* Nunito Sans ExtraBold Italic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 800;
  src: url('/fonts/nunitoSans/NunitoSans-ExtraBoldItalic.ttf') format('truetype');
}

/* Nunito Sans SemiBold */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 600;
  src: url('/fonts/nunitoSans/NunitoSans-SemiBold.ttf') format('truetype');
}

/* Nunito Sans SemiBold Italic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 600;
  src: url('/fonts/nunitoSans/NunitoSans-SemiBoldItalic.ttf') format('truetype');
}

/* Nunito Sans Medium */
// @font-face {
//   font-family: 'Nunito Sans';
//   font-style: normal;
//   font-weight: 500;
//   src: url('/fonts/archivo/Archivo-Medium.ttf') format('truetype');
// }

// /* Archivo Medium Italic */
// @font-face {
//   font-family: 'Nunito Sans';
//   font-style: italic;
//   font-weight: 500;
//   src: url('/fonts/archivo/Archivo-MediumItalic.ttf') format('truetype');
// }

/* Nunito Sans Light */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 300;
  src: url('/fonts/nunitoSans/NunitoSans-Light.ttf') format('truetype');
}

/* Nunito Sans Light Italic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 300;
  src: url('/fonts/nunitoSans/NunitoSans-LightItalic.ttf') format('truetype');
}

/* Nunito Sans ExtraLight */
@font-face {
  font-family: 'Nunito Sans';
  font-style: normal;
  font-weight: 200;
  src: url('/fonts/nunitoSans/NunitoSans-ExtraLight.ttf') format('truetype');
}

/* Nunito Sans ExtraLight Italic */
@font-face {
  font-family: 'Nunito Sans';
  font-style: italic;
  font-weight: 200;
  src: url('/fonts/nunitoSans/NunitoSans-ExtraLightItalic.ttf') format('truetype');
}

// /* Nunito Sans Thin */
// @font-face {
//   font-family: 'Nunito Sans';
//   font-style: normal;
//   font-weight: 100;
//   src: url('/fonts/archivo/Archivo-Thin.ttf') format('truetype');
// }

// /* Nunito Sans Thin Italic */
// @font-face {
//   font-family: 'Nunito Sans';
//   font-style: italic;
//   font-weight: 100;
//   src: url('/fonts/archivo/Archivo-ThinItalic.ttf') format('truetype');
// }