<script setup lang="ts">
// Dtos
import type { UserDto } from "@/dto/user.dto";
import { RedeemedRewardsDto, RewardsDto } from "@/dto/rewards.dto";
import type { PaginationDto } from "@/dto/pagination.dto";
import { PointDto } from "@/dto/point.dto";

// APIs
import AuthPageApi from "@/services/auth_api";
import RewardApi from "@/services/reward_api";
import PointsApi from "@/services/points_api";

// Helpers
import ColorPick from "@/helpers/colorpick";
import ThousandsHelper from "@/helpers/thousands_helper";
import RoutersHelper from "@/helpers/routes_helper";
import { useDisplay } from "vuetify";
import { useI18n } from "vue-i18n";
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { VPullToRefresh } from "vuetify/labs/VPullToRefresh";

// Components
import RewardsItem from "@/components/RewardsItem.vue";
import MyRewardsItem from "@/components/MyRewardsItem.vue";
import LoadingPlus from "@/components/custom_control/LoadingPlus.vue";
import LoadingPlusItem from "@/components/custom_control/LoadingPlusItem.vue";
import NoData from "@/components/custom_control/NoData.vue";
import RequiredLogin from "@/components/RequiredLogin.vue";

// Images
import AppConfig from "@/appconfig";
import moment from "moment";
import localStorageHelper from "@/helpers/localstorage_helper";

// Language
const { t } = useI18n();

//Handle Display
const { xs, smAndDown } = useDisplay();

// Route
const route = useRoute();
const router = useRouter();

const isLoading = ref<boolean>(false);

const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

// Fetch Screen Height
const screenHeight = ref(window.innerHeight);

const updateScreenHeight = () => {
  screenHeight.value = window.innerHeight;
};

// tabs
const activeTab = ref(route.query.tab ?? "redeem_rewards");
const pullDownThreshold = ref(60);

// Reward Pagination
const page = ref(1);
const hasMore = ref(true);
let doneCopy: (status: "error" | "loading" | "empty" | "ok") => void;
const rewardsListKey = ref(0);
const infiniteScrollRef = ref();

// My Reward Pagination
const myRewardPage = ref(1);
const myRewardhasMore = ref(true);
let myRewardDoneCopy: (status: "error" | "loading" | "empty" | "ok") => void;
const myRewardsListKey = ref(0);
const myInfiniteScrollRef = ref();

// Point History Pagination
const pointHistoryPage = ref(1);
const pointHistoryHasMore = ref(true);
let pointHistoryDoneCopy: (
  status: "error" | "loading" | "empty" | "ok"
) => void;
const pointHistoryListKey = ref(0);
const pointHistoryInfiniteScrollRef = ref();

// data
const userData = ref<UserDto>();
const rewardsList = ref<RewardsDto[]>([]);
const myRewardList = ref<RedeemedRewardsDto[]>([]);

const pointHistoryList = ref<PointDto[]>([]);
const openPointHistorySheet = ref<boolean>(false);

const openLoginDialog = ref<boolean>(false);
const token = localStorageHelper.getUserToken();

// Move to Reward Details
function toRewardDetails(id: string, isMyReward: string) {
  router.push({
    path: `${RoutersHelper.rewardDetails}/${id}`,
    query: { isMyReward: isMyReward },
  });
}

// Function - API
// Fetch Profile details
async function loadUser() {
  try {
    userData.value = await AuthPageApi.getProfile();
  } catch (_) {}
}

// Fetch Rewards List
async function loadRedeemableVoucher() {
  try {
    const response = await RewardApi.redeemableList(page.value);
    return response;
  } catch (_) {}
  return undefined;
}

// Fetch My Rewards List
async function loadRedeemedVoucher() {
  try {
    if (token === null) return undefined;
    const response = await RewardApi.redeemedList(myRewardPage.value);
    return response;
  } catch (_) {}
  return undefined;
}

// Fetch Points History List
async function loadPointsHistoryList() {
  try {
    const response = await PointsApi.list(pointHistoryPage.value, undefined);
    return response;
  } catch (_) {}
  return undefined;
}

// Rewards List - Infinite Scroll
async function infiniteload({
  done,
}: {
  done: (status: "error" | "loading" | "empty" | "ok") => void;
}) {
  if (!hasMore.value) {
    done("empty");
    return;
  }

  try {
    const res = await loadRedeemableVoucher();

    if (res?.data !== undefined) {
      const newData = res.data ?? [];
      const existingIds = new Set(rewardsList.value.map((item) => item.id));
      const filteredData = newData.filter(
        (item: any) => !existingIds.has(item.id)
      );

      rewardsList.value.push(...filteredData);

      const lastPage = res.last_page ?? 1;

      if (page.value >= lastPage) {
        hasMore.value = false;

        // For refresh the page, when change the tab
        doneCopy = done;
      } else {
        hasMore.value = true;

        page.value += 1;
      }
    } else {
      hasMore.value = false;
      done("empty");
      doneCopy = done;
    }
  } catch (e) {
  } finally {
    done("ok");
  }
}

// My Rewards - Infinite Scroll
async function myRewardInfiniteload({
  done,
}: {
  done: (status: "error" | "loading" | "empty" | "ok") => void;
}) {
  if (!myRewardhasMore.value) {
    done("empty");
    return;
  }

  try {
    const res = await loadRedeemedVoucher();

    if (res?.data !== undefined) {
      const newData = res.data ?? [];
      const existingIds = new Set(myRewardList.value.map((item) => item.id));
      const filteredData = newData.filter(
        (item: any) => !existingIds.has(item.id)
      );

      myRewardList.value.push(...filteredData);

      const lastPage = res.last_page ?? 1;

      if (myRewardPage.value >= lastPage) {
        myRewardhasMore.value = false;

        // For refresh the page, when change the tab
        myRewardDoneCopy = done;
      } else {
        myRewardhasMore.value = true;

        myRewardPage.value += 1;
      }
    } else {
      myRewardhasMore.value = false;
      done("empty");
      myRewardDoneCopy = done;
    }
  } catch (e) {
  } finally {
    done("ok");
  }
}

// Points History - Infinite Scroll
async function pointHistoryInfiniteload({
  done,
}: {
  done: (status: "error" | "loading" | "empty" | "ok") => void;
}) {
  if (!pointHistoryHasMore.value) {
    done("empty");
    return;
  }

  try {
    const res = await loadPointsHistoryList();

    if (res?.data !== undefined) {
      const newData = res.data ?? [];
      const existingIds = new Set(
        pointHistoryList.value.map((item) => item.data)
      );
      const filteredData = newData.filter(
        (item: any) => !existingIds.has(item.id)
      );

      pointHistoryList.value.push(...filteredData);

      const lastPage = res.last_page ?? 1;

      if (pointHistoryPage.value >= lastPage) {
        pointHistoryHasMore.value = false;

        // For refresh the page, when change the tab
        pointHistoryDoneCopy = done;
      } else {
        pointHistoryHasMore.value = true;

        pointHistoryPage.value += 1;
      }
    } else {
      pointHistoryHasMore.value = false;
      done("empty");
      pointHistoryDoneCopy = done;
    }
  } catch (e) {
  } finally {
    done("ok");
  }
}

// Open Points History Sheet
function selectOpenPointHistorySheet() {
  if (token !== null) {
    pointHistoryPage.value = 1;
    pointHistoryList.value = [];
    pointHistoryHasMore.value = true;
    pointHistoryListKey.value += 1;

    nextTick(() => {
      if (
        pointHistoryInfiniteScrollRef.value &&
        typeof pointHistoryInfiniteScrollRef.value.reset === "function"
      ) {
        pointHistoryInfiniteScrollRef.value.reset();
      }
    });
    openPointHistorySheet.value = true;
  } else {
    openLoginDialog.value = true;
  }
}

// Close Points History Sheet
function closePointHistorySheet() {
  openPointHistorySheet.value = false;
}

onMounted(() => {
  // Fetch Point Balance

  window.scrollTo(0, 0);
  window.addEventListener("resize", updateScreenHeight);

  var token: string | null = null;
  token = localStorageHelper.getUserToken();

  if (token !== null) {
    loadUser();
    loadPointsHistoryList();
    loadRedeemableVoucher();
  } else {
    loadRedeemableVoucher();
  }
});

onUnmounted(() => {
  window.removeEventListener("resize", updateScreenHeight);
});

async function refresh({ done }: { done: () => void }) {
  try {
    if (token !== null) {
      loadUser();

      // Voucher Refresh
      if (activeTab.value === "redeem_rewards") {
        page.value = 1;
        rewardsList.value = [];
        hasMore.value = true;
        rewardsListKey.value += 1;

        doneCopy("ok");
        await infiniteload({ done: doneCopy });
      }
      // My voucher Refresh

      // My voucher Refresh
      if (activeTab.value === "my_rewards") {
        myRewardPage.value = 1;
        myRewardList.value = [];
        myRewardhasMore.value = true;

        myRewardsListKey.value += 1;

        myRewardDoneCopy("ok");
        await myRewardInfiniteload({ done: myRewardDoneCopy });
      }
    } else {
      if (activeTab.value === "redeem_rewards") {
        page.value = 1;
        rewardsList.value = [];
        hasMore.value = true;
        rewardsListKey.value += 1;

        doneCopy("ok");
        await infiniteload({ done: doneCopy });
      }
    }

    // My voucher Refresh
  } catch (e) {
  } finally {
    done();
  }
}

// Watch - On tap the tab to refresh
watch(activeTab, (newValue) => {
  // Update the route query param so it's synced
  router.replace({ query: { tab: newValue } });

  if (newValue === "redeem_rewards") {
    page.value = 1;
    rewardsList.value = [];
    hasMore.value = true;
    rewardsListKey.value += 1;

    nextTick(() => {
      if (
        infiniteScrollRef.value &&
        typeof infiniteScrollRef.value.reset === "function"
      ) {
        infiniteScrollRef.value.reset();
      }
    });
  } else if (newValue === "my_rewards") {
    if (token !== null) {
      myRewardPage.value = 1;
      myRewardList.value = [];
      myRewardhasMore.value = true;

      myRewardsListKey.value += 1;

      nextTick(() => {
        if (
          myInfiniteScrollRef.value &&
          typeof myInfiniteScrollRef.value.reset === "function"
        ) {
          myInfiniteScrollRef.value.reset();
        }
      });
    } else {
      openLoginDialog.value = true;
      activeTab.value = "redeem_rewards";
    }
  }
});
</script>

<template>
  <!-- Loading -->
  <!-- <LoadingPlus v-if="isLoading" /> -->

  <!-- Snack Bar -->
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>

  <AppBarPlus :title="t('rewards')" />

  <div style="height: 70px"></div>
  <div class="rewards-container">
    <!-- Rewards Header -->
    <div class="rewards-banner-container">
      <VPullToRefresh @load="refresh" :pullDownThreshold="pullDownThreshold">
        <div style="padding: 25px 0px" @click="selectOpenPointHistorySheet">
          <span
            v-if="activeTab === 'redeem_rewards'"
            class="rewards-banner-text"
          >
            {{ ThousandsHelper.format(userData?.point_balance ?? 0) }}
            {{ t("points").toUpperCase() }}
          </span>
          <span v-else class="rewards-banner-text">
            {{ t("rewards_centre").toUpperCase() }}
          </span>
        </div>
        <!-- Rewards Header -->

        <div style="height: 20px"></div>

        <!-- Reward Tab bar -->
        <div class="rewards-tab-container">
          <div class="rewards-tab-button">
            <VBtn
              @click="() => (activeTab = 'redeem_rewards')"
              variant="flat"
              :key="'redeem_rewards'"
              v-model="activeTab"
              height="42px"
              :color="
                activeTab === 'redeem_rewards'
                  ? ColorPick.primaryColor
                  : '#ffffff'
              "
              style="text-transform: unset !important"
              :style="{
                fontSize: '11px',
                borderRadius: '40px',
                flex: 1,
                fontWeight: '400',
                border: '1px solid #201747',
              }"
              >{{ t("redeem_rewards") }}</VBtn
            >
            <VBtn
              @click="() => (activeTab = 'my_rewards')"
              variant="flat"
              v-model="activeTab"
              :key="'my_rewards'"
              height="42px"
              :color="
                activeTab === 'my_rewards' ? ColorPick.primaryColor : '#ffffff'
              "
              style="text-transform: unset !important"
              :style="{
                fontSize: '11px',
                borderRadius: '40px',
                flex: 1,
                fontWeight: '400',
                border: '1px solid #201747',
              }"
              >{{ t("my_rewards") }}</VBtn
            >
          </div>

          <div style="height: 10px"></div>

          <!-- Tab - Active -->
          <VWindow v-model="activeTab">
            <VWindowItem value="redeem_rewards">
              <v-container>
                <VInfiniteScroll
                  ref="infiniteScrollRef"
                  :key="rewardsListKey"
                  :height="'100%'"
                  :items="rewardsList"
                  @load="infiniteload"
                  v-model="hasMore"
                >
                  <template v-slot:empty>
                    <NoData v-if="rewardsList?.length <= 0" />
                    <span v-else>{{ t("no_more") }}</span>
                  </template>
                  <template
                    v-for="(item, index) in rewardsList"
                    :key="item"
                    v-if="rewardsList?.length ?? 0 > 0"
                  >
                    <RewardsItem
                      :image="item.list_image_url"
                      :reward="
                        item.discount_type === 'percentage'
                          ? item.value?.toString() + '%'
                          : AppConfig.currencyMark +
                            ' ' +
                            item.value?.toString()
                      "
                      :redeemWith="item.redeem_points?.toString()"
                      @click="toRewardDetails(item.id.toString(), 'false')"
                    ></RewardsItem>

                    <div style="height: 25px"></div>
                  </template>
                </VInfiniteScroll>
              </v-container>
            </VWindowItem>

            <!-- Reward Tab bar -->

            <!-- Tab - My Rewards -->
            <VWindowItem value="my_rewards">
              <VInfiniteScroll
                ref="myInfiniteScrollRef"
                :key="myRewardsListKey"
                :height="'100%'"
                :items="myRewardList"
                @load="myRewardInfiniteload"
                v-model="myRewardhasMore"
              >
                <template v-slot:empty>
                  <NoData v-if="myRewardList?.length <= 0" />
                  <span v-else>{{ t("no_more") }}</span>
                </template>
                <template
                  v-for="(myReward, index) in myRewardList"
                  :key="myReward"
                  v-if="myRewardList?.length ?? 0 > 0"
                >
                  <MyRewardsItem
                    :image="myReward.voucher_data?.image_url"
                    :title="myReward.voucher_data?.name"
                    :description="myReward.voucher_data?.description"
                    :status="myReward.status"
                    :validDate="myReward.effective_end_date"
                    @click="toRewardDetails(myReward.id.toString(), 'true')"
                  >
                  </MyRewardsItem>
                </template>
                <!-- <NoData v-else-if="!isLoading" text=""></NoData> -->
              </VInfiniteScroll>
            </VWindowItem>
          </VWindow>
          <!-- Tab - My Rewards -->
        </div>
        <div style="height: 55px"></div>
      </VPullToRefresh>
    </div>
  </div>

  <!-- Points History Bottomsheet -->
  <BottomSheetPlus
    v-model="openPointHistorySheet"
    :title="t('points_history')"
    :onClickConfirm="closePointHistorySheet"
    :onClickClose="closePointHistorySheet"
    :maxHeight="500"
  >
    <div class="points-history-bottomsheet-my-points-text">
      {{ t("my_points") }}
    </div>
    <div class="points-history-bottomsheet-my-points">
      {{ userData?.point_balance }} {{ t("points") }}
    </div>
    <!-- <div style="height: 20px"></div> -->
    <v-container style="padding: 0px">
      <VInfiniteScroll
        ref="pointHistoryInfiniteScrollRef"
        :key="pointHistoryListKey"
        :height="'100%'"
        :items="pointHistoryList"
        @load="pointHistoryInfiniteload"
        v-model="pointHistoryHasMore"
        :empty-text="
          pointHistoryList?.length ?? 0 > 0 ? t('no_more') : t('no_data_found')
        "
      >
        <template
          v-for="(points, index) in pointHistoryList"
          :key="points"
          v-if="pointHistoryList?.length ?? 0 > 0"
        >
          <div v-for="item in points.data" class="points-history-item">
            <div class="points-history-reason">{{ item.remarks }}</div>
            <div class="points-history-right">
              <div
                :style="{
                  color:
                    item.points_prefix === '+'
                      ? ColorPick.rewardsPointHistoryPositive
                      : ColorPick.rewardsPointHistoryNegative,
                }"
              >
                {{ item.points }} {{ t("points") }}
              </div>
              <div class="points-history-date">
                {{ moment(item.updated_at).format("D MMM YYYY, hh:mm") }}
              </div>
            </div>
          </div>
        </template>
      </VInfiniteScroll>
    </v-container>
  </BottomSheetPlus>

  <!-- Required login dialog -->
  <RequiredLogin v-model="openLoginDialog" :isBack="true" />
</template>

<style lang="scss" scoped>
.rewards-container {
  height: 100vh;
  width: 100%;
  text-align: -webkit-center;
}

.rewards-banner-container {
  padding: v-bind('xs ? "0" : "15px"');
}

.rewards-banner-text {
  cursor: pointer;
  font-size: 20px;
  font-weight: 400;
  text-align: center;
  padding: 0px;
  color: #000000;
}

.rewards-tab-button {
  display: flex;
  gap: 70px;
  padding: 10px 20px;
}

.rewards-tab-container {
  max-width: 600px;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 180px);
  background-color: v-bind("ColorPick.secondaryColor");
  border-top-left-radius: 30px;
  border-top-right-radius: 30px;
  padding: 20px 10px;
}

.tab-container {
  display: flex;
  justify-content: center;
  max-width: 600px;
  margin: 0 auto;
}

.custom-tabs {
  width: 100%;
  max-width: 600px;
  background-color: v-bind("ColorPick.secondaryColor");
}

.v-tab {
  border: 1px solid #2e1e50;
  border-radius: 20px !important;
  margin: 0px 10px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
  text-transform: uppercase;
}

.v-tab--selected {
  background-color: #2e1e50 !important;
  color: white !important;
}

.redeem-rewards-container {
  max-width: 600px;
  display: flex;
  padding: 20px;
  flex-direction: column;
  flex-grow: 1;
}

.redeem-rewards-content {
  position: relative;
  //   display: flex;
  //   flex-wrap: wrap;
  justify-content: center;
}

.my-rewards-container {
  max-width: 600px;
  display: flex;
  padding: 20px;
  flex-direction: column;
  flex-grow: 1;
}

.pagination {
  position: fixed;
  max-width: 600px;
  width: 100%;
  max-width: 600px;
  bottom: 70px;
  background-color: v-bind("ColorPick.secondaryColor");
  padding: 10px 0;
  left: 50%;
  transform: translateX(-50%);
}

// My points History BottomSheet
.points-history-bottomsheet-my-points-text {
  color: #000000;
  font-weight: 600;
  font-size: 13px;
}

.points-history-bottomsheet-my-points {
  color: #000000;
  font-weight: 600;
  font-size: 20px;
}

.points-history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #dadada;
}

.points-history-reason {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 9px;
  color: #201747;
}

.points-history-date {
  font-size: 12px;
  font-weight: 400;
  color: #201747;
}

.points-history-right {
  font-size: 16px;
  font-weight: 500;
  text-align: right;
}

.points-history-amount {
  font-weight: bold;
}

.point-history-pagination {
  position: fixed;
  max-width: 600px;
  width: 100%;
  max-width: 600px;
  bottom: 60px;
  padding: 10px 0;
  left: 50%;
  transform: translateX(-50%);
}
</style>

<style lang="scss">
.v-tabs {
  overflow-x: auto !important;
  white-space: nowrap;
}

.v-tab {
  text-transform: none !important;
}

@media (max-width: 385px) {
  .v-tab {
    font-size: 13px;
    padding: 8px 12px;
  }
}
</style>
