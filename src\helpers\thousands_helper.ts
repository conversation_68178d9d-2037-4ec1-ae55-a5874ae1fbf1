class ThousandsHelper {
  static format(
    value: string | number,
    decimal: boolean = false
  ): string | undefined {
    try {
      let tempValue: string = value.toString();

      const isNegative = tempValue.startsWith("-");
      if (isNegative) {
        tempValue = tempValue.substring(1);
      }

      let [integerPart, decimalPart] = tempValue.split(".");
      console.log(integerPart, decimalPart, tempValue, value)
      integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

      if ((decimalPart && parseFloat(decimalPart) > 0) || decimal) {
        let tempDecimalPart: number = parseFloat(decimalPart) || 0;
        tempValue = `${integerPart}.${tempDecimalPart.toString().padStart(2, '0')}`;
      } else {
        tempValue = integerPart;
      }

      if (isNegative) {
        tempValue = `-${tempValue}`;
      }

      return tempValue;
    } catch (e) {
      return undefined;
    }
  }
}

export default ThousandsHelper;
