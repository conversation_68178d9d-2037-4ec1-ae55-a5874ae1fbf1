import messages from '@intlify/unplugin-vue-i18n/messages'
import { useStorage } from '@vueuse/core'
import type { App } from 'vue'
import { createI18n } from 'vue-i18n'

export function translate(key: string) {
  const defaultLocale = useStorage('locale', 'en-US')
  const i18n = createI18n({
    locale: defaultLocale.value,
    messages,
  })

  return i18n.global.t(key)
}

export default function (app: App) {
  const defaultLocale = useStorage('locale', 'en-US')
  const i18n = createI18n({
    locale: defaultLocale.value,
    messages,
  })
  app.use(i18n)
}
