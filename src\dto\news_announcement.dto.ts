import moment from "moment";

export class NewsAnnouncementDto {
  id: number;
  title: string;
  brand: string;
  image_url: string[];
  single_image_url: string;
  content: string;
  created_at: string | undefined;
  updated_at: string | undefined;

  constructor(data: any) {
    try {
      this.image_url = data.image_url.map((item: any) => item.toString());
    } catch (_) {
      this.image_url = [];
    }

    this.id = data?.id;
    this.title = data?.title;
    this.brand = data?.brand;
    this.content = data?.content;
    this.single_image_url = data?.single_image_url;
    this.created_at = data?.created_at
      ? moment(data.created_at).format("DD MMMM YYYY")
      : undefined;
    this.updated_at = data?.created_at
      ? moment(data.updated_at).format("DD MMMM YYYY")
      : undefined;
  }
}
