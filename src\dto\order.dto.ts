import { OrderStatusEnum } from "@/enums/OrderStatusEnum";
import { ProductDto } from "./product.dto";
import { CartProductsDto } from "./cart_summary.dto";
import moment from "moment";
import AppConfig from "@/appconfig";
import { StoreLocatorDto } from "./store_locators.dto";

export interface OrderStatus {
  label: string;
  completed: boolean;
}

export class MyOrderDto {
  id: number;
  order_no: string;
  status: string;
  currency: string;
  total: string;
  created_at: Date;
  products?: CartProductsDto;
  tracking_number?: string;
  delivery_method?: string;
  shipping_fee?: string;
  voucher?: string;
  delivery_discount?: string;
  coupon_id?: string;
  coupon?: string;
  subtotal?: string;
  tracking_link?: string
  total_tax?: string;
  point_earned?: number;
  address_recipient?: string;
  address_phone?: string;
  address?: string;
  city?: string;
  postcode?: string;
  state?: string;
  address_country?: string;
  receipt?: string;
  rating?: number;
  review?: string;
  payment_method?: string;
  sales_channel?: string;
  outlet_id?: number;
  outlet: StoreLocatorDto;
  delivery_vehicle?: string;
  lalamove_quotation_id?: string;
  lalamove_status?: string;
  share_link?: string;
  remark?: string;
  payment_date?: Date;
  payment_ref_no?: string;
  show_msg?: boolean;
  status_progress?: StatusProgressDto[];

  constructor(data: any) {
    try {
      this.products = new CartProductsDto(data.products);
    } catch (_) {}
    
    try{
      this.outlet = new StoreLocatorDto(data.outlet);
    }catch(_){
    }

    try {
      if (data.created_at) {
        this.created_at = moment(
          data.created_at,
          "YYYY-MM-DD HH:mm:ss"
        ).toDate();
      }

      if (data.created_date) {
        this.created_at = moment(
          data.created_date,
          "YYYY-MM-DD HH:mm:ss"
        ).toDate();
      }
    } catch (_) {}

    try {
        this.status_progress = data.status_progress.map((item: any) => new StatusProgressDto(item));
        } catch (_) {
        this.status_progress = [];
      }

    this.id = data.id;
    this.order_no = data.order_no;
    this.status = data.status;
    this.created_at = data.created_at || data.created_date;
    this.currency = data.currency ?? AppConfig.currencyMark;
    this.total = data.total;
    this.tracking_number = data.tracking_number;
    this.delivery_method = data.delivery_method;
    this.shipping_fee = data.shipping_fee;
    this.voucher = data.voucher;
    this.delivery_discount = data.delivery_discount;
    this.coupon_id = data.coupon_id;
    this.coupon = data.coupon;
    this.subtotal = data.subtotal;
    this.total_tax = data.total_tax;
    this.point_earned = data.point_earned;
    this.address_recipient = data.address_recipient;
    this.address_phone = data.address_phone;
    this.address = data.address;
    this.city = data.city;
    this.postcode = data.postcode;
    this.state = data.state;
    this.address_country = data.address_country;
    this.receipt = data.receipt;
    this.rating = data.rating;
    this.review = data.review;
    this.payment_method = data.payment_method;
    this.payment_date = data.payment_date;
    this.sales_channel = data.sales_channel;
    this.outlet_id = data.outlet_id;
    this.delivery_vehicle = data.delivery_vehicle;
    this.lalamove_quotation_id = data.lalamove_quotation_id;
    this.lalamove_status = data.lalamove_status;
    this.share_link = data.share_link;
    this.remark = data.remark;
    this.payment_ref_no = data.payment_ref_no;
    this.show_msg = data.show_msg == 1;
    this.tracking_link = data.tracking_link;
  }
}

export class StatusProgressDto{
  status: string;
  checked: boolean;

  constructor(data: any) {
    this.status = data.status;
    this.checked = data.checked == 1;
  }
} 
