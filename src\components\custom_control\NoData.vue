<script setup lang="ts">
import { useI18n } from "vue-i18n";
import logo from "@/assets/icons/logo.png";
import { translate } from "../../plugins/i18n";

// Language
const { t } = useI18n();

interface Props {
  text?: string | undefined;
}

const props = withDefaults(defineProps<Props>(), {
  text: translate("no_data_found"),
});
</script>

<template>
  <div class="max-width-handle">
    <div class="logo-container">
      <VImg class="logo" :src="logo" :width="100" :height="100" alt="logo" />
    </div>
    <span class="no-data-text">
      {{ props.text }}
    </span>
  </div>
</template>

<style lang="scss" scoped>
.logo-container {
  display: flex;
  justify-content: center;
}

.max-width-handle {
  inline-size: 100%;
  text-align: center;
}

.no-data-text {
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  font-size: 16px;
  font-weight: 500;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  text-align: center;
}
</style>
