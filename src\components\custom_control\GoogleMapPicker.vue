<script setup lang="ts">
import { Loader } from "@googlemaps/js-api-loader";
import { onMounted, ref, watch } from "vue";
import { useI18n } from 'vue-i18n';
import ColorPick from "@/helpers/colorpick";
import AppConfig from "@/appconfig";

// Props and Emits
interface Props {
    style?: any | undefined;
    defaultLocation?: { lat: number | undefined; lng: number | undefined } | undefined;
    isEdit?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    style: undefined,
    defaultLocation: undefined,
    isEdit: false,
});

// Language
const { t } = useI18n();

// For Edit No need load address to lat lng in frist time
let isEditFirstTime = props.isEdit;

// Return
const emit = defineEmits<{
    (e: 'update:location', value: { lat: number | undefined; lng: number | undefined }): void;
    (e: 'update:onLoading', value: boolean): void;
}>();
const selectedLocation = ref<{ lat: number | undefined; lng: number | undefined }>({
    lat: props.defaultLocation?.lat,
    lng: props.defaultLocation?.lng,
});
const model = defineModel<string>(); // Address

// Search
const searchDebounce = ref(); //Search
const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>('');

// Map
const loader = new Loader({
    apiKey: AppConfig.googleMapApiKey,
    version: "weekly",
    libraries: ["maps", "marker", "places"],
});

const isError = ref<boolean>(false);
const isLoading = ref<boolean>(false);

let map: google.maps.Map | null = null;
let marker: google.maps.marker.AdvancedMarkerElement | null = null;

// Functions
const searchListener = () => {
    if (isEditFirstTime) {
        // For Edit No need load address to lat lng in frist time
        isEditFirstTime = false;

        if (selectedLocation.value.lat !== undefined && selectedLocation.value.lng !== undefined && selectedLocation.value.lat !== null && selectedLocation.value.lng !== null) {
            return;
        }
    }

    isLoading.value = true;

    if (searchDebounce.value ?? false) {
        clearTimeout(searchDebounce.value);
        searchDebounce.value = undefined;
    }

    if (!searchDebounce.value) {
        searchDebounce.value = setTimeout(() => {
            getLatLong();
        }, 1000);
    }

};


async function initializeMap() {
    try {
        await loader.importLibrary("maps");
        await loader.importLibrary("marker");
        await loader.importLibrary("places");
        const { AdvancedMarkerElement } = await google.maps.importLibrary("marker") as google.maps.MarkerLibrary;
        const { Map } = await google.maps.importLibrary("maps") as google.maps.MapsLibrary;

        map = new Map(document.getElementById('map') as HTMLElement, {
            center: selectedLocation.value.lat && selectedLocation.value.lng ? { lat: selectedLocation.value.lat, lng: selectedLocation.value.lng } : { lat: 3.1424, lng: 101.6981 },
            zoom: 18,
            mapId: AppConfig.googleMapId,
            streetViewControl: false,
            zoomControl: false,
            mapTypeControl: false,
            fullscreenControl: false,
        });

        marker = new AdvancedMarkerElement({
            map,
            gmpDraggable: true,
        });

        // Update location on marker drag
        marker.addListener('dragend', () => {
            if (marker && marker.position) {
                const position = marker.position as google.maps.LatLngLiteral;
                selectedLocation.value = {
                    lat: position.lat,
                    lng: position.lng,
                };
                emit('update:location', selectedLocation.value);
            }
        });

        await updateMarker();

    } catch (e) {
        isError.value = true;
    }
}


async function getLatLong() {
    try {
        isLoading.value = true;

        // Reset
        selectedLocation.value = {
            lat: undefined,
            lng: undefined,
        };

        if (model.value) {
            var geocoder = new google.maps.Geocoder();

            geocoder.geocode({ 'address': model.value }, async function (results, status) {
                if (status == 'OK' && map && results && results.length > 0) {
                    map.setCenter(results[0].geometry.location);

                    selectedLocation.value.lat = results[0].geometry.location.lat();
                    selectedLocation.value.lng = results[0].geometry.location.lng();

                    await updateMarker();
                }
            });
        }
    } catch (e) {
        snackBarMsg.value = `${e}`;
        openSnackBar.value = true;
    } finally {
        isLoading.value = false;
    }
}

// Update Marker
async function updateMarker() {
    if (marker) {
        marker.position = selectedLocation.value.lat && selectedLocation.value.lng ? { lat: selectedLocation.value.lat, lng: selectedLocation.value.lng } : undefined;

        // Emit initial location
        emit('update:location', selectedLocation.value);
    }

}

onMounted(() => {
    initializeMap();
})

//Watch search
watch(model, searchListener);

watch(isLoading, (newValue) => {
    emit('update:onLoading', newValue);
})
</script>

<template>
    <!-- Snack Bar -->
    <SnackBarPlus v-model="openSnackBar">
        {{ snackBarMsg }}
    </SnackBarPlus>
    <div>
        <VProgressLinear v-if="isLoading" indeterminate color="primary"></VProgressLinear>
        <div id="map" class="map" :style="props.style">
            <span class="error-message" v-if="isError">{{ t('failed_to_load_the_map') }}</span>
        </div>
    </div>

</template>

<style lang="scss" scoped>
#map {
    border: 1px solid #ccc;
    position: relative;
    z-index: 1;
    height: 100%;
    width: 100%;
}

.map-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.7);
}

.error-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: v-bind('ColorPick.fontColor');
    font-weight: bold;
    font-size: 16px;
}
</style>
