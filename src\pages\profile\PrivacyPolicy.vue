<script setup lang="ts">
import GeneralContent from "@/components/GeneralContent.vue";
import type { GeneralPageDto } from "@/dto/general_page.dto";
import GeneralPageApi from "@/services/general_page_api";
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from 'vue-router';

const { t } = useI18n();
const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>('');

const isLoading = ref<boolean>(false);
const router = useRouter();

//Data
const data = ref<GeneralPageDto>();

//Function
function goBack() {
  if (window.history.state.back === null) {
    router.push('/');
  } else {
    router.back();
  }
}

async function loadData() {
  try {
    isLoading.value = true;
    data.value = await GeneralPageApi.getPrivacyPolicy();
  } catch (error) {
    dialogMsg.value = `${error}`;
    openDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}

onMounted(() => {
  window.scrollTo(0, 0);
  loadData();
})
</script>

<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading"></LoadingPlus>
  <!-- Success Dialog -->
  <DialogPlus persistent v-model="openDialog" :title="t('privacy_policy')" :confirmText="t('okay')" :onClickConfirm="() => {
    openDialog = false;
    goBack();
  }">
    {{ dialogMsg }}
  </DialogPlus>
  <GeneralContent :title="t('privacy_policy')" :htmlContent="data?.content ?? ''" />
  <!-- Body  -->
</template>
