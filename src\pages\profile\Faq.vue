<script setup lang="ts">
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";
import { ref, onMounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { VPullToRefresh } from "vuetify/labs/VPullToRefresh";

import type { FaqsDto } from "@/dto/faqs.dto";
import GeneralPageApi from "@/services/general_page_api";

// Language
const { t } = useI18n();

// Route
const route = useRoute();

const openDialog = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const router = useRouter();

//Data
const data = ref<FaqsDto[]>([]);
const dialogMsg = ref<string>("");

const pullDownThreshold = ref(60);

//Function
function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

// Function - API
async function loadData() {
  try {
    isLoading.value = true;
    data.value = await GeneralPageApi.getFaqs();
  } catch (error) {
    dialogMsg.value = `${error}`;
    openDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}

// onRefresh
async function refresh({ done }: { done: () => void }) {

  loadData();
  setTimeout(() => {
    done(); // Stop the loading
  }, 1000);
}

onMounted(() => {
  window.scrollTo(0, 0);
  loadData();
});
</script>

<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading"></LoadingPlus>
  <!-- Success Dialog -->
  <DialogPlus
    persistent
    v-model="openDialog"
    :title="t('faq')"
    :confirmText="t('okay')"
    :onClickConfirm="
      () => {
        openDialog = false;
        goBack();
      }
    "
  >
    {{ dialogMsg }}
  </DialogPlus>

  <AppBarPlus :title="t('faq')" :back="true" />

  <!-- AppBarPlus Spacing -->
  <div style="height: 70px" />
  <v-pull-to-refresh :pull-down-threshold="pullDownThreshold" @load="refresh">
    <div class="faq">
      <div v-if="data.length > 0" class="faq-container">
        <VExpansionPanels variant="accordion" multiple>
          <VExpansionPanel
            v-for="item in data"
            :title="item.question"
            :text="item.answer"
          />
        </VExpansionPanels>
      </div>
      <NoData v-else-if="!isLoading" />
    </div>
  </v-pull-to-refresh>
</template>

<style lang="scss" scoped>
.flex {
  display: flex;
}

.item-center {
  align-items: center;
}

.faq {
  width: 100%;
  height: 100vh;
  display: inline-table;
  text-align: -webkit-center;
}

.faq-container {
  max-width: 600px;
}

.faq-item {
  margin: 50px 10px;
}

.faq-item:first-child {
  margin-top: 0;
}

.faq-title {
  color: v-bind("ColorPick.faqTitleColor");
  font-size: 19px;
  line-height: 19px;
  font-weight: 600;
}
</style>

<style lang="scss">
.v-expansion-panels {
  z-index: unset !important;
}

.v-expansion-panel__shadow {
  box-shadow: unset !important;
}

.v-expansion-panel-title {
  font-size: 15px !important;
  font-weight: 500;
  border-bottom: 1px solid #2e263d26;
  color: v-bind("ColorPick.fontColor");
}

.v-expansion-panel-text {
  text-align: left;
  font-size: 13px !important;
  font-weight: 400;
  background-color: #ffffff;
}

.v-expansion-panel-text__wrapper {
  padding-top: 16px !important;
  padding-bottom: 32px !important;
}

.v-expansion-panel::after {
  opacity: 0 !important;
}
</style>
