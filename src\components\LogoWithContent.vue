<template>
  <div class="logo-with-content-base">
    <div>
      <VImg
        class="logo"
        :src="logo"
        :width="100"
        alt="logo"
      />
    </div>
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import logo from '@/assets/icons/logo.png';
</script>

<style lang="scss" scoped>
.logo-with-content-base {
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  overflow: auto;
  box-sizing: border-box;
  padding: 10px;
  max-width: 100%;
  max-height: 100%;
}

.logo {
  display: block;
  padding: 10px;
  margin-inline: auto;
}
</style>
