import moment from "moment";

export class StoreLocatorDto {
  id: number;
  chain_store_id: number;
  name: string;
  brand: string;
  contact_number: string;
  image: string;
  image_url: string;
  description: string;
  outlets: StoreLocatorDto[];
  address: string;
  latitude: string;
  longitude: string;
  state: string;
  location: string;
  operating_hours: any;
  created_at: string;
  updated_at: string;
  isFeatured: number;
  is_open: boolean;


  constructor(data: any) {
    this.id = data.id;
    this.chain_store_id = data.chain_store_id;
    this.name = data.name;
    this.brand = data.brand;
    this.contact_number = data.contact_number;
    this.image = data.image;
    this.image_url = data.image_url;
    this.description = data.description;
    this.outlets = data.outlets;
    this.address = data.address;
    this.latitude = data.latitude;
    this.longitude = data.longitude;
    this.state = data.state;
    this.location = data.location;
    this.operating_hours = data.operating_hours;
    this.created_at = moment(data.created_at).format("DD MMMM YYYY");
    this.updated_at = moment(data.updated_at).format("DD MMMM YYYY");
    this.isFeatured = data.isFeatured;
    this.is_open = data.is_open;
  }
}


export class StoreLocatorPaginationDto {
  current_page: number | undefined;
  last_page: number | undefined;
  per_page: number | undefined;
  total: number | undefined;
  data?: StoreLocatorDto[];

  constructor(data: any) {
    try {
      this.data = data.data.map((item: any) => new StoreLocatorDto(item));
    } catch (_) {}

    this.current_page = data?.current_page;
    this.last_page = data?.last_page;
    this.per_page = data?.per_page;
    this.total = data?.total;
  }
}

export class StoreLocatorDistanceDto{
  distance: string;

  constructor(data: any) {
    this.distance = data.distance;
  }
}