<script setup lang="ts">
import ColorPick from "@/helpers/colorpick";
import ValidationHelper from "@/helpers/validation_helper";
import success from "@/assets/images/success.png";
import placeholder from "@/assets/icons/logo.png";
import TextFieldPlus from "@/components/custom_control/TextFieldPlus.vue";
import TelInputPlus from "@/components/custom_control/TelInputPlus.vue";
import ButtonPlus from "@/components/custom_control/ButtonPlus.vue";
import UploadPlus from "@/components/custom_control/UploadPlus.vue";
import moment from "moment";

import type { VForm } from "vuetify/components";
import { useI18n } from "vue-i18n";
import { ref, onMounted, watch } from "vue";
import type { UserDto } from "@/dto/user.dto";

import AuthPageApi from "@/services/auth_api";
import "@/interfaces/getphonenumber";

// Language
const { t } = useI18n();

const name = ref<string>("");
const phone = ref<string>("");
const email = ref<string>("");
const dob = ref<string>("");
const otp = ref<string>("");

const isLoading = ref<boolean>(false);
const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");
const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>("");
const openSuccessDialog = ref<boolean>(false);
const openOTPDialog = ref<boolean>(false);
const openUploadSheet = ref<boolean>(false);

//Timer
const timerDefault: number = 60;
const timeRemaining = ref(timerDefault);
const isTimerRunning = ref(false);
let timer: any = null;

// Verifiy Phone Number Otp
const verifiedOtp = ref<boolean>(true);
const verifiedPhoneNumber = ref<string>("");

// User Profile Data
const userData = ref<UserDto>();
const refForm = ref<VForm>();
const profileImage = ref<string | null>(null);
const localModelValue = ref({ localUrls: [], selectedFile: null });

// Handle Image upload
const handleImageUpload = (imageData: {
  localUrls: string | any[];
  webUrls: string | any[];
}) => {
  if (imageData?.localUrls?.length > 0) {
    profileImage.value = imageData.localUrls[0].url;
    localModelValue.value.selectedFile = imageData.localUrls[0]; // Store the File
  } else if (imageData?.webUrls?.length > 0) {
    profileImage.value = imageData.webUrls[0].url;
  }
};

onMounted(() => {
  window.scrollTo(0, 0);

  init();
});

watch(verifiedOtp, (newValue) => {
  if (newValue) {
    // phoneApiError.value = '';
  }
});

function init() {
  loadUser();
}

// Call API
// Get profile
async function loadUser() {
  try {
    isLoading.value = true;

    userData.value = await AuthPageApi.getProfile();

    if (userData.value != null) {
      profileImage.value = userData.value.profile_image;
      name.value = userData.value.name;
      email.value = userData.value.email_address;
      phone.value = userData.value.phone_number;
      verifiedPhoneNumber.value = userData.value.phone_number;
      dob.value = moment(userData.value.dob).format("YYYY-MM-DD");
    }
  } catch (_) {
  } finally {
    isLoading.value = false;
  }
}

// Submit Edit Proifle API
async function onSubmit() {
  const validated = await refForm.value?.validate();

  if (verifiedOtp.value === true) {
    if (validated?.valid) {
      try {
        //If Loading will no do anything
        if (isLoading.value) {
          snackBarMsg.value = t("please_wait...");
          openSnackBar.value = true;
          return;
        }

        //Show Loader
        isLoading.value = true;

        // Use the stored file
        const imageFile = localModelValue.value.selectedFile || null;

        await AuthPageApi.editProfile(
          imageFile != null ? imageFile : null,
          name.value,
          email.value,
          `${phone.value}`.getPhoneNumber(),
        );

        loadUser();

        openSuccessDialog.value = true;
      } catch (e) {
        dialogMsg.value = `${e}`;
        openDialog.value = true;
      } finally {
        isLoading.value = false;
      }
    }
  } else {
    snackBarMsg.value = t("please_verify_otp");
    openSnackBar.value = true;
  }
}

async function verifyOtpSubmit() {
  if (otp?.value !== "" && otp?.value.length === 6) {
    otpVerify();
  } else {
    snackBarMsg.value = t("please_enter_otp");
    openSnackBar.value = true;
  }
}

async function otpVerify() {
  //If Loading will no do anything
  if (isLoading.value) {
    snackBarMsg.value = t("please_wait...");
    openSnackBar.value = true;
    return;
  }

  try {
    isLoading.value = true;

    await AuthPageApi.profileVerifyOTP(
      `${phone.value}`.getPhoneNumber(),
      otp.value
    );

    verifiedOtp.value = true;
    openOTPDialog.value = false;

    // loadUser();
  } catch (e) {
    snackBarMsg.value = `${e}`;
    openSnackBar.value = true;
  } finally {
    isLoading.value = false;
  }
}

//Timer
const startTimer = () => {
  if (!isTimerRunning.value && timeRemaining.value > 0) {
    isTimerRunning.value = true;
    timer = setInterval(() => {
      timeRemaining.value--;
      if (timeRemaining.value === 0) {
        resetTimer();
      }
    }, 1000);
  }
};

const resetTimer = () => {
  clearInterval(timer);
  timeRemaining.value = timerDefault;
  isTimerRunning.value = false;
};

const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, "0")}:${secs
    .toString()
    .padStart(2, "0")}`;
};

// Sent Otp to verify
async function sentOtp() {
  //If Loading will no do anything
  if (isLoading.value) {
    snackBarMsg.value = t("please_wait...");
    openSnackBar.value = true;
    return;
  }

  try {
    isLoading.value = true;
    await AuthPageApi.sendOtp(`${phone.value}`.getPhoneNumber(),
    );
    startTimer();
    if (openOTPDialog.value != true) {
      openOTPDialog.value = true;
    }
  } catch (e) {
    snackBarMsg.value = `${e}`;
    openSnackBar.value = true;
  } finally {
    isLoading.value = false;
  }
}

watch(phone, (value) => {
  var newValue = value;
  phone.value = newValue;

  const removePhoneFormat = `${phone.value}`.getPhoneNumber(); // Remove Phone Format


  if (
    verifiedPhoneNumber.value != "" &&
    verifiedPhoneNumber.value == removePhoneFormat
  ) {
    verifiedOtp.value = true;

  }

  // Need to validate again if OTP is verified and changed again
  if (verifiedOtp.value && verifiedPhoneNumber.value != removePhoneFormat) {
    verifiedOtp.value = false;
  }
});
</script>

<template>
  <AppBarPlus :title="t('my_profile')" :back="true" />
  <!-- Dialog -->
  <DialogPlus persistent v-model="openDialog" :confirmText="t('okay')" :onClickConfirm="() => {
    openDialog = false;
  }
    ">
    {{ dialogMsg }}
  </DialogPlus>
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>
  <!-- AppBarPlus Spacing -->
  <div style="height: 70px" />
  <!-- Loading Plus -->
  <LoadingPlus v-if="isLoading" />
  <div class="my-profile">
    <VForm class="my-profile-container" @submit.prevent="onSubmit" ref="refForm">
      <!-- Change profile picture -->
      <div class="profile-image-title px-5">
        {{ t("change_profile_picture") }}
      </div>
      <div style="height: 10px" />
      <div class="my-profile-image pointer" @click="openUploadSheet = true">
        <VImg :src="profileImage || placeholder" rounded="circle" height="150" width="150" :cover="true" />
        <div class="edit-icon-container">
          <VIcon size="14">mdi-pencil-outline</VIcon>
        </div>
      </div>

      <div style="height: 20px" />

      <!-- Name -->
      <div class="px-5">
        <TextFieldPlus :title="t('name')" v-model="name" :placeholder="t('name')" :rules="[ValidationHelper.required]"
          required />
      </div>

      <div style="height: 20px" />

      <!-- Phone Number -->
      <div class="px-5">
        <div class="profile-phone-title">
          {{ t("phone_number") }}
          <span class="text-required-field">*</span>
        </div>
        <div style="height: 5px" />

        <div class="flex item-center">
          <TelInputPlus v-model="phone" @validate="
            (value) => {
              verifiedOtp = value.valid;
            }
          " style="width: 100%; max-height: 48px" />
          <div style="width: 10px" />
          <ButtonPlus v-if="!verifiedOtp" @click="sentOtp()">{{ t("get_otp") }}
          </ButtonPlus>
          <div v-if="verifiedOtp" class="otp-verified" :style="{ 'background-color': ColorPick.successColor }">
            <!-- v-if="!smAndDown" -->
            <span>{{ t("verified") }}</span>
            <VIcon icon="mdi-check" size="20" />
          </div>
        </div>
      </div>

      <div style="height: 20px" />

      <!-- Email Address -->
      <div class="px-5">
        <TextFieldPlus :title="t('email_address')" v-model="email" :placeholder="t('email_address')"
          :rules="[ValidationHelper.required, ValidationHelper.email]" required />
      </div>

      <div style="height: 20px" />

      <!-- DOB -->
      <div class="px-5">
        <TextFieldPlus :title="t('date_of_birth')" v-model="dob" :placeholder="t('date_of_birth')" readonly />
      </div>

      <div style="height: 50px" />

      <!-- Save Profile -->
      <div class="px-5">
        <ButtonPlus @click="onSubmit()" width="100%">{{
          t("save_profile")
          }}</ButtonPlus>
      </div>

      <div style="height: 30px" />
    </VForm>
  </div>

  <!-- Upload image -->
  <BottomSheetPlus v-model="openUploadSheet" :title="t('change_profile_picture')" :confirm-text="t('confirm')"
    :onClickConfirm="() => (openUploadSheet = false)">
    <UploadPlus @update:modelValue="handleImageUpload" />
  </BottomSheetPlus>

  <!-- OTP Dialog -->
  <DialogPlus v-model="openOTPDialog" :onClickConfirm="verifyOtpSubmit" :title="t('otp_validation')">
    <!-- Enter otp -->
    <div class="otp-description">
      {{ t("otp_description").replace("[phone_number]", phone) }}
    </div>
    <div style="height: 20px" />

    <div>
      <VOtpInput v-model="otp" length="6"></VOtpInput>
      <div class="otp-resend-text">
        {{ t("didnt_receive_code") }}
        <a class="resend-text" v-if="!isTimerRunning" @click="sentOtp()">{{
          t("resend")
          }}</a>
        <span v-else>
          {{ formatTime(timeRemaining) }}
        </span>
      </div>
    </div>
  </DialogPlus>

  <!-- Success Dialog -->
  <DialogPlus v-model="openSuccessDialog" :onClickConfirm="() => (openSuccessDialog = false)">
    <div class="success-dialog">
      <VImg :src="success" cover height="85" width="85" />

      <div style="height: 30px" />

      <div class="dialog-title" :style="{ color: ColorPick.fontColor }">
        {{ t("profile_saved_successfully") }}
      </div>
    </div>
  </DialogPlus>
</template>

<style lang="scss" scoped>
.flex {
  display: flex;
}

.item-center {
  align-items: center;
}

.pointer {
  cursor: pointer;
}

.px-5 {
  padding: 0 5px;
}

.my-profile {
  width: 100%;
  display: inline-table;
  text-align: -webkit-center;
}

.my-profile-container {
  max-width: 600px;
}

.profile-image-title,
.profile-phone-title {
  text-align: left;
  font-size: 16px;
  font-weight: 600;
}

.my-profile-image {
  position: relative;
  max-width: 150px;
}

.edit-icon-container {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 0 5px;
  border-radius: 100%;
  background-color: v-bind("ColorPick.secondaryDarkenColor");
}

.success-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.dialog-title {
  font-size: 24px;
  font-weight: 600;
}

.otp-resend-text {
  text-align: center;
  padding: 10px;
  font-size: 14px;
  font-weight: 500;
}

.resend-text {
  color: #693915;
  background: linear-gradient(to right, #693915, #693915) no-repeat;
  background-size: 0 2px;
  background-position: right bottom;
  transition: background-size 0.3s;
}

.resend-text:hover {
  background-size: 100% 2px;
  background-position: left bottom;
}

.otp-verified {
  display: flex;
  gap: 5px;
  color: #fff;
  padding: 10px;
  border-radius: 6px;
}
</style>
