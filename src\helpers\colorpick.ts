class ColorPick {
  //Theme Color
  static backgroundColor: string = "#ffffff";
  static primaryColor: string = "#201747";
  static primaryDarkenColor: string = "#472100";
  static secondaryColor: string = "#F7F5F3";
  static secondaryDarkenColor: string = "#E3DDD6";
  static surfaceColor: string = "#ffffff";
  static surfaceDarkColor: string = "#000000";
  static onBackgroundColor: string = "#0A0A0C";
  static onBackgroundDarkColor: string = "#f4f5fa";
  static onSurfaceColor: string = "#757474";
  static fontColor: string = "#000000";
  static fontColorWhite: string = "#ffffff";
  static dividerColor: string = "#b6b2bf";

  static appBarTitleColor: string = "#1f1744";

  static errorColor: string = "#FF0000";

  static successColor: string = '#52b963';

  static buttonColorAuth = "#D08550";
  static splashScreenColor: string = "#F7F5F3";

  // Home Page
  static homeOrderStatusColor: string = "#AFE1E3";
  static homeMembershipUserName: string = "#F3BD2F";
  static homeMembershipProgressBar: string = "#F3BD2F";
  static homeAarrowColor: string = "#aaa5b5";


  //Dialog
  static dialogDescription: string = "#334155";

  // Badge
  static badgeColor: string = "#EE4B2B";
  static iconColor: string = "#000";

  // Shop
  static shopHeaderColor: String = "#FFFDF9";
  static productPriceColor: String = "#472100";
  static inactiveBundleBorderColor: String = "#E7E7E7";
  static inactiveBundleBgColor: String = "#FBFBFC";
  static inactiveTierColor: String = "#A2A2A2";
  static counterHoverBgColor: String = "#ECECEC";
  static orderItemHeadBgColor = "#D08550";
  static shareIconColor: string = "#757575";
  static wishlistIconColor: string = "#757575";

  // Order
  static sectionBorderColor: String = "#d6d6d6cc";
  static editFontColor: String = "#375DFB";
  static voucherExpireColor: String = "#EB2127";
  static voucherValidUntilColor: String = '#4B4B4B';
  static deleteFontColor: String = "#EB2127";
  static greyFontColor01: String = "#8a8a8a";
  static greyFontColor02: String = "#4b4b4b";
  static greyFontColor03: String = "#5F5E5E";
  static greyFontColor04: String = "#313131";
  static greyFontColor05: String = "#757575";
  static greyFontColor06: String = "#EBEBEB";
  static greyFontColor07: String = "#424242";
  static orderFontColor01: String = "#4B4B4B";

  static darkBlueFontColor01: String = "#344054";
  static darkBlueFontColor02: String = "#0D1217";

  static memberCardColor1: string = "#A95104";
  static memberCardItemTitle: string = "#7E7E7E";
  static memberCardItemSubTitle: string = "#5B5250";
  static homeSeeAllColor: string = "#5F5E5E";

  // FAQ
  static faqIconColor: string = '#D08550'
  static faqTitleColor: string = '#472100'

  // Settings
  static settingsTitleFontColor: string = '#656565'

  //Rewards
  static rewardsBorderColor: String = "#0000000A";
  static rewardsLabelColor: String = "#D6D6D6";
  static rewardsPointHistoryPositive: string = "#1EA319"
  static rewardsPointHistoryNegative: string = "#E0251B"


  //Status
  static statusActive: String = '#1EA319'
  static statusExpired: String = '#A8A8A8'

  // Referral Program
  static referraProgramBackgroudColor: string = '#FFFCF5'
  static referralLinkButton: string = '#D08550'

  //Membership card
  static memberProgressBarActive: string = "#201747";
  static memberProgressBarInActive: string = "#2017474D";


}

export default ColorPick;
