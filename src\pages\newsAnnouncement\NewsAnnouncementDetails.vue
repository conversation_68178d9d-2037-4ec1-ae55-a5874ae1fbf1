<script setup lang="ts">
// Helpers
import ColorPick from "@/helpers/colorpick";
import { ref, onMounted, onUnmounted } from "vue";
import { useI18n } from "vue-i18n";

// Images
import placeholder from "@/assets/icons/logo.png";

// Components
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";

// Routers
import { useRoute, useRouter } from "vue-router";

// Dtos
import type { NewsAnnouncementDto } from "@/dto/news_announcement.dto";

// Apis
import NewsAnnouncementApi from "@/services/news_announcement_api";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const isLoading = ref<boolean>(false);
const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>("");

const data = ref<NewsAnnouncementDto>();

// Fetch Screen Width
const screenWidth = ref(window.innerWidth);

const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth;
};

async function loadNewsAnnouncementDetail() {
  try {
    isLoading.value = true;

    data.value = await NewsAnnouncementApi.detail(route.params.id as string);
  } catch (error) {
    dialogMsg.value = `${error}`;
    openDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}

function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

onMounted(() => {
  window.scrollTo(0, 0);
  window.addEventListener("resize", updateScreenWidth);

  loadNewsAnnouncementDetail();
});

onUnmounted(() => {
  window.removeEventListener("resize", updateScreenWidth);
});
</script>

<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading" />

  <AppBarPlus :title="t('news_announcement_details')" :back="true" />

  <!-- AppBarPlus Spacing -->
  <div style="height: 60px" />

  <div class="news-announcement-detials">
    <div
      class="news-announcement-details-container"
      :class="screenWidth < 615 ? '' : 'pad'"
      :style="{ width: `${screenWidth}px` }"
    >
      <div class="news-image">
        <VImg
          :src="data?.image_url[0] ?? placeholder"
          :rounded="screenWidth < 615 ? 0 : 'lg'"
          maxWidth="615"
          :cover="true"
        />
      </div>

      <div v-if="data?.image_url" style="height: 20px" />

      <div :class="screenWidth < 615 ? 'pad' : ''">
        <div v-if="data?.created_at" class="news-announcement-datetime">
          {{ data?.created_at }}
        </div>

        <div v-if="data?.title" style="height: 10px" />

        <div v-if="data?.title" class="news-announcement-title">
          {{ data?.title }}
        </div>

        <div v-if="data?.content" style="height: 30px" />

        <div
          v-if="data?.content"
          class="news-announcement-descrtiption"
          v-html="data?.content"
        />
      </div>

      <div style="height: 20px" />

      <div class="news-image">
        <VImg
          v-for="image in data?.image_url.slice(1)"
          :src="image ?? placeholder"
          :rounded="screenWidth < 615 ? 0 : 'lg'"
          maxWidth="615"
          class="mb-5"
          :cover="true"
        />
      </div>
    </div>
  </div>

  <!-- Dialog -->
  <DialogPlus
    persistent
    v-model="openDialog"
    :confirmText="t('back')"
    :onClickConfirm="
      () => {
        openDialog = false;
        goBack();
      }
    "
  >
    {{ dialogMsg }}
  </DialogPlus>
</template>

<style lang="scss" scoped>
.pad {
  padding: 5px 10px;
}

.news-announcement-detials {
  width: 100%;
  display: inline-table;
  text-align: -webkit-center;
}

.news-announcement-details-container {
  max-width: 600px;
}

.news-image {
  max-width: 600px;
  width: "100%";
}

.news-announcement-title {
  font-size: 20px;
  font-weight: 600;
  text-align: left;
  color: v-bind("ColorPick.fontColor");
}

.news-announcement-datetime {
  font-size: 14px;
  font-weight: 500;
  text-align: left;
  color: v-bind("ColorPick.fontColor");
}

.news-announcement-descrtiption {
  font-size: 14px;
  font-weight: 500;
  text-align: left;
  color: v-bind("ColorPick.fontColor");
}
</style>
