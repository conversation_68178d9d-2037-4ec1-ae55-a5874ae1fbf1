import AppConfig from "@/appconfig";
import { FaqsDto } from "@/dto/faqs.dto";
import { GeneralPageDto } from "@/dto/general_page.dto";
import { PaymentMethod, PaymentMethodDto } from "@/dto/paymentMethod.dto";
import { SettingDto } from "@/dto/setting.dto";
import ApiHelper from "@/helpers/api_helper";

class GeneralPageApi {
  //Get Privacy Policy
  public static async getPrivacyPolicy(): Promise<GeneralPageDto> {
    try {
      var response = await ApiHelper.post(AppConfig.apiPageUrl, undefined, {
        type: "privacy-policy",
      });

      const tempPrivacyPolicy: GeneralPageDto = new GeneralPageDto(
        response.data.data
      );

      return tempPrivacyPolicy;
    } catch (error) {
      throw error;
    }
  }

  //Get About Us
  public static async getAboutUs(): Promise<GeneralPageDto> {
    try {
      var response = await ApiHelper.post(AppConfig.apiPageUrl, undefined, {
        type: "about-us",
      });
      const tempAboutUs: GeneralPageDto = new GeneralPageDto(
        response.data.data
      );

      return tempAboutUs;
    } catch (error) {
      throw error;
    }
  }

  //Get Terms And Conditions
  public static async getTermsAndConditions(): Promise<GeneralPageDto> {
    try {
      var response = await ApiHelper.post(AppConfig.apiPageUrl, undefined, {
        type: "terms-of-use",
      });

      const tempTermsAndConditions: GeneralPageDto = new GeneralPageDto(
        response.data.data
      );

      return tempTermsAndConditions;
    } catch (error) {
      throw error;
    }
  }

  //Get Faqs
  public static async getFaqs(): Promise<FaqsDto[]> {
    try {
      var response = await ApiHelper.post(AppConfig.apiFaqsUrl);

      const tempFaq: FaqsDto[] = response.data.data.map(
        (item: any) => new FaqsDto(item)
      );

      return tempFaq;
    } catch (error) {
      throw error;
    }
  }

  //Get State List
  public static async getStateList(): Promise<any[]> {
    try {
      var response = await ApiHelper.get(AppConfig.apiStateListUrl);
      // Extract the first object inside "data" array

      const rawStates = response.data.data[0];

      return rawStates;
    } catch (error) {
      throw error;
    }
  }

  //Get Country List
  public static async getCountryList(): Promise<any[]> {
    try {
      var response = await ApiHelper.get(AppConfig.apiCountryListUrl);
      // Extract the first object inside "data" array

      const rawStates = response.data.data;

      return rawStates;
    } catch (error) {
      throw error;
    }
  }

  //Get Settings
  public static async getSettings(): Promise<SettingDto> {
    try {
      var response = await ApiHelper.post(AppConfig.apiSettingsUrl);
      const tempSetting: SettingDto = new SettingDto(response.data.data);

      return tempSetting;
    } catch (error) {
      throw error;
    }
  }

  //Get Payment Methods
  public static async getPaymentMethods(): Promise<PaymentMethodDto[]> {
    try {
      var response = await ApiHelper.get(AppConfig.apiPaymentMethodsUrl);

      var tempPaymentMethods = PaymentMethod.transformedPaymentMethodData(response.data.data);

      const tempPaymentMethodDto: PaymentMethodDto[] = tempPaymentMethods.map(
        (item: any) => new PaymentMethodDto(item)
      );

      return tempPaymentMethodDto;
    } catch (error) {
      throw error;
    }
  }
}

export default GeneralPageApi;

