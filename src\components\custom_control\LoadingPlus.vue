<script setup lang="ts">
import LoadingPlusItem from './LoadingPlusItem.vue';

interface Props {
    loaderWidth?: number,
    loaderSized?: number,
    loaderSpeed?: number,
}

const props = defineProps<Props>();



</script>

<template>
    <div class="loading-overlay">
        <LoadingPlusItem :loaderSized="props.loaderSized" :loaderSpeed="props.loaderSpeed"></LoadingPlusItem>
    </div>
</template>

<style lang="scss">
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}
</style>