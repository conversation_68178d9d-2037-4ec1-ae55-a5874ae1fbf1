import AppConfig from "@/appconfig";

export class ProductDto {
  id: number;
  name: string;
  product_weight: number;
  online_order: boolean;
  products?: ProductDataDto[];

  constructor(data: any) {
    let tempProductData: ProductDataDto[] | undefined;
    try {
      tempProductData = data.products.map(
        (item: any) => new ProductDataDto(item)
      );
    } catch (_) {}

    this.id = data.id;
    this.name = data.name;
    this.product_weight = data.product_weight;
    this.online_order = data.online_order;
    this.products = tempProductData;
  }
}

export class ProductCategory {
  id?: number;
  name: string;
  is_active: boolean;
  product_weight: number;
  online_order: number;
  created_at: string;
  updated_at: string;

  constructor(data: any) {
    this.id = data.id;
    this.name = data.name;
    this.is_active = data.is_active;
    this.product_weight = data.product_weight;
    this.online_order = data.online_order;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }
}

export class ProductDataDto {
  id: number;
  name: string;
  product_category_id: number;
  product_subcategory_id: number;
  is_variant: boolean;
  sku: string;
  weight: number;
  price: string;
  short_desc: string;
  desc: string;
  quantity: number;
  url: string;
  is_saleable: boolean;
  is_out_of_stock: boolean;
  is_active: boolean;
  currency: string;
  image: string;
  item_count: number;
  product_subcategory?: ProductCategory;
  in_cart?: InCartDto;

  constructor(data: any) {
    this.id = data.id;
    this.name = data.name;
    this.product_category_id = data.product_category_id;
    this.product_subcategory_id = data.product_subcategory_id;
    this.is_variant = data.is_variant;
    this.sku = data.sku;
    this.weight = data.weight;
    this.short_desc = data.short_desc;
    this.desc = data.desc;
    this.quantity = data.quantity;
    this.url = data.url;
    this.is_saleable = data.is_saleable;
    this.is_out_of_stock = data.is_out_of_stock;
    this.price = data.price;
    this.currency = data.currency ?? AppConfig.currencyMark;
    this.image = data.image ?? data.single_image_url;
    this.is_active = data.is_active;
    this.item_count = data.item_count ?? 0;
    try {
      this.product_subcategory = new ProductCategory(data.product_subcategory);
    } catch (_) {}
    try {
      this.in_cart = new InCartDto(data.in_cart);
    } catch (_) {}
  }
}

export class InCartDto{
 cart_product_id: number;
 quantity: number;

 constructor(data: any){
  this.cart_product_id = data.cart_product_id;
  this.quantity = data.quantity;
 }
}