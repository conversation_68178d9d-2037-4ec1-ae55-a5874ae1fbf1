import AppConfig from "@/appconfig";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/helpers/api_helper";
import PointTypeEnum from "@/enums/PointTypeEnum";
import { PointDto } from "@/dto/point.dto";
import { PaginationDto } from "@/dto/pagination.dto";

class PointsApi {
  public static async list(
    page: number,
    perPage?: number,
  ): Promise<PaginationDto> {
    try {
      var response = await ApiHelper.post(AppConfig.apiPointsUrl, undefined, {
        page: page,
        per_page: perPage ?? AppConfig.paginationLimit,
      });

      const tempPoints: PaginationDto = new PaginationDto(response.data.data);
      
      return tempPoints;
    } catch (error) {
      throw error;
    }
  }
}

export default PointsApi;
