<script setup lang="ts">
import ColorPick from "@/helpers/colorpick";
import RewardsItemBackground from "@/assets/images/rewards_item_background.jpeg";
import { useI18n } from "vue-i18n";
import ThousandsHelper from "@/helpers/thousands_helper";

// Images
import redeemWithIcon from "@/assets/icons/redeem_with_icon.png";
import rewardsIcon2 from "@/assets/icons/rewards_icon2.png";

// Language
const { t } = useI18n();

interface Props {
  image?: string | undefined;
  reward?: string | undefined;
  redeemWith?: string | undefined;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "click"): void;
}>();

const onClick = () => {
  emit("click");
};
</script>

<template>
  <div class="reward-card" @click="onClick">
    <div
      class="reward-image"
      :style="{
        backgroundImage: `url(${props.image || RewardsItemBackground})`,
      }"
    ></div>
    <div class="reward-info">
      <div class="reward-meta">
        <div class="redeem-with" :style="{ flex: 1 }">
          <div class="redeem-top">
            <img :src="redeemWithIcon" alt="Redeem with" class="icon" />
            <div class="redeem-text">
              <span class="label">{{ t("redeem_with") }}</span>
              <div style="height: 3px"></div>
              <span class="value">{{ props.redeemWith }} {{ t("pts") }}</span>
            </div>
          </div>
        </div>
        <div class="reward-amount" :style="{ flex: 1 }">
          <div class="reward-top">
            <img :src="rewardsIcon2" alt="Rewards" class="icon" />
            <div class="reward-text">
              <span class="label">{{ t("rewards") }}</span>
              <div style="height: 3px"></div>
              <span class="value">{{ props.reward }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.reward-card {
  width: 100%;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  display: flex;
  flex-direction: column;
}

.reward-image {
  aspect-ratio: 11/4;
  background-size: cover;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.reward-info {
  background: #ffffff;

  display: flex;
  flex-direction: column;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  text-align: left;
}

.reward-meta {
  display: flex;
  // gap: 20px;
  align-items: center;
  justify-content: left;
  // justify-content: space-between;
  width: 100%;
}

.redeem-with,
.reward-amount {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 10px;
  justify-content: center;
}

.redeem-top,
.reward-top {
  display: flex;
  align-items: start;
  gap: 8px;
}

.redeem-text,
.reward-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1.2;
}

.label {
  font-size: 12px;
  color: v-bind("ColorPick.rewardsLabelColor");
}

.value {
  font-size: 13px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}

.icon {
  width: 15px;
  height: 15px;
}
</style>
