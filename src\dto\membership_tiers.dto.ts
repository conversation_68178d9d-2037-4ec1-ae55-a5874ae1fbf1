import moment from "moment";

export class MembershipTiersDto {
    id?: number;
    description: string;
    name: string;
    next_level_name: string;
    points_expiring: number;
    points_expiring_date: Date;

    constructor(data: any) {
        this.id = data.id;
        this.description = data.description?.toString() ?? '';
        this.name = data.name?.toString() ?? '';
        this.points_expiring = data.points_expiring;
        this.next_level_name = data.next_level_name;
        this.points_expiring_date = moment(data.points_expiring_date, "YYYY-MM-DD").toDate();
    }
}