<script setup lang="ts">
import { translate } from "../../plugins/i18n";
import ColorPick from "@/helpers/colorpick";
interface Props {
  title?: string | undefined;
  persistent?: boolean;
  confirmText?: string;
  cancelText?: string;
  onClickConfirm?: () => void;
  onClickCancel?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  persistent: false,
  confirmText: translate("okay"),
  cancelText: translate("cancel"),
});
const model = defineModel<boolean>();
</script>

<template>
  <VDialog
    v-model="model"
    max-width="600px"
    scrollable
    :persistent="persistent"
  >
    <VCard class="dialog-plus">
      <VIcon
        v-if="!persistent"
        class="dialog-plus-close ri-close-line"
        size="35"
        @click="model = false"
      ></VIcon>
      <VCardTitle v-if="props.title">
        <div class="dialog-plus-title">
          {{ props.title }}
        </div>
        <VDivider
          tickness="2"
          :style="{ color: ColorPick.fontColor, opacity: 0.2 }"
        />
      </VCardTitle>
      <VCardText
        :style="{
          textAlign: 'center',
          whiteSpace: 'pre-line',
        }"
      >
        <div
          :style="{
            padding: '20px',
          }"
        >
          <slot></slot>
        </div>
      </VCardText>
      <VCardActions v-if="onClickConfirm" class="dialog-plus-actions">
        <ButtonPlus @click="onClickConfirm" variant="elevated">
          {{ confirmText }}
        </ButtonPlus>
        <ButtonPlus
          v-if="onClickCancel"
          @click="onClickCancel"
          variant="outlined"
        >
          {{ cancelText }}
        </ButtonPlus>
      </VCardActions>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
.dialog-plus {
  border-radius: 20px !important;
  max-width: 600px !important;
  padding: 20px !important;
}

.dialog-plus-title {
  font-size: 23px;
  font-weight: 700;
  text-align: center;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding: 5px;
}

.dialog-plus-close {
  position: absolute;
  right: 15px;
  top: 10px;
}

.dialog-plus-actions {
  display: flex;
  justify-content: start;
  // block-size: 60px;
  inline-size: 100%;
  padding: 20px;
}

.dialog-plus-actions .v-btn {
  flex: 1;
}
</style>
