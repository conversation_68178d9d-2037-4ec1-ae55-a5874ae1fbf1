<script setup lang="ts">
import { VPullToRefresh } from "vuetify/labs/VPullToRefresh";

// Components
import ButtonPlus from "../custom_control/ButtonPlus.vue";
import AddressDetails from "./AddressDetails.vue";

// Helpers
import ColorPick from "@/helpers/colorpick";
import CapitalizeHelper from "@/helpers/capitalize_helper";
import RoutersHelper from "@/helpers/routes_helper";
import { useI18n } from "vue-i18n";
import { onMounted, onUnmounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import { AxiosError } from "axios";

// Dtos
import { AddressDto } from "@/dto/address.dto";

// Apis
import AddressApi from "@/services/address_api";
import NoData from "../custom_control/NoData.vue";
import { de } from "vuetify/locale";

interface Props {
  canSelect?: boolean;
  canRoute?: boolean;
  onSelect?: (id: string) => void;
}

const props = withDefaults(defineProps<Props>(), {
  canSelect: false,
  canRoute: true,
});

//Handle Route
const route = useRoute();
const router = useRouter();

// Language
const { t } = useI18n();

const isLoading = ref<boolean>(false);

const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

const page = ref<number>(1);
const hasMore = ref(true);
let doneCopy: (status: "error" | "loading" | "empty" | "ok") => void;
const pullDownThreshold = ref(60);

const openDeleteDialog = ref<boolean>(false);
const showDetails = ref<boolean>(false);

const detailsId = ref<string>();
const deleteAddressId = ref<string>();

//data
const addressList = ref<AddressDto[]>([]);

//Handle Show Details
function checkShowDetails() {
  if (props.canRoute) {
    if (
      route.params.id !== undefined &&
      route.params.id !== null &&
      route.params.id !== ""
    ) {
      if (route.params.id === "new") {
        onClickDetails();
      } else {
        onClickDetails(route?.params?.id as string);
      }
    } else {
      showDetails.value = false;
    }
  }
}

function onClickDetails(id?: string) {
  detailsId.value = undefined;

  if (props.canRoute) {
    //If have id it means edit
    if (id !== undefined && id !== null && id !== "") {
      detailsId.value = id;
      router.replace(`${RoutersHelper.address}/${id}`);
    } else {
      router.replace(`${RoutersHelper.address}/new`);
    }
  } else {
    detailsId.value = id;
  }
  showDetails.value = true;
}

// Fetch Address List
async function loadAddressList() {
  const response = await AddressApi.list(page.value);
  return response;
}

// Address List - Infinite Scroll
async function infiniteload({
  done,
}: {
  done: (status: "error" | "loading" | "empty" | "ok") => void;
}) {
  if (!hasMore.value) {
    done("empty");
    return;
  }

  try {
    const res = await loadAddressList();

    if (res.data !== undefined) {
      if (page.value <= 1) {
        addressList.value = [];
      }

      addressList.value.push(...res.data);

      const lastPage = res.last_page ?? 1;

      if (page.value >= lastPage) {
        hasMore.value = false;

        // For refresh the page
        doneCopy = done;
      } else {
        hasMore.value = true;

        page.value += 1;
      }
    } else {
      hasMore.value = false;
      done("empty");
      doneCopy = done;
    }
  } catch (e) {
  } finally {
    done("ok");
  }
}

async function deleteAddress() {
  //If Loading will no do anything
  if (isLoading.value) {
    snackBarMsg.value = t("please_wait...");
    openSnackBar.value = true;
    return;
  }

  try {
    isLoading.value = true;

    var result = await AddressApi.delete(deleteAddressId.value ?? "");

    if (result) {
      snackBarMsg.value = t("address_deleted_successfully");
      //Delete in List
      addressList.value = addressList.value.filter(
        (item) => `${item.id}` !== deleteAddressId.value
      );
    } else {
      snackBarMsg.value = result;
    }

    openDeleteDialog.value = false;
    openSnackBar.value = true;
  } catch (e) {
    if (e instanceof AxiosError) {
      var message: string =
        e.response?.data?.message ?? e["message"] ?? e.toString();

      snackBarMsg.value = message.toString();
      openSnackBar.value = true;
    } else if (e instanceof Error) {
      snackBarMsg.value = e.message.toString();
      openSnackBar.value = true;
    } else {
      snackBarMsg.value = t("oops");
      openSnackBar.value = true;
    }
  } finally {
    isLoading.value = false;
    loadAddressList();
  }
}

async function updateAddress(id: string): Promise<void> {
  try {
    isLoading.value = true;

    let response: AddressDto = await AddressApi.details(id);

    if (response) {
      // Update in list if have same id
      const addressListIndex = addressList.value.findIndex(
        (item) => `${item.id}` === id
      );
      if (addressListIndex !== -1) {
        addressList.value[addressListIndex] = response;
      }
    }
  } catch (_) {
  } finally {
    isLoading.value = false;
  }
}

// On Refresh
async function refresh({ done }: { done?: () => void }) {
  try {
    page.value = 1;
    hasMore.value = true;

    doneCopy("ok");
    await infiniteload({ done: doneCopy });
  } catch (_) {
  } finally {
    if (done) {
      done();
    }
  }
}

onMounted(() => {
  checkShowDetails();
});

onUnmounted(() => {});

watch(showDetails, () => {
  if (!showDetails.value) {
    if (detailsId.value !== undefined) {
      updateAddress(detailsId.value);
    } else {
      addressList.value = [];
      refresh({});
    }
  }
});
</script>

<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading" />
  <!-- Snack Bar -->
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>
  <!-- <AppBarPlus :title="t('address')" :back="true" :currentPage="page" /> -->

  <VAppBar v-if="!props.canSelect" elevation="0">
    <VBtn
      :color="ColorPick.fontColor"
      @click="
        () => {
          router.push(RoutersHelper.settings);
        }
      "
    >
      <VIcon class="ri-arrow-left-s-line"></VIcon>
    </VBtn>
    <VAppBarTitle
      :style="{
        color: ColorPick.fontColor,
        textAlign: 'left',
        fontFamily: `'Nunito Sans', sans-serif`,
      }"
    >
      {{ t("address") }}
    </VAppBarTitle>
  </VAppBar>

  <!-- AppBarPlus Spacing -->
  <div v-if="!props.canSelect" style="height: 80px"></div>
  <div v-else style="height: 20px"></div>

  <!-- Address Details -->
  <AddressDetails
    v-model="showDetails"
    :id="detailsId"
    :canRoute="props.canRoute"
    :refreshData="loadAddressList"
  />

  <!-- Logout Dialog -->
  <DialogPlus
    v-model="openDeleteDialog"
    :title="t('address')"
    :confirmText="t('yes')"
    :cancelText="t('no')"
    :onClickConfirm="deleteAddress"
    :onClickCancel="
      () => {
        openDeleteDialog = false;
      }
    "
  >
    {{ t("are_you_sure_you_want_to_delete") }}
  </DialogPlus>
  <div class="address">
    <div :style="{ textAlign: 'right' }">
      <ButtonPlus
        @click="
          () => {
            onClickDetails();
          }
        "
        variant="outlined"
      >
        {{ t("new_address") }}
      </ButtonPlus>
    </div>

    <VPullToRefresh @load="refresh" :pullDownThreshold="pullDownThreshold">
      <VContainer style="padding: 0">
        <VInfiniteScroll
          :height="'100%'"
          :items="addressList"
          @load="infiniteload"
          v-model="hasMore"
        >
          <template v-slot:empty>
            <NoData v-if="addressList?.length <= 0" />
            <span v-else>{{ t("no_more") }}</span>
          </template>

          <div
            v-if="
              addressList !== undefined &&
              addressList !== null &&
              addressList.length > 0
            "
            v-for="item in addressList"
            :key="item.id"
          >
            <div class="address-item-container">
              <div class="address-item">
                <div
                  class="address-item-label"
                  :style="{ fontSize: props.canSelect ? '12px' : '16px' }"
                >
                  {{ item.recipient }} - ({{ item.label }})
                </div>
                <div
                  class="address-item-address"
                  :style="{ fontSize: props.canSelect ? '11px' : '14px' }"
                >
                  {{
                    [
                      item.address,
                      `${item.postcode} ${item.city}`,
                      CapitalizeHelper.capitalize(item.state),
                    ].join(", ")
                  }}
                </div>
              </div>
              <div :style="{ display: 'flex', alignItems: 'anchor-center' }">
                <div :style="{ width: '5px' }"></div>
                <div
                  @click="
                    () => {
                      onClickDetails(item.id.toString());
                    }
                  "
                  class="address-icon-btn"
                  background="none"
                >
                  <VIcon
                    icon="mdi-pencil"
                    size="20px"
                    :color="ColorPick.fontColor"
                  />
                </div>
                <div
                  @click="
                    () => {
                      openDeleteDialog = true;
                      deleteAddressId = item.id.toString();
                    }
                  "
                  class="address-icon-btn"
                  background="none"
                >
                  <VIcon
                    icon="mdi-delete"
                    :size="20"
                    :color="ColorPick.errorColor"
                  />
                </div>
                <div v-if="canSelect" :style="{ width: '10px' }"></div>
                <ButtonPlus
                  class="select-button"
                  v-if="canSelect"
                  @click="
                    () => {
                      if (props.onSelect) {
                        props.onSelect(item.id.toString());
                      }
                    }
                  "
                >
                  {{ t("select") }}
                </ButtonPlus>
              </div>
            </div>
            <VDivider />
          </div>
        </VInfiniteScroll>
      </VContainer>
      <div style="height: 50px" />
    </VPullToRefresh>
  </div>
</template>

<style lang="scss" scoped>
.address {
  width: 100%;
  max-width: 600px;
  height: 100%;
  text-align: -webkit-center;
  margin: 0 auto;
  /* Center horizontally */
}

.address-icon-btn {
  cursor: pointer;
  padding: 5px;
}

.address-item-container {
  display: flex;
}

.address-item {
  flex: 1;
  padding: 10px 0 10px 0;
  height: 80px;
  overflow: hidden;

  .address-item-label {
    width: 100%;
    // font-size: 16px;
    font-weight: 600;
    text-align: left;
    color: v-bind("ColorPick.fontColor");
    max-lines: 1;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    overflow-wrap: break-word;
  }

  .address-item-address {
    width: 100%;
    // font-size: 14px;
    font-weight: 400;
    text-align: left;
    color: v-bind("ColorPick.greyFontColor02");
    max-lines: 2;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    overflow-wrap: break-word;
  }
}

.address-item-edit {
  flex: 1;
  padding: 10px 0 10px 0;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dialog-pagination {
  width: 100%;
}

.pagination {
  max-width: 600px;
  width: 100%;
  position: fixed;
  bottom: 0;
  background-color: v-bind("ColorPick.backgroundColor");
  padding: 10px 0;
}

.select-button {
  font-size: 10px;
  font-weight: bold;
  padding: 2px 10px;
  min-width: unset;
  height: 30px;
  border-radius: 2px;
}
</style>
