import AppConfig from "@/appconfig";
import { BundleDto } from "./bundle.dto";
import { ProductCategory } from "./product.dto";

export class ProductDetailsDto {
  id: number;
  name: string;
  product_category_id: number;
  product_subcategory_id: number;
  is_variant: boolean;
  sku: string;
  weight: number;
  price: number;
  desc: string;
  quantity: number;
  url: string;
  is_saleable: boolean;
  is_out_of_stock: boolean;
  is_active: boolean;
  currency: string;
  is_wishlist: number;
  image_url: string[];
  item_count: number;
  product_category?: ProductCategory;
  product_subcategory?: ProductCategory;

    constructor(data: any) {
    try {
      this.image_url = data.image_url.map((item: any) => item.toString());
    } catch (_) {
      this.image_url = [];
    }
    this.id = data.id;
    this.name = data.name;
    this.product_category_id = data.product_category_id;
    this.product_subcategory_id = data.product_subcategory_id;
    this.is_variant = data.is_variant;
    this.sku = data.sku;
    this.weight = data.weight;
    this.desc = data.desc;
    this.quantity = data.quantity;
    this.url = data.url;
    this.is_saleable = data.is_saleable;
    this.is_out_of_stock = data.is_out_of_stock;
    this.price = data.price;
    this.currency = data.currency ?? AppConfig.currencyMark;
    this.is_active = data.is_active;
    this.item_count = data.item_count ?? 1;
    this.is_wishlist = data.is_wishlist;
    this.product_category = data.product_category
    ? new ProductCategory(data.product_category)
    : new ProductCategory({});
    this.product_subcategory = data.product_subcategory
    ? new ProductCategory(data.product_subcategory)
    : new ProductCategory({});
  }
}

export class VariationDto {
  id: number;
  name: string;
  sku: string;
  image?: string;
  price: PriceDto[];
  count: number;

  constructor(data: any) {
    try {
      this.price = data.price.map((item: any) => new PriceDto(item));
    } catch (_) {
      this.price = [];
    }

    this.id = data.id;
    this.name = data.name;
    this.sku = data.sku;
    this.image = data.image;
    this.price = data.price;
    this.count = 0;
  }
}

export class PriceDto {
  current_tier: boolean;
  tier_id: number;
  tier_name: string;
  regular_price: string;
  discounted_price: string;
  discount_value: string;
  discount_type: string;

  constructor(data: any) {
    this.current_tier = data.current_tier;
    this.tier_id = data.tier_id;
    this.tier_name = data.tier_name;
    this.regular_price = data.regular_price;
    this.discounted_price = data.discounted_price;
    this.discount_value = data.discount_value;
    this.discount_type = data.discount_type;
  }
}
