class AppConfig {
  static backendUrl: string =
    import.meta.env.BACKEND_URL || "http://localhost:8000/"; // local
  // static backendUrl: string =
  //   import.meta.env.BACKEND_URL || "https://miniprogramapp.mybolehboleh.com/"; // Live
  static currencyMark: string = "RM";
  static weightMark: string = "kg";

  //Pagination
  static paginationLimit: number = 20;

  // Google Map API Key - Dev
  // Google Map API Key - Dev

  // Google Map API Key - Live
  static googleMapApiKey: string = "AIzaSyDz9UYX3N2TFjXAoxE0ivhZUPtBcBge1fQ";
  static googleMapId: string = "9a38cfd743992b41f50e8e71";
  // Google Map API Key - Live

  //API
  static apiUrl: string = `${AppConfig.backendUrl}api/`;

  //Page
  static apiPageUrl: string = `${AppConfig.apiUrl}page/`;
  static apiFaqsUrl: string = `${AppConfig.apiPageUrl}faqs/`;
  static apiHomeBannersUrl: string = `${AppConfig.apiPageUrl}banners/`;

  //Auth
  static apiSendOtpUrl: string = `${AppConfig.apiUrl}send-otp/`;

  static apiUserUrl: string = `${AppConfig.apiUrl}user/`;
  static apiLoginUrl: string = `${AppConfig.apiUserUrl}login/`;
  static apiLogoutUrl: string = `${AppConfig.apiUserUrl}logout/`;
  static apiRegisterUrl: string = `${AppConfig.apiUserUrl}signup/`;
  static apiProfileUrl: string = `${AppConfig.apiUserUrl}profile/`;
  static apiEditProfileUrl: string = `${AppConfig.apiUserUrl}update-profile/`;
  static apiVerifyPhoneOtp: string = `${AppConfig.apiUserUrl}update-phone-otp/`;

  // Membership Tiers
  static apiMembershipTiersUrl: string = `${AppConfig.apiUrl}membership-tiers/`;

  // Product
  static apiProductsUrl: string = `${AppConfig.apiUrl}products/`;
  static apiProductBundleDetailUrl: string = `${AppConfig.apiProductsUrl}bundle-detail/`;

  //Referral
  static apiReferralUrl: string = `${AppConfig.apiUrl}referral-reward/`;

  //Points
  static apiPointsUrl: string = `${AppConfig.apiUserUrl}point/`;

  // News & Announcement
  static apiNewsAnnouncementUrl: string = `${AppConfig.apiUrl}news-and-announcement/`;

  // Rewards
  static apiRedeemableVoucherUrl: string = `${AppConfig.apiUrl}redeemable-vouchers/`;
  static apiRedeemedVoucherUrl: string = `${AppConfig.apiUrl}redeemed-vouchers/`;
  static apiRedeemedVoucherUseNowUrl: string = `${AppConfig.apiRedeemedVoucherUrl}use-now/`;

  // Address
  static apiAddressUrl: string = `${AppConfig.apiUrl}user-address-book/`;

  // Store Locator
  static apiStoreLocatorUrl: string = `${AppConfig.apiUrl}chain-stores/`;
  static apiOutletsUrl: string = `${AppConfig.apiUrl}outlets/`;
  static apiOutletDistanceUrl: string = `${AppConfig.apiOutletsUrl}distance/`;

  // General
  static apiGeneralUrl: string = `${AppConfig.apiUrl}general/`;
  static apiStateListUrl: string = `${AppConfig.apiGeneralUrl}states/`;
  static apiCountryListUrl: string = `${AppConfig.apiGeneralUrl}country-list/`;
  static apiPaymentMethodsUrl: string = `${AppConfig.apiGeneralUrl}payment-method/`;

  // Purchase Receipt
  static apiPurchaseReceiptUrl: string = `${AppConfig.apiUrl}purchase-receipt/`;
  static apiPurchaseReceiptUploadUrl: string = `${AppConfig.apiPurchaseReceiptUrl}upload/`;

  // Cart
  static apiCartUrl: string = `${AppConfig.apiUrl}carts/`;
  static apiCartAddUrl: string = `${AppConfig.apiCartUrl}add/`;

  static apiCartSummaryUrl: string = `${AppConfig.apiCartUrl}summary/`;
  static apiCartUpdateAddressUrl: string = `${AppConfig.apiCartUrl}update/`;
  static apiCartDeleteProductUrl: string = `${AppConfig.apiCartUrl}delete/`;

  static apiProductQuantityUrl: string = `${AppConfig.apiCartUrl}update/`;

  static apiCartBadgeUrl: string = `${AppConfig.apiCartUrl}badge/`;

  // Order
  static apiOrderUrl: string = `${AppConfig.apiUrl}order/`;
  static apiOrderCreateUrl: string = `${AppConfig.apiOrderUrl}create/`;

  // Setting
  static apiSettingsUrl: string = `${AppConfig.apiUrl}settings/`;

  // Wishlist
  static apiUserWishUrl: string = `${AppConfig.apiUrl}user-wishlist/`;
  static apiUserWishlistAddUrl: string = `${AppConfig.apiUserWishUrl}add`;
  static apiUserWishlistDeleteUrl: string = `${AppConfig.apiUserWishUrl}delete`;

  // Coupon
  static apiCouponUrl: string = `${AppConfig.apiUrl}coupon/`;
  static apiCouponApplyUrl: string = `${AppConfig.apiCouponUrl}apply`;
  static apiCouponRemoveUrl: string = `${AppConfig.apiCouponUrl}remove`;

  // Order Pay
  static apiOrderPaymentUrl: string = `${AppConfig.apiUrl}order/payment/`;
  static apiOrderApplyVoucherUrl: string = `${AppConfig.apiOrderUrl}apply-voucher/`;
  static apiOrderRemoveVoucherUrl: string = `${AppConfig.apiOrderUrl}remove-voucher/`;
  static apiOrderApplyCouponUrl: string = `${AppConfig.apiOrderUrl}apply-coupon/`;
  static apiOrderRemoveCouponUrl: string = `${AppConfig.apiOrderUrl}remove-coupon/`;
  static apiOrderPayUrl: string = `${AppConfig.apiOrderUrl}pay/`;

  //API
}

export default AppConfig;
