import ColorPick from '@/helpers/colorpick'
import type { ThemeDefinition } from 'vuetify'

export const staticPrimaryColor = ColorPick.primaryColor

export const themes: Record<string, ThemeDefinition> = {
  light: {
    dark: false,
    colors: {
      primary: staticPrimaryColor,
      'on-primary': '#fff',
      'primary-darken-1': ColorPick.primaryDarkenColor,
      secondary: ColorPick.secondaryColor,
      'secondary-darken-1': ColorPick.secondaryDarkenColor,
      'on-secondary': '#fff',
      success: '#56CA00',
      'success-darken-1': '#4DB600',
      'on-success': '#fff',
      info: '#16B1FF',
      'info-darken-1': '#149FE6',
      'on-info': '#fff',
      warning: '#FFB400',
      'warning-darken-1': '#E6A200',
      'on-warning': '#fff',
      error: '#FF4C51',
      'error-darken-1': '#E64449',
      'on-error': '#fff',
      background: ColorPick.backgroundColor,
      'on-background': ColorPick.onBackgroundColor,
      surface: ColorPick.surfaceColor,
      'on-surface': ColorPick.onSurfaceColor,
      'grey-50': '#FAFAFA',
      'grey-100': '#F5F5F5',
      'grey-200': '#EEEEEE',
      'grey-300': '#E0E0E0',
      'grey-400': '#BDBDBD',
      'grey-500': '#9E9E9E',
      'grey-600': '#757575',
      'grey-700': '#616161',
      'grey-800': '#424242',
      'grey-900': '#212121',
      'perfect-scrollbar-thumb': '#dbdade',
      'skin-bordered-background': '#fff',
      'skin-bordered-surface': '#fff',
      'expansion-panel-text-custom-bg': '#fafafa',
      'track-bg': '#F0F2F8',
      'chat-bg': '#F7F6FA',
      'required-field': '#F44336',
    },

    variables: {
      'code-color': '#d400ff',
      'overlay-scrim-background': '#2E263D',
      'tooltip-background': '#1A0E33',
      'overlay-scrim-opacity': 0.5,
      'hover-opacity': 0.04,
      'focus-opacity': 0.1,
      'selected-opacity': 0.08,
      'activated-opacity': 0.16,
      'pressed-opacity': 0.14,
      'dragged-opacity': 0.1,
      'disabled-opacity': 0.4,
      'border-color': '#2E263D',
      'border-opacity': 0.15,
      'table-header-color': '#F6F7FB',
      'high-emphasis-opacity': 0.9,
      'medium-emphasis-opacity': 0.7,

      // 👉 shadows
      'shadow-key-umbra-color': '#2E263D',
      'shadow-xs-opacity': '0.16',
      'shadow-sm-opacity': '0.18',
      'shadow-md-opacity': '0.20',
      'shadow-lg-opacity': '0.22',
      'shadow-xl-opacity': '0.24',
    },
  },

  dark: {
    dark: false,
  },
}

export default themes
