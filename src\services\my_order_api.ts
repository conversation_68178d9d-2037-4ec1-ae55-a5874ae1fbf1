import { PaginationDto } from "@/dto/pagination.dto";
import AppConfig from "@/appconfig";
import ApiHelper from "@/helpers/api_helper";
import { OrderStatusEnum } from "@/enums/OrderStatusEnum";

class MyOrderApi {
  //Get My Orders List
  public static async myOrderList(
    page: number,
    perPage?: number,
    status?: string[],
  ): Promise<PaginationDto> {
    try {
      const body: any = {
        page: page,
        per_page: perPage ?? AppConfig.paginationLimit,
        status: status,
      };

      var response = await ApiHelper.post(
        AppConfig.apiOrderUrl,
        undefined,
        body
      );

      const tempMyOrders: PaginationDto = new PaginationDto(response.data.data);

      return tempMyOrders;
    } catch (error) {
      throw error;
    }
  }
  //Get My Orders List
}

export default MyOrderApi;
