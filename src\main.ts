/**
 * main.ts
 *
 * Bootstraps Vuetify and other plugins then mounts the App`
 */

// Plugins
import { registerPlugins } from './@core/utils/plugins';

// Components
import App from './App.vue'

// Composables
import { createApp } from 'vue'

// Styles
import '@/@layouts/styles/index.scss'
import 'remixicon/fonts/remixicon.css'
import '@mdi/font/css/materialdesignicons.css'

const app = createApp(App)

registerPlugins(app)

app.mount('#app')
