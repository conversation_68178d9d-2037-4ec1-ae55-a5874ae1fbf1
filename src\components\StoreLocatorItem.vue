<script setup lang="ts">
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";

interface Props {
  image?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
  subDescription?: string | undefined;
  isOpen?: boolean | undefined;
  onClick?: () => any;
}
const props = defineProps<Props>();

const { t } = useI18n();
</script>

<template>
  <div
    class="store-locator-item"
    :class="{ 'store-closed': props.isOpen === false }"
    :style="{
      cursor: props.onClick ? 'pointer' : 'none',
    }"
    @click="props.onClick"
  >
    <VImg
      v-if="image"
      :src="props.image"
      width="125px"
      height="125px"
      class="store-locator-item-image"
      cover
    >
    </VImg>
    <div class="store-locator-item-content">
      <span v-if="name" class="store-locator-item-content-title">
        {{ props.name }}
      </span>
      <div v-if="subDescription" style="height: 2px"></div>
      <span v-if="description" class="store-locator-item-content-description">
        {{ props.description }}
      </span>
      <span
        v-if="props.isOpen !== undefined"
        :class="['store-locator-status', props.isOpen ? 'open' : 'closed']"
      >
        {{ props.isOpen ? t("opened") : t("closed") }}
      </span>
      <div v-if="subDescription" style="height: 35px"></div>
      <span
        v-if="subDescription"
        class="store-locator-item-content-sub-description"
      >
        {{ props.subDescription }}
      </span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.store-locator-item {
  display: -webkit-box;
  padding: 10px;
  text-align: left;
  transition: all 0.4s ease;
  height: 150px;
  -webkit-box-align: center;
  // opacity: 1;
}

.store-locator-item.store-closed {
  opacity: 0.7;
  // pointer-events: none;
}

.store-locator-item:hover {
  background-color: v-bind("props.onClick ? ColorPick.secondaryColor : null");
}

.store-locator-item-image {
  border-radius: 10px;
}

.store-locator-item-content {
  padding: 10px;
  width: 60%;
}

.store-locator-item-content-title {
  text-align: left;
  -webkit-line-clamp: 1;
  max-lines: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  font-size: 16px;
  font-weight: 500;
  color: v-bind("ColorPick.fontColor");
  display: -webkit-box;
}

.store-locator-item-content-description {
  text-align: left;
  -webkit-line-clamp: 2;
  max-lines: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  font-size: 10px;
  font-weight: 400;
  color: v-bind("ColorPick.homeSeeAllColor");
  display: -webkit-box;
}

.store-locator-item-content-sub-description {
  text-align: left;
  -webkit-line-clamp: 1;
  max-lines: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  font-size: 10px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryDarkenColor");
  display: -webkit-box;
}

.store-locator-status {
  font-size: 11px;
  font-weight: bold;
}

.store-locator-status.open {
  color: green;
}

.store-locator-status.closed {
  color: red;
}
</style>
