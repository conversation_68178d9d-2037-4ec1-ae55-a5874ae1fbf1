export class PaymentDetailsDto {
   subtotal: string;
   shipping_fee: string;
   voucher_discount: string;
   tax: string;
   grand_total: string;
   points_earned: number;
   coupon_discount: string;
   delivery_discount: string;

   constructor(data: any) {
      this.subtotal = data.subtotal;
      this.shipping_fee = data.shipping_fee;
      this.voucher_discount = data.voucher_discount;
      this.tax = data.tax;
      this.grand_total = data.grand_total;
      this.points_earned = data.points_earned;
      this.coupon_discount = data.coupon_discount;
      this.delivery_discount = data.delivery_discount;
   }
}