<script setup lang="ts">
import { VCard, VImg } from "vuetify/components";
import logo from "@/assets/icons/logo.png";
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";
import TelInputPlus from "@/components/custom_control/TelInputPlus.vue";
import { ref } from "vue";
import SnackBarPlus from "@/components/custom_control/SnackBarPlus.vue";
import { useRouter, useRoute } from "vue-router";
import RoutersHelper from "@/helpers/routes_helper";
import ButtonPlus from "@/components/custom_control/ButtonPlus.vue";
import '@/interfaces/getphonenumber';

const { t } = useI18n();
const router = useRouter();
const route = useRoute();

const isValid = ref<boolean>(false);
const phoneTextField = ref<string>();

const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

//Function
function onNext() {
  router.push({
    path: RoutersHelper.otp,
    query: {
      phoneNumber: `${phoneTextField.value}`.getPhoneNumber(),
      referralCode: route.query.referralCode,
    },
  });
}

function toHomePage() {
  router.push(RoutersHelper.home);
}
</script>

<template>
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>
  <div class="login">
    <div class="login-container">
      <div style="height: 50px"></div>
      <div class="login-logo">
        <VImg :src="logo" :cover="false" height="60" width="60" />
      </div>
      <h1 class="login-title">
        {{ t("app_name") }}
      </h1>
      <div class="login-description">
        {{ t("login_description") }}
      </div>
      <div style="height: 20px"></div>
      <VCard class="login-card">
        <VCardText>
          <TelInputPlus
            v-model="phoneTextField"
            @validate="
              (value) => {
                isValid = value.valid;
              }
            "
          ></TelInputPlus>
        </VCardText>

        <div style="height: 10px"></div>

        <VCardActions>
          <ButtonPlus
            class="login-next"
            :disabled="!isValid"
            :color="ColorPick.buttonColorAuth"
            @click="onNext"
          >
            {{ t("next") }}
          </ButtonPlus>
        </VCardActions>

        <div style="height: 20px"></div>

        <div class="divider">
          <span>{{ t("or") }}</span>
        </div>

        <VContainer @click="toHomePage()">
          <VBtn variant="text" class="guess-title">
            {{ t("continue_as_guest") }}
          </VBtn>
        </VContainer>
      </VCard>

      <div style="height: 50px"></div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login {
  width: 100%;
  position: absolute;
  justify-items: center;
  text-align: -webkit-center;
  overflow: auto;
  height: 100%;
}

.login-container {
  padding: 20px;
  max-width: 600px;
}

.login-logo {
  padding: 10px;
}

.login-title {
  font-size: 32px;
  font-weight: 700;
  text-align: center;
  padding: 3px;
  color: v-bind("ColorPick.fontColorWhite");
}

.login-description {
  font-size: 17px;
  font-weight: 500;
  text-align: center;
  padding: 3px;
  color: v-bind("ColorPick.fontColorWhite");
}

.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
  padding: 15px;
  overflow: visible;
}

.login-next {
  width: 100%;
}

.divider {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

.divider::before,
.divider::after {
  content: "";
  flex: 1;
  height: 1px;
  background-color: gray;
  margin: 0 10px;
}

.guess-title {
  font-size: 12px;
  color: v-bind("ColorPick.primaryColor");
  font-weight: 500;
}
</style>
