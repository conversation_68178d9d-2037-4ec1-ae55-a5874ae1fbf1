{"name": "mybolehboleh", "private": true, "type": "module", "version": "1.0.1", "scripts": {"dev": "vite --host", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@intlify/unplugin-vue-i18n": "^6.0.3", "@mdi/font": "7.4.47", "@vueuse/core": "^12.5.0", "axios": "^1.7.9", "core-js": "^3.37.1", "moment": "^2.30.1", "remixicon": "^4.6.0", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^0.27.5", "v-viewer": "^3.0.22", "vue": "^3.4.31", "vue-flux": "^7.0.0-beta.6", "vue-i18n": "^11.1.0", "vue-tel-input": "^9.3.0", "vue3-carousel": "^0.14.0", "vuetify": "^3.6.14"}, "devDependencies": {"@eslint/js": "^9.14.0", "@tsconfig/node22": "^22.0.0", "@types/google.maps": "^3.58.1", "@types/node": "^22.13.1", "@vitejs/plugin-vue": "^5.1.4", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.5.1", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "npm-run-all2": "^7.0.1", "sass": "1.77.8", "sass-embedded": "^1.77.8", "typescript": "~5.6.3", "unplugin-fonts": "^1.1.1", "unplugin-vue-router": "^0.10.0", "vite": "^5.4.10", "vite-plugin-vuetify": "^2.0.3", "vue-router": "^4.4.0", "vue-tsc": "^2.1.10"}}