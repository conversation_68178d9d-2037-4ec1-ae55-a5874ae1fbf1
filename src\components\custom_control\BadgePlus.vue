<script setup lang="ts">
import ColorPick from '@/helpers/colorpick';

interface Props {
    content: number,
    color?: string,
}

const props = defineProps<Props>();

const emit = defineEmits<{
    (e: "click"): void;
}>();

const onClick = () => {
    emit("click");
};
</script>

<template>
    <div class="flex items-center badge-container" @click="onClick()">
        <VBadge :content="props.content" :model-value="props.content > 0" :color="props.color ?? ColorPick.badgeColor">
            <slot></slot>
        </VBadge>
    </div>
</template>

<style lang="scss" scoped>
.badge-container {
    padding: 10px;
}
</style>