<script setup lang="ts">
import ColorPick from "@/helpers/colorpick";
import { useRouter } from "vue-router";
import { useSlots, computed } from "vue";

//Variables
const router = useRouter();

interface Props {
  title?: string | undefined;
  back?: boolean;
  titleCenter?: boolean;
  currentPage?: number;
}
const props = withDefaults(defineProps<Props>(), {
  title: undefined,
  back: false,
  titleCenter: true,
  currentPage: 0,
});

//Function
function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    // if (props.currentPage == 1) {
    //   router.back();
    //   router.back();
    // } else {
    router.back();
    // }
  }
}

const slots: any = useSlots();

// Check if the "header" slot has content
const hasHeaderSlotContent = computed(() => {
  return slots.actions;
});
</script>

<template>
  <VAppBar elevation="0">
    <VBtn v-if="props.back" :color="ColorPick.fontColor" @click="goBack">
      <VIcon class="ri-arrow-left-s-line" :size="28"></VIcon>
    </VBtn>

    <div
      v-if="!props.back && hasHeaderSlotContent && titleCenter"
      style="width: 64px"
    ></div>

    <VAppBarTitle
      :style="{
        color: ColorPick.appBarTitleColor,
        textAlign: props.titleCenter ? 'center' : 'left',
        margin: props.titleCenter ? 'auto' : '',
        fontWeight: '400',
        fontSize: '18px',
      }"
    >
      {{ props.title?.toUpperCase() }}
    </VAppBarTitle>

    <!-- Action -->
    <slot name="actions" />

    <div
      v-if="props.back && !hasHeaderSlotContent && titleCenter"
      style="width: 64px"
    ></div>

    <!-- Right Spacing if there is actions -->
    <div v-if="hasHeaderSlotContent" style="width: 10px"></div>
  </VAppBar>
</template>
