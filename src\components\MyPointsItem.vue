<script setup lang="ts">
import ColorPick from '@/helpers/colorpick';
import { useI18n } from 'vue-i18n';
import moment from 'moment';
import ThousandsHelper from '@/helpers/thousands_helper';

// Language
const { t } = useI18n();

interface Props {
    image?: string | undefined;
    title?: string | undefined;
    description?: string | undefined;
    prefix?: string;
    points?: number | undefined;
    date?: Date | undefined;
}

const props = withDefaults(defineProps<Props>(), {
    prefix: '+',
})


</script>

<template>
    <div>
        <div class="my-points-item-container">
            <VImg v-if="props.image" :src="props.image" cover height="70px" width="70px"
                :style="{ borderRadius: '10px' }" />
            <div v-if="props.image" :style="{ width: '10px' }"></div>
            <div :style="{ width: '100%', }">
                <div v-if="props.title" class="my-points-item-title">
                    {{ props.title }}
                </div>
                <div v-if="props.description" class="my-points-item-description">
                    {{ props.description }}
                </div>
                <div style="height: 8px;">
                </div>
                <div v-if="props.date" class="my-points-item-date">
                    {{ moment(props.date).format('D MMMM YYYY, h:mm a') }}
                </div>

            </div>

            <div v-if="props.points !== undefined"
                :style="{ color: prefix === '+' || props.points == 0 ? `${ColorPick.primaryColor}` : `${ColorPick.greyFontColor05}` }"
                class="my-points-item-points">
                {{ props.points <= 0 ? '' : prefix }} {{ ThousandsHelper.format(`${props.points}`) }} </div>
            </div>
            <VDivider></VDivider>
        </div>
</template>

<style lang="scss" scoped>
.my-points-item-container {
    display: flex;
    -webkit-box-align: center;
    padding: 10px;
    text-align: left;
    transition: all .4s ease;
    height: 90px;
    align-items: center;
    overflow: hidden;
}

.my-points-item-title {
    width: 100%;
    font-size: 16px;
    font-weight: 600;
    text-align: left;
    color: v-bind('ColorPick.fontColor');
    max-lines: 1;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}

.my-points-item-description {
    width: 100%;
    font-size: 10px;
    font-weight: 400;
    text-align: left;
    color: v-bind('ColorPick.greyFontColor02');
    max-lines: 2;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}

.my-points-item-date {
    width: 100%;
    font-size: 10px;
    font-weight: 400;
    text-align: left;
    color: v-bind('ColorPick.greyFontColor02');
    max-lines: 1;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}

.my-points-item-points {
    padding: 0px 10px;
    width: 120px;
    font-size: 16px;
    font-weight: 600;
    text-align: right;
    max-lines: 1;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}
</style>