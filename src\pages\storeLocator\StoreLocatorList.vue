<script setup lang="ts">
// Helpers
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";
import { ref, onMounted, watch } from "vue";
import AppConfig from "@/appconfig";
import localStorageHelper from "@/helpers/localstorage_helper";

// Router
import { useRouter, useRoute } from "vue-router";
import RoutersHelper from "@/helpers/routes_helper";

// Components
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";
import StoreLocatorItem from "@/components/StoreLocatorItem.vue";
import LoadingPlus from "@/components/custom_control/LoadingPlus.vue";

// Dtos
import { StoreLocatorDto } from "@/dto/store_locators.dto";

// APIs
import GeneralPageApi from "@/services/general_page_api";
import HomePageStoreLocatorApi from "@/services/homepage_store_locator_api";
import SnackBarPlus from "@/components/custom_control/SnackBarPlus.vue";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// Variables
const stateSelected = ref("all");
const placeName = ref<string>("");
let searchTimeout: ReturnType<typeof setTimeout> | null = null;

// Store Locators list
const page = ref<number>(1);
const hasMore = ref(true);
let doneCopy: (status: "error" | "loading" | "empty" | "ok") => void;

const data = ref<StoreLocatorDto[]>([]);
const stateLists = ref<any[]>([{ value: "all", name: t("all_states") }]);

// General Variable
const isLoading = ref<boolean>(false);
const isLoadingitems = ref<boolean>(false);
const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>("");
const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

// Function
function toStoreLocatorDetails(item: StoreLocatorDto) {
  if (route.query.isFromProduct) {
    if (item.is_open === false) {
      openSnackBar.value = true;
      snackBarMsg.value = t("store_closed");
      // localStorageHelper.setOutletId(item.id.toString());

      // setTimeout(() => {
      //   router.push(RoutersHelper.product);
      // }, 1000);
    } else {
      localStorageHelper.setOutletId(item.id.toString());
      router.push(RoutersHelper.product);
    }
  } else {
    router.push(`${RoutersHelper.storeLocatorDetails}/${item.id}`);
  }
}

function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

// Fetch State List
async function getStateList() {
  try {
    // Fetch only if not stored
    var tempStates = await GeneralPageApi.getStateList();

    stateLists.value = [
      { value: "all", name: t("all_states") },
      ...Object.entries(tempStates).map(([key, value]) => ({
        value: key,
        name: value,
      })),
    ];
  } catch (_) {}
}

// Fetch Store Location List
async function loadStoreLocatorList() {
  await new Promise((resolve) => setTimeout(resolve, 1000));
  const response = await HomePageStoreLocatorApi.storeLocatorList(
    page.value,
    AppConfig.paginationLimit,
    stateSelected.value === "all" ? "" : stateSelected.value,
    placeName.value
  );
  return response;
}

// Store Location List - Infinite Scroll
async function infiniteload({
  done,
}: {
  done: (status: "error" | "loading" | "empty" | "ok") => void;
}) {
  if (!hasMore.value) {
    done("empty");
    return;
  }

  try {
    const res = await loadStoreLocatorList();

    if (res.data !== undefined) {
      data.value.push(...res.data);

      const lastPage = res.last_page ?? 1;

      if (page.value >= lastPage) {
        hasMore.value = false;

        // For refresh the page
        doneCopy = done;
      } else {
        hasMore.value = true;

        page.value += 1;
      }
    } else {
      hasMore.value = false;
      done("empty");
      doneCopy = done;
    }
  } catch (e) {
  } finally {
    done("ok");
  }
}

onMounted(() => {
  window.scrollTo(0, 0);

  getStateList();
  loadStoreLocatorList();
});

// Search name, address
watch(placeName, (value) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
  placeName.value = value;

  searchTimeout = setTimeout(() => {
    page.value = 1;
    data.value = [];
    hasMore.value = true;

    isLoadingitems.value = true;

    infiniteload({
      done: (status) => {
        isLoadingitems.value = false;
      },
    });
  }, 1000); // Delay
});

// Search state
watch(stateSelected, (value) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }
  stateSelected.value = value;

  searchTimeout = setTimeout(() => {
    page.value = 1;
    data.value = [];
    hasMore.value = true;

    isLoadingitems.value = true;

    infiniteload({
      done: (status) => {
        isLoadingitems.value = false;
      },
    });
  }, 200); // Delay
});
</script>

<template>
  <LoadingPlus v-if="isLoading && !data" />

  <AppBarPlus :title="t('store_location')" :back="true" :current-page="page" />

  <!-- AppBarPlus Spacing -->
  <div style="height: 70px"></div>
  <!-- Body  -->
  <div class="store-locator">
    <div class="store-locator-container">
      <div class="news-announcement-content">
        <div class="search_filter">
          <div class="d-flex" :style="{ padding: '0 10px 0 10px' }">
            <div :style="{ flex: 1, padding: '5px' }">
              <SelectPlus
                v-model="stateSelected"
                :items="stateLists"
                item-value="value"
                item-title="name"
                :placeholder="t('select_state')"
              >
              </SelectPlus>
            </div>
            <div :style="{ flex: 2, padding: '5px' }">
              <div :style="{ height: '16px' }"></div>
              <TextFieldPlus
                v-model="placeName"
                :placeholder="t('enter_place_name')"
                clearable
                persistent-clear
              >
              </TextFieldPlus>
            </div>
          </div>
        </div>

        <div style="height: 20px"></div>

        <div
          :style="{
            width: '100%',
            maxWidth: '600px',
            padding: '0 10px 0 10px',
          }"
        >
          <span class="store_locator-title">{{
            stateLists
              .find((item) => item.value === stateSelected)
              ?.name?.toUpperCase() ?? ""
          }}</span>
        </div>

        <div style="height: 5px"></div>

        <LoadingPlus v-if="isLoadingitems" />

        <v-container v-else style="padding: 0">
          <VInfiniteScroll
            :height="'100%'"
            :items="data"
            @load="infiniteload"
            v-model="hasMore"
            :empty-text="
              data?.length ?? 0 > 0 ? t('no_more') : t('no_data_found')
            "
          >
            <div
              v-if="data && data?.length > 0"
              v-for="item in data"
              :style="{
                maxWidth: '600px',
              }"
            >
              <div>
                <StoreLocatorItem
                  :image="item.image"
                  :name="item.name"
                  :description="item.address"
                  :sub-description="item.contact_number"
                  :is-open="item.is_open"
                  :onClick="
                    () => {
                      toStoreLocatorDetails(item);
                    }
                  "
                />
              </div>
            </div>
            <!-- </template> -->
          </VInfiniteScroll>
        </v-container>

        <VSpacer />

        <!-- <div style="height: 70px"></div> -->
      </div>
    </div>
  </div>

  <!-- Dialog -->
  <DialogPlus
    persistent
    v-model="openDialog"
    :confirmText="t('back')"
    :onClickConfirm="
      () => {
        openDialog = false;
        goBack();
      }
    "
  >
    {{ dialogMsg }}
  </DialogPlus>

  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>
</template>

<style lang="scss" scoped>
.store-locator {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.store-locator-container {
  width: 100%;
  max-width: 600px;
  padding: 70px 10px 70px;
}

.search_filter {
  position: fixed;
  top: 50px;
  /* Below AppBar */
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 600px;
  background-color: v-bind("ColorPick.backgroundColor");
  padding: 10px 0;
  z-index: 10;
  /* Ensure it stays above content */
}

.store_locator-title {
  font-size: 16px;
  font-weight: 600;
}

.pagination {
  position: fixed;
  bottom: 0;
  width: 100%;
  max-width: 600px;
  background-color: v-bind("ColorPick.backgroundColor");
  padding: 10px 0;
  text-align: center;
}
</style>
