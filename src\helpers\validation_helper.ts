import { translate } from '@/plugins/i18n';
const t = translate
class ValidationHelper {
  static _userId: string;
  static get userId() {
    return ValidationHelper._userId;
  }
  static set userId(id: string) {
    ValidationHelper._userId = id;
  };

  static _userName: string;
  static get userName() {
    return ValidationHelper._userName;
  }
  static set userName(name: string) {
    ValidationHelper._userName = name;
  };

  public static required(value: string) {
    if (value === null || value === undefined || value === '') {
      return t('fill_is_required')
    }

    return true
  }
  public static email(value: string) {
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (value !== null && value !== undefined && value !== '' && !emailPattern.test(value)) {
      return t('please_enter_valid_email')
    }

    return true
  }

  public static number(value: string) {
    if (value !== null && value !== undefined && value !== '' && isNaN(Number(value))) {
      return t('please_enter_number')
    }
  }
}

export default ValidationHelper
