import AppConfig from "@/appconfig";
import { HomepageBannersDto } from "@/dto/homepage_banners.dto";
import ApiHelper from "@/helpers/api_helper";

class HomepageBannersApi {
  //Get Homepage Banners List
  public static async list(): Promise<HomepageBannersDto[]> {
    try {
      var response = await ApiHelper.post(
        AppConfig.apiHomeBannersUrl,
        undefined,
        {
          type: "home",
        }
      );

      const tempBanner: HomepageBannersDto[] = response.data.data.map(
        (item: any) => new HomepageBannersDto(item)
      );

      return tempBanner;
    } catch (error) {
      throw error;
    }
  }
  //Get Homepage Banners List

  //Get Profile Banners List
  public static async profileList(): Promise<HomepageBannersDto[]> {
    try {
      var response = await ApiHelper.post(
        AppConfig.apiHomeBannersUrl,
        undefined,
        {
          type: "profile",
        }
      );

      const tempBanner: HomepageBannersDto[] = response.data.data.map(
        (item: any) => new HomepageBannersDto(item)
      );

      return tempBanner;
    } catch (error) {
      throw error;
    }
  }
}

export default HomepageBannersApi;
