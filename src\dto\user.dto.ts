import { CurrentMembershipDto } from "./current_membership.dto";
import { ExpiringPointsDto } from "./expiring_points.dto";
import { MembershipTiersDto } from "./membership_tiers.dto";
import moment from "moment";
import { NextMembershipDto } from "./next_membership.dto";
export class UserDto {
  point_balance: number;
  accumulated_points: number;
  brand: string;
  profile_image: string;
  dob: Date;
  email_address: string;
  hear_about: string;
  expiring_points?: ExpiringPointsDto;
  membership_tier: string;
  membership_tiers?: MembershipTiersDto;
  current_membership?: CurrentMembershipDto;
  next_membership?: NextMembershipDto;
  name: string;
  phone_number: string;
  preferred_language: string;
  race: string;
  token?: string;
  unused_vouchers: number;
  referral_code?: string;

  constructor(data: any) {
    try {
      this.membership_tiers = new MembershipTiersDto(data.membership_tiers);
    } catch (_) {}

    try {
      this.current_membership = new CurrentMembershipDto(
        data.current_membership
      );
    } catch (_) {}

    try {
      this.next_membership = new NextMembershipDto(data.next_membership);
    } catch (_) {}

    try {
      this.expiring_points = new ExpiringPointsDto(data.expiring_points);
    } catch (_) {}

    this.point_balance = data.point_balance ?? 0;
    this.accumulated_points = data.accumulated_points ?? 0;
    this.brand = data.brand ?? "";
    this.profile_image = data.profile_image ?? "";
    this.dob = moment(data.dob, "DD-MM-YYYY").toDate();
    this.email_address = data.email_address ?? "";
    this.hear_about = data.hear_about ?? "";
    this.membership_tier = data.membership_tier ?? "";
    this.name = data.name ?? "";
    this.phone_number = data.phone_number ?? "";
    this.preferred_language = data.preferred_language ?? "";
    this.race = data.race ?? "";
    this.token = data.token ?? "";
    this.unused_vouchers = data.unused_vouchers ?? 0;
    this.referral_code = data.referral_code?.toString();
  }
}
