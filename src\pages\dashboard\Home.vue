<script setup lang="ts">
// Images
import pickupLogo from "@/assets/icons/pickup.png";
import deliverylogo from "@/assets/icons/delivery.png";
import locationLogo from "@/assets/icons/location.png";
import progressBarIcon from "@/assets/icons/progress_bar_icon.png";
import logo from "@/assets/icons/logo.png";

import storeLocationImg from "@/assets/images/store_location.png";
import voucherImg from "@/assets/images/voucher.png";
import ourStory from "@/assets/images/our_story.png";

// Helpers
import { computed, onMounted, onUnmounted, ref } from "vue";
import { useDisplay } from "vuetify";
import { VPullToRefresh } from "vuetify/labs/VPullToRefresh";
import CapitalizeHelper from "@/helpers/capitalize_helper";

import ColorPick from "@/helpers/colorpick";
import "vue3-carousel/carousel.css";
import { Carousel, Slide, Pagination, Navigation } from "vue3-carousel";
import OrderTypeEnum from "@/enums/OrderTypeEnum";

// Dtos
import type { HomepageBannersDto } from "@/dto/homepage_banners.dto";
import type { NewsAnnouncementDto } from "@/dto/news_announcement.dto";
import type { UserDto } from "@/dto/user.dto";
import type { SettingDto } from "@/dto/setting.dto";
import type { RewardsDto } from "@/dto/rewards.dto";

// Apis
import HomepageBannersApi from "@/services/homepage_banners_api";
import NewsAnnouncementApi from "@/services/news_announcement_api";
import AuthPageApi from "@/services/auth_api";
import GeneralPageApi from "@/services/general_page_api";
import MyOrderApi from "@/services/my_order_api";

// Routes
import RoutersHelper from "@/helpers/routes_helper";
import { useRouter } from "vue-router";
import { watch } from "vue";

// Language
import { useI18n } from "vue-i18n";
import ButtonContent from "@/components/ButtonContent.vue";
import localStorageHelper from "@/helpers/localstorage_helper";
import AppConfig from "@/appconfig";
import RewardApi from "@/services/reward_api";

// Components
import RequiredLogin from "@/components/RequiredLogin.vue";
import { MyOrderDto } from "@/dto/order.dto";

const { t } = useI18n();
const router = useRouter();

//Handle Display
const { xs, smAndDown } = useDisplay();

// Data List
const userData = ref<UserDto>();
const bannerList = ref<HomepageBannersDto[]>([]);
const settingData = ref<SettingDto>();

const promotionList = ref<RewardsDto[]>([]);
const promotionFeaturedList = ref<RewardsDto[]>([]);

const newsList = ref<NewsAnnouncementDto[]>([]);

const orderData = ref<MyOrderDto>();

// Carousel
const carouselConfig = ref({
  itemsToShow: getItemsToShow(),
  autoplay: 3000,
  wrapAround: true, // Infinite scrolling enabled
});

// Track active slide index
const activeSlide = ref(0);
const pullDownThreshold = ref(60);
const openLoginDialog = ref<boolean>(false);

// Function

// Handle the banner size
function getItemsToShow() {
  const gap = 20; // Fixed gap of 20px between items
  const itemWidth = 300; // Width of each item
  const availableWidth = window.innerWidth >= 600 ? 600 : window.innerWidth;

  return (availableWidth + gap) / (itemWidth + gap);
}

// Handle the banner size
function handleResize() {
  carouselConfig.value.itemsToShow = getItemsToShow();
}

// Calaculation progress bar
function percentageSpend(current: number, total: number) {
  return (current / total) * 100;
}

// Move to Home page
function toHomePage() {
  openLoginDialog.value = false;
}

// Move to store locator
function moveToStoreLocator() {
  var token: string | null = null;
  token = localStorageHelper.getUserToken();

  if (token !== null) {
    router.push({ path: RoutersHelper.storeLocator });
  } else {
    openLoginDialog.value = true;
  }
}

// Move to News and Announcement
function moveToNewsAndAnnouncement() {
  router.push({ path: RoutersHelper.newsAnnouncement });
}

// Move to News and Announcement Details
function moveToNewsAndAnnouncementDetails(id: string) {
  router.push(`${RoutersHelper.newsAnnouncementDetails}/${id}`);
}

// Move to Product Page
async function moveToProductPage(orderTypeEnum: string) {
  localStorageHelper.setOrderType(orderTypeEnum);

  if (localStorageHelper.getUserToken() !== null) {
    if (localStorageHelper.getOutletId() === null) {
      router.push({
        path: RoutersHelper.storeLocator,
        query: { isFromProduct: "true" },
      });
    } else {
      router.push(RoutersHelper.product);
    }
  } else {
    router.push(RoutersHelper.product);
  }
}

// Move to point history
function moveToVoucher() {
  router.push(RoutersHelper.rewards);
}

// Move to Reward Details
function toRewardDetails(id: string) {
  router.push({
    path: `${RoutersHelper.rewardDetails}/${id}`,
  });
}

// Move to About Us
function toAboutUs() {
  router.push(RoutersHelper.aboutUs);
}

function moveToOrderDetails(id: number) {
  router.push(`${RoutersHelper.orderDetails}/${id}`);
}

// Whatsapp
const goToWhatsApp = () => {
  window.open(
    "https://wa.me/" +
      settingData?.value?.support_contact +
      "?text=Hi, I'm interested in your product.",
    "_blank"
  );
};

// Function - API
async function loadBanners() {
  try {
    bannerList.value = (await HomepageBannersApi.list()) ?? [];
  } catch (_) {}
}

// Get News and Announcement
async function loadNewsAnnouncementList() {
  try {
    newsList.value = (await NewsAnnouncementApi.list(1, 2)).data ?? [];
  } catch (e) {}
}

// Get latest Promotion List
async function loadLatestPromotionList() {
  try {
    promotionList.value =
      (await RewardApi.redeemableList(1, 5, undefined, "desc")).data ?? [];
  } catch (e) {}
}

async function loadLatestPromotionFeaturedList() {
  try {
    promotionFeaturedList.value =
      (await RewardApi.redeemableList(1, 3, 1, "asc")).data ?? [];
  } catch (e) {}
}

// Get Profile
async function loadUser() {
  try {
    userData.value = await AuthPageApi.getProfile();
  } catch (_) {}
}

// Get Setting
async function loadSetting() {
  try {
    settingData.value = await GeneralPageApi.getSettings();
  } catch (_) {}
}

// Fetch Avtive order List
async function loadActiveOrder() {
  try {
    var token = localStorageHelper.getUserToken();

    if (token === null) return undefined;
    const response = await MyOrderApi.myOrderList(1, 1, ["paid", "shipped"]);

    if (response.data.length !== 0) {
      orderData.value = response.data[0];
    }
    return response;
  } catch (_) {}
  return undefined;
}

// On Refresh
async function refresh({ done }: { done: () => void }) {
  var token: string | null = null;
  token = localStorageHelper.getUserToken();

  if (token !== null) {
    loadUser();
    loadBanners();
    loadNewsAnnouncementList();
    loadLatestPromotionList();
    loadLatestPromotionFeaturedList();
    loadSetting();
  } else {
    loadBanners();
    loadLatestPromotionList();
    loadLatestPromotionFeaturedList();
    loadNewsAnnouncementList();
    loadSetting();
  }
  setTimeout(() => {
    done(); // Stop the loading
  }, 1000);
}

onMounted(() => {
  window.addEventListener("resize", handleResize);

  var token: string | null = null;
  token = localStorageHelper.getUserToken();

  if (token !== null) {
    loadUser();
    loadActiveOrder();
    loadLatestPromotionList();
    loadLatestPromotionFeaturedList();
    loadBanners();
    loadNewsAnnouncementList();
    loadSetting();
  } else {
    loadBanners();
    loadLatestPromotionList();
    loadLatestPromotionFeaturedList();
    loadNewsAnnouncementList();
    loadSetting();
  }
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<template>
  <div class="home">
    <div>
      <v-pull-to-refresh
        :pull-down-threshold="pullDownThreshold"
        @load="refresh"
      >
        <div
          v-if="bannerList.length > 0"
          class="home-image-container"
          :style="{
            padding: xs ? '0' : '15px',
          }"
        >
          <Carousel v-model="activeSlide" v-bind="carouselConfig">
            <Slide v-for="(slide, index) in bannerList" :key="index">
              <div
                class="carousel__item"
                :style="{
                  transform: index === activeSlide ? 'scale(1)' : 'scale(0.94)',
                  opacity: index === activeSlide ? 1 : 0.8,
                }"
              >
                <img
                  :src="slide.image"
                  :alt="slide.title"
                  class="carousel-image"
                />
              </div>
            </Slide>
          </Carousel>
        </div>

        <!-- Order Status Widget -->
        <div style="padding: 0px 10px">
          <div
            v-if="orderData"
            class="order-card"
            @click="moveToOrderDetails(orderData.id)"
          >
            <!-- Order Location -->
            <div class="order-info">
              <VImg :src="locationLogo" :width="10" :height="20" />
              <div style="width: 10px" />
              <span
                >{{ orderData.outlet.name }} (
                {{ CapitalizeHelper.capitalize(orderData.outlet.state) }}
                test )</span
              >
            </div>

            <!-- Order ID -->
            <div style="padding-left: 20px">
              <span class="order-id">#{{ orderData.order_no }}</span>
            </div>

            <div style="height: 10px" />

            <!-- Estimated Pick-up Info -->
            <div class="order-info pickup-section">
              <div class="left-content">
                <span class="status-dot"></span>

                <span>{{ t("order_status") }}: {{ orderData.status }}</span>
              </div>
              <span class="pickup-time">{{ orderData.created_at }}</span>
            </div>
          </div>
          <!-- Order Status Widget -->

          <div style="height: 20px" />

          <!-- Membership Card Widget -->
          <div
            class="membership-card"
            :style="{
              padding: localStorageHelper.getUserToken()
                ? '20px 10px'
                : '0px 10px',
            }"
          >
            <div v-if="localStorageHelper.getUserToken()">
              <div class="membership-card-info">
                <div class="membership-level">
                  <span style="margin-left: 3px">{{
                    userData?.name ?? t("guest")
                  }}</span>
                  <div style="height: 2px" />
                  <div class="membership-level-voucher-count">
                    <span class="membership-tier-container">{{
                      userData?.current_membership?.membership_name ??
                      t("unknown")
                    }}</span>
                    <span class="voucher-count"
                      >{{ userData?.unused_vouchers ?? 0 }}
                      {{ t("vouchers_cap") }}</span
                    >
                  </div>
                </div>
              </div>

              <div style="height: 10px" />

              <!-- Progress Bar -->
              <div class="progress-section">
                <div
                  class="progress-bar"
                  :style="{
                    background: `linear-gradient(to right, ${
                      ColorPick.primaryColor
                    } ${percentageSpend(
                      userData?.current_membership?.total_spent ?? 0,
                      userData?.current_membership?.point_to ?? 0
                    )}%, ${
                      ColorPick.memberProgressBarInActive
                    } ${percentageSpend(
                      userData?.current_membership?.total_spent ?? 0,
                      userData?.current_membership?.point_to ?? 0
                    )}%)`,
                  }"
                >
                  <div
                    :style="{
                      width:
                        percentageSpend(
                          userData?.current_membership?.total_spent ?? 0,
                          userData?.current_membership?.point_to ?? 0
                        ) + '%',
                      backgroundColor: ColorPick.primaryColor,
                    }"
                  >
                    <div
                      class="progress-icon"
                      :style="{
                        left: `calc(${percentageSpend(
                          userData?.current_membership?.total_spent ?? 0,
                          userData?.current_membership?.point_to ?? 0
                        )}%)`,
                      }"
                    >
                      <VImg
                        :src="progressBarIcon"
                        height="25"
                        width="25"
                      ></VImg>
                    </div>
                  </div>
                </div>

                <div style="height: 10px" />

                <div class="progress-header">
                  <p style="text-align: start">
                    {{
                      t("earn_to_upgrade_to")
                        .replace(
                          "[level]",
                          userData?.next_membership?.name ?? t("unknown")
                        )
                        .replace(
                          "[points]",
                          userData?.current_membership?.point_to?.toString() ??
                            "0"
                        )
                    }}
                  </p>

                  <div style="width: 10px" />

                  <p class="progress-text">
                    {{ AppConfig.currencyMark
                    }}{{ userData?.current_membership?.total_spent ?? 0 }} /
                    {{ AppConfig.currencyMark
                    }}{{ userData?.current_membership?.point_to ?? 0 }}
                  </p>
                </div>
              </div>

              <div style="height: 10px" />
              <div style="padding: 0px 20px">
                <VDivider
                  :thickness="1"
                  opacity="1"
                  :color="ColorPick.dividerColor"
                ></VDivider>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="actions">
              <div class="action-item disabled">
                <!-- @click="moveToProductPage(OrderTypeEnum.pickup)" -->
                <div style="height: 6px" />
                <VImg
                  :src="pickupLogo"
                  alt="Pick Up"
                  class="action-pickup-icon"
                />
                <div style="height: 10px" />
                <p>{{ t("pick_up") }}</p>
              </div>

              <VDivider
                :thickness="1"
                length="60"
                opacity="1"
                :vertical="true"
                :color="ColorPick.dividerColor"
              >
              </VDivider>

              <div
                class="action-item"
                @click="moveToProductPage(OrderTypeEnum.delivery)"
              >
                <VImg
                  :src="deliverylogo"
                  alt="Delivery"
                  class="action-delivery-icon"
                />
                <p>{{ t("delivery") }}</p>
              </div>
            </div>
          </div>
          <!-- Membership Card Widget -->

          <div style="height: 20px" />

          <!-- Latest Promotion Widget -->
          <div
            v-if="promotionList?.length > 0 || promotionFeaturedList.length > 0"
            class="latest-promotion-card"
          >
            <VCarousel
              v-if="promotionList?.length > 0"
              cycle
              show-arrows="hover"
              height="150px"
              interval="5000"
              hide-delimiter-background
            >
              <VCarouselItem
                v-for="(slide, i) in promotionList"
                :key="i"
                :src="slide.list_image_url"
                :alt="slide.list_image_url"
                cover
              >
              </VCarouselItem>
            </VCarousel>

            <div
              v-if="
                promotionList?.length > 0 && promotionFeaturedList.length > 0
              "
              style="height: 35px"
            />

            <!-- Latest Promotion Header -->
            <div
              v-if="promotionFeaturedList?.length > 0"
              class="home-see-all"
              @click="moveToVoucher()"
            >
              <span class="home-content-title">{{
                t("latest_promotion_cap")
              }}</span>
              <div
                class="d-flex align-right"
                :style="{
                  alignItems: 'center',
                }"
              >
                <span class="home-content-sub-title">{{ t("more") }}</span>
                <VIcon size="22" :color="ColorPick.homeAarrowColor"
                  >mdi-chevron-right</VIcon
                >
              </div>
            </div>
            <div style="height: 5px" />

            <div v-if="promotionFeaturedList.length > 0" class="promotion-grid">
              <div
                v-for="(promotion, index) in promotionFeaturedList.slice(0, 3)"
                @click="toRewardDetails(promotion.id.toString())"
                :key="promotion.id"
                :class="{
                  'promotion-large': index === 0,
                  'promotion-small': index === 2,
                }"
              >
                <VImg
                  :src="promotion.banner || logo"
                  height="100%"
                  cover
                  class="promotion-img"
                />
              </div>
            </div>
          </div>
          <!-- Latest Promotion Widget -->

          <div style="height: 20px" />

          <!-- Button Content Redirect -->
          <div class="button-container">
            <ButtonContent
              :image="storeLocationImg"
              :title="t('store_location')"
              @click="moveToStoreLocator()"
            />
            <ButtonContent
              :image="voucherImg"
              :title="t('voucher')"
              @click="moveToVoucher"
            />
            <ButtonContent
              :image="ourStory"
              :title="t('our_story')"
              @click="toAboutUs"
            />
          </div>
          <!-- Button Content Redirect -->

          <div v-if="newsList?.length > 0" style="height: 20px" />

          <!-- News and Announcement -->
          <div v-if="newsList?.length > 0" class="news-announcement-card">
            <div
              class="news-announcement-see-all"
              @click="moveToNewsAndAnnouncement()"
            >
              <span class="news-announcement-title">{{
                t("news_and_announcement_cap")
              }}</span>
              <div style="width: 5px" />

              <div
                class="d-flex align-right"
                :style="{
                  alignItems: 'center',
                }"
              >
                <span class="home-content-sub-title">{{ t("more") }}</span>
                <VIcon size="22" :color="ColorPick.homeAarrowColor"
                  >mdi-chevron-right</VIcon
                >
              </div>
            </div>

            <div>
              <div class="news-listing-container">
                <VImg
                  v-for="(item, i) in newsList"
                  @click="moveToNewsAndAnnouncementDetails(item.id.toString())"
                  :key="i"
                  :src="item.single_image_url"
                  :cover="true"
                  height="200px"
                />
              </div>
            </div>
          </div>
          <!-- News and Announcement -->

          <!-- FAB button 014-975 6491-->
          <div class="fab-button" @click="goToWhatsApp()">
            <VFab color="#32D951" :rounded="true" size="45">
              <VIcon color="white" size="30">mdi-whatsapp</VIcon>
            </VFab>
          </div>
          <!-- FAB button -->

          <div style="height: 100px" />
        </div>
      </v-pull-to-refresh>
    </div>
  </div>

  <!-- Required login dialog -->
  <RequiredLogin v-model="openLoginDialog" :isBack="true" />
</template>

<style lang="scss" scoped>
.home {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  text-align: -webkit-center;
}

.home-image-container {
  width: 100%;
  max-width: 600px;
  justify-content: center;
  align-items: center;
}

// Carousel Slider CSS
.carousel__item {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  padding: 20px 0px;
  flex: 0 0 auto;
  width: 100%;
}

/* Enlarge the active slide */
.carousel__item.active {
  transform: scale(1);
  opacity: 1;
  border-radius: 2px;
}

/* Ensure images are responsive */
.carousel-image {
  width: 300px;
  height: 300px;
  /* Set a fixed height */
  object-fit: cover;
  border-radius: 2px;
  // display: flex;
  // white-space: nowrap;
}

.carousel-container {
  display: flex;
  overflow: hidden;
  /* Hide extra slides */
  white-space: nowrap;
}

// Carousel Slider CSS

// Order card CSS
.order-card {
  align-items: flex-start;
  display: flex;
  background: v-bind("ColorPick.secondaryColor");
  color: white;
  padding: 15px 30px;
  width: 100%;
  max-width: 600px;
  flex-direction: column;
  gap: 4px;
  text-align: start;
}

.order-info {
  color: v-bind("ColorPick.primaryColor");
  display: flex;
  align-items: center;
  font-weight: 500;
  font-size: 14px;
}

.order-id {
  background: #2e2e52;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.status-dot {
  width: 10px;
  height: 10px;
  background: v-bind("ColorPick.primaryColor");
  border-radius: 50%;
  margin-right: 3px;
}

.pickup-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.left-content {
  display: flex;
  align-items: center;
  gap: 8px;
  /* Space between dot and text */
}

// Order card CSS

// Membership card CSS
.membership-card {
  background: v-bind("ColorPick.secondaryColor");
  color: white;
  max-width: 600px;
  width: 100%;
  text-align: center;
}

.membership-card-info {
  color: v-bind("ColorPick.primaryColor");
  display: flex;
  padding: 0px 20px;
  justify-content: space-between;
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
  align-items: center;
}

.membership-level-voucher-count {
  width: 100%;
  align-items: center;
  display: flex;
  justify-content: space-between;
}

.membership-tier-container {
  background: #201747;
  color: white;
  padding: 3px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  display: inline-flex; /* 👈 makes the span behave like a flexbox */
  align-items: center; /* 👈 vertical center */
  justify-content: center; /* 👈 horizontal center (optional, if needed) */
  height: 25px;
}

.membership-level {
  color: v-bind("ColorPick.primaryColor");
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}

.voucher-count {
  margin-top: 10px;
  color: v-bind("ColorPick.primaryColor");
  font-weight: 600;
  font-size: 10px;
}

.progress-header {
  color: v-bind("ColorPick.primaryColor");
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  margin-bottom: 10px;
  font-weight: 400;
  align-items: center;
}

/* Progress Bar */
.progress-section {
  padding: 0px 20px;
  text-align: center;
}

.progress-container {
  width: 100%;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 3px;
  background: v-bind("ColorPick.memberProgressBarInActive");
  border-radius: 2px;
}

.progress-icon {
  position: absolute;
  left: 10;
  top: -10px;
  font-size: 14px;
  transform: translateX(-50%);
}

.progress-text {
  display: flex;
  justify-content: space-between;
  font-size: 9px;
  font-weight: 400;
  margin-top: 4px;
}

/* Action Buttons */
.actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 15px;
  color: v-bind("ColorPick.primaryColor");
}

.action-item.disabled {
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none;
}

.action-item {
  flex: 1;
  text-align: center;
  font-size: 15px;
  font-weight: 400;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.action-pickup-icon {
  width: 40px;
  height: 38px;
  filter: grayscale(100%);
}

.action-delivery-icon {
  width: 55px;
  height: 55px;
}

// Membership card CSS

// Latest promotion card Widget Archivo
.latest-promotion-card {
  background: v-bind("ColorPick.secondaryColor");
  color: white;
  padding: 20px 15px;
  max-width: 600px;
  width: 100%;
}

.home-content-title {
  color: v-bind("ColorPick.primaryColor");
  font-size: 16px;
  font-weight: 400;
  text-align: start;
}

.home-content-sub-title {
  color: v-bind("ColorPick.primaryColor");
  font-size: 11px;
  font-weight: 400;
}

.home-see-all {
  color: v-bind("ColorPick.primaryColor");
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  margin-bottom: 10px;
}

.promotion-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 18px;
  padding: 0px 0px;
}

.promotion-large {
  grid-row: span 2;
}

.promotion-small {
  grid-column: 2;
  grid-row: 2;
}

.promotion-img {
  object-fit: cover;
  border-radius: 2px;
}

// Latest promotion card Widget Archivo

// Button Content Redirect
.button-container {
  max-width: 600px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  padding: 15px 30px;
}

// Button Content Redirect

// News Announcement Card Widget
.news-announcement-card {
  background: v-bind("ColorPick.secondaryColor");
  color: white;
  padding: 20px 15px;
  max-width: 600px;
  width: 100%;
}

.news-announcement-title {
  color: v-bind("ColorPick.primaryColor");
  font-size: 16px;
  font-weight: 400;
  text-align: start;
}

.news-announcement-sub-title {
  color: v-bind("ColorPick.primaryColor");
  font-size: 14px;
  font-weight: 500;
}

.news-announcement-see-all {
  color: v-bind("ColorPick.primaryColor");
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  margin-bottom: 10px;
}

.news-listing-container {
  display: flex;
  gap: 15px;
  cursor: pointer;
}

// News Announcement Card Widget

.fab-button {
  position: fixed;
  right: max(30px, calc(50% - 270px));
  bottom: 100px;
  cursor: grab;
  z-index: 1000;
}
</style>
