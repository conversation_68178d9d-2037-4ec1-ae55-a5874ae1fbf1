<script setup lang="ts">
import type { MyOrderDto } from "@/dto/order.dto";

// Components
import TimelineBarPlus from "@/components/custom_control/TimelineBarPlus.vue";

// Apis
import CartsApi from "@/services/carts_api";

// Helpers
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { OrderStatusEnum } from "@/enums/OrderStatusEnum";
import AppConfig from "@/appconfig";
import CapitalizeHelper from "@/helpers/capitalize_helper";

// Images
import placeholder from "@/assets/icons/logo.png";
import success from "@/assets/images/success.png";

// Language
const { t } = useI18n();

// Route
const route = useRoute();
const router = useRouter();

const data = ref<MyOrderDto>();

const isLoading = ref<boolean>(false);
const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>("");
const openSuccessDialog = ref<boolean>(false);

const openRatingSnackBar = ref<boolean>(false);
const ratingSnackBarMsg = ref<string>("");
const openRatingDialog = ref<boolean>(false);
const rating = ref<number>(0);
const reviewText = ref<string>("");

function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

onMounted(() => {
  window.scrollTo(0, 0);

  getOrderDetails();
});

//Api
// Get Available Stores Detail
async function getOrderDetails() {
  try {
    isLoading.value = true;

    data.value = await CartsApi.orderDetails(route.params.id.toString());

    if (data.value.show_msg) {
      openSuccessDialog.value = true;
    }

    if (data.value.review != null) {
      rating.value = data.value.rating ?? 0;
    }
  } catch (error) {
    dialogMsg.value = `${error}`;
    openDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}

// Submit Rating
async function submitRating() {
  try {
    await CartsApi.ratingOrder(
      data?.value?.id.toString() ?? "",
      rating.value,
      reviewText.value
    );

    getOrderDetails();

    openRatingSnackBar.value = true;
    ratingSnackBarMsg.value = t("rating_submitted_successfully");
  } catch (error) {
    console.log(error);
  } finally {
    // Close dialog
    openRatingDialog.value = false;
    rating.value = 0;
    reviewText.value = "";
  }
}
</script>

<template>
  <LoadingPlus v-if="isLoading" />

  <!-- Success Dialog -->
  <DialogPlus
    v-model="openSuccessDialog"
    :onClickConfirm="() => (openSuccessDialog = false)"
  >
    <div class="success-dialog">
      <VImg :src="success" cover height="85" width="85" />

      <div style="height: 30px" />

      <div class="dialog-title" :style="{ color: ColorPick.fontColor }">
        {{ t("thank_you_for_your_order") }}
      </div>
      <div v-if="data?.order_no" style="height: 10px" />
      <div
        v-if="data?.order_no"
        class="dialog-desc"
        :style="{ color: ColorPick.dialogDescription }"
      >
        {{
          t("your_order_successfully_placed").replace(
            "[order_id]",
            data?.order_no ?? t("unknown")
          )
        }}
      </div>
    </div>
  </DialogPlus>

  <!-- Dialog -->
  <DialogPlus
    persistent
    v-model="openDialog"
    :confirmText="t('back')"
    :onClickConfirm="
      () => {
        openDialog = false;
        goBack();
      }
    "
  >
    {{ dialogMsg }}
  </DialogPlus>

  <AppBarPlus :title="t('order_details')" :back="true" />

  <!-- AppBarPlus Spacing -->
  <div style="height: 80px" />

  <div class="order">
    <!-- Order header -->
    <div class="order-container">
      <div
        v-if="data?.status.toLowerCase() === 'completed'"
        class="finish-text"
      >
        {{ t("finished_cap") }}
      </div>
      <div
        v-if="data?.status.toLowerCase() === 'completed'"
        style="height: 20px"
      />
      <div
        v-if="data?.status.toLowerCase() === 'completed'"
        class="finish-sub-text"
      >
        {{ t("thank_you_for_coming") }}
      </div>
      <div
        v-if="data?.status.toLowerCase() === 'completed'"
        style="height: 20px"
      />
      <VBtn
        v-if="data?.status.toLowerCase() === 'completed'"
        rounded="xl"
        class="btn-rate"
        @click="openRatingDialog = true"
        >{{ data?.review !== null ? t("rated_order") : t("rate_order") }}</VBtn
      >

      <!-- Timeline bar -->
      <div style="height: 10px" />
      <TimelineBarPlus
        v-if="
          data?.status.toLowerCase() !== 'cancelled' ||
          data?.status.toLowerCase() !== 'refunded'
        "
        :statusProgress="data?.status_progress"
      />

      <!-- Order Summary -->
      <div class="order-summary">
        <div class="flex item-center">
          <div class="order-details-title">{{ t("order_summary") }}</div>
          <VSpacer />
          <div class="order-status">
            {{ CapitalizeHelper.capitalize(data?.status ?? " ") }}
          </div>
        </div>

        <!-- Product list -->
        <div v-for="product in data?.products?.normal">
          <div class="product">
            <div class="product-image">
              <VImg
                :src="product.image ?? placeholder"
                :cover="true"
                rounded="lg"
                height="80"
                width="80"
              />
            </div>

            <div style="width: 10px" />

            <div class="product-details">
              <div class="flex">
                <h3 class="product-title">{{ product.name }}</h3>
                <VSpacer />
                <div style="width: 10px" />
              </div>

              <div v-if="product.unit_weight !== null" class="product-desc">
                {{ t("weight") }}: {{ product.unit_weight }}
              </div>
              <VSpacer />

              <div class="flex">
                <div
                  class="product-price"
                  :style="{
                    fontSize: '12px',
                  }"
                >
                  {{ AppConfig.currencyMark }}
                  {{ product.total_price ?? "0.00" }}
                </div>
                <VSpacer />
                <div style="width: 10px" />
                <div class="product-count">x{{ product.quantity }}</div>
              </div>
            </div>
          </div>
          <div style="height: 10px" />
        </div>
        <!-- Product list -->

        <!-- Remarks -->
        <div class="flex py3">
          <div class="remarks">{{ t("remarks_to_seller") }}:</div>
          <VSpacer />
          <div class="remarks">{{ data?.remark }}</div>
        </div>

        <!-- Price Details -->
        <div class="payment-details">
          <!-- Subtotal -->
          <div class="flex py3">
            <div class="payment-details-item">{{ t("subtotal") }}</div>
            <VSpacer />
            <div class="payment-details-price">
              {{ AppConfig.currencyMark }} {{ data?.subtotal }}
            </div>
          </div>

          <!-- Delivery Fee -->
          <div class="flex py3">
            <div class="payment-details-item">{{ t("delivery_fee") }}</div>
            <VSpacer />
            <div class="payment-details-price">
              {{ AppConfig.currencyMark }} {{ data?.shipping_fee }}
            </div>
          </div>

          <!-- Delivery Fee -->
          <div v-if="Number(data?.voucher) > 0" class="flex py3">
            <div class="payment-details-item">{{ t("voucher_discount") }}</div>
            <VSpacer />
            <div class="payment-details-price">
              - {{ AppConfig.currencyMark }} {{ data?.voucher }}
            </div>
          </div>

          <!-- Delivery Discount Fee -->
          <div
            v-if="
              Number(data?.delivery_discount) > 0 ||
              String(data?.delivery_discount).toLowerCase() === 'free'
            "
            class="flex py3"
          >
            <div class="payment-details-item">{{ t("voucher_discount") }}</div>
            <VSpacer />
            <div class="payment-details-price">
              - {{ AppConfig.currencyMark }}
              {{
                String(data?.delivery_discount).toLowerCase() === "free"
                  ? data?.shipping_fee
                  : data?.delivery_discount
              }}
            </div>
          </div>

          <!-- Coupon Discount Fee -->
          <div v-if="Number(data?.coupon) > 0" class="flex py3">
            <div class="payment-details-item">{{ t("coupon_discount") }}</div>
            <VSpacer />
            <div class="payment-details-price">
              - {{ AppConfig.currencyMark }}
              {{ data?.coupon }}
            </div>
          </div>

          <!-- Service Tax -->
          <div class="flex py3">
            <div class="payment-details-item">{{ t("service_tax") }}</div>
            <VSpacer />
            <div class="payment-details-price">
              {{ AppConfig.currencyMark }} {{ data?.total_tax }}
            </div>
          </div>

          <div style="height: 15px" />
          <VDivider
            thickness="1"
            :color="ColorPick.primaryColor"
            :opacity="1"
          ></VDivider>
          <div style="height: 20px" />

          <!-- Grand Total -->
          <div class="flex item-center">
            <div class="payment-details-total-item">
              {{ t("total_with_tax") }}
            </div>
            <VSpacer />
            <div class="payment-details-total-price">
              {{ AppConfig.currencyMark }} {{ data?.total }}
            </div>
          </div>
          <div style="height: 20px" />

          <div class="points-earn">
            {{ data?.point_earned }} {{ t("points_awarded") }} !
          </div>
        </div>
      </div>

      <div style="height: 10px" />

      <!-- Order Information -->
      <div class="order-details">
        <div class="order-details-title">{{ t("order_details") }}</div>
        <div style="height: 10px" />
        <VDivider thickness="1" />
        <div style="height: 10px" />

        <!-- Order ID -->
        <div class="flex item-center">
          <div class="order-details-item">{{ t("order_id") }} :</div>
          <VSpacer />
          <div class="order-details-value">{{ data?.order_no }}</div>
        </div>

        <div style="height: 10px" />

        <!-- Tracking Number -->
        <div v-if="data?.tracking_number" class="flex item-center">
          <div class="order-details-item">{{ t("tracking_number") }} :</div>
          <VSpacer />
          <div class="order-details-value">{{ data?.tracking_number }}</div>
        </div>

        <div v-if="data?.tracking_number" style="height: 10px" />

        <!-- Order Time -->
        <div class="flex item-center">
          <div class="order-details-item">{{ t("order_time") }} :</div>
          <VSpacer />
          <div class="order-details-value">{{ data?.created_at }}</div>
        </div>

        <div style="height: 10px" />

        <!-- Payment Time -->
        <div class="flex item-center">
          <div class="order-details-item">{{ t("payment_time") }} :</div>
          <VSpacer />
          <div class="order-details-value">{{ data?.payment_date }}</div>
        </div>

        <div style="height: 10px" />

        <!-- Payment Ref No -->
        <div class="flex item-center">
          <div class="order-details-item">{{ t("payment_ref_no") }} :</div>
          <VSpacer />
          <div class="order-details-value">{{ data?.payment_ref_no }}</div>
        </div>

        <div style="height: 10px" />

        <!-- Paid by -->
        <div class="flex item-center">
          <div class="order-details-item">{{ t("paid_by") }} :</div>
          <VSpacer />
          <div class="order-details-value">{{ data?.payment_method }}</div>
        </div>
      </div>

      <!-- <div>{{ data?.tracking_link }}</div> -->
      <div
        v-if="data?.share_link && data?.status?.toLowerCase() !== 'completed'"
        style="padding: 20px"
      >
        <VBtn
          :href="data?.share_link"
          target="_blank"
          width="100%"
          :style="{
            fontSize: '16px',
            height: '50px',
            maxWidth: '600px',
            textTransform: 'none',
          }"
          >{{ t("track_order") }}</VBtn
        >
      </div>
    </div>
  </div>

  <!-- Rating Dialog -->
  <DialogPlus
    v-model="openRatingDialog"
    :title="t('rate_your_order')"
    :onClickConfirm="data?.review !== null ? null : submitRating"
    :confirmText="t('submit')"
  >
    <div
      style="
        margin-top: -20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
      "
    >
      <!-- Star Rating -->
      <VRating
        :readonly="data?.review !== null"
        v-model="rating"
        color="yellow-darken-3"
        background-color="grey-lighten-1"
        hover
        size="40"
      />
      <div style="height: 20px" />

      <!-- Review Text -->
      <VTextarea
        v-if="data?.review === null"
        v-model="reviewText"
        :label="t('write_your_review')"
        variant="outlined"
        auto-grow
        rows="3"
        style="width: 100%"
      />

      <div v-else style="font-size: 17px; font-weight: 600">
        {{ t("thanks_for_your_review") }}
      </div>
    </div>
  </DialogPlus>

  <!-- Delete Product Snackbar -->
  <SnackBarPlus v-model="openRatingSnackBar">
    {{ ratingSnackBarMsg }}
  </SnackBarPlus>
</template>

<style lang="scss" scoped>
.flex {
  display: flex;
}

.item-center {
  align-items: center;
}

.pointer {
  cursor: pointer;
}

.py3 {
  padding: 3px 0;
}

.order {
  height: 100vh;
  width: 100%;
  display: inline-table;
  text-align: -webkit-center;
}

.order-container {
  max-width: 600px;
}

.finish-text {
  color: v-bind("ColorPick.primaryColor");
  font-size: 18px;
  font-weight: 400;
  text-align: center;
}

.finish-sub-text {
  color: v-bind("ColorPick.primaryColor");
  font-size: 17px;
  font-weight: 400;
  text-align: center;
}

.btn-rate {
  background-color: v-bind("ColorPick.primaryColor");
  color: v-bind("ColorPick.fontColorWhite");
  font-size: 12px;
  font-weight: 500;
  border-radius: 5px;
  padding: 5px 20px;
  height: 35px;
}

.order-summary {
  padding: 20px 15px;
  margin: 10px;
  text-align: left;
  background-color: v-bind("ColorPick.secondaryColor");
}

.order-status {
  background-color: v-bind("ColorPick.primaryColor");
  color: white;
  font-size: 13px;
  font-weight: 500;
  line-height: 13px;
  border-radius: 20px;
  padding: 5px 12px;
}

.product {
  display: flex;
  margin: 15px 0;
  height: 80px;
}

.product-image {
  max-width: 80px;
}

.product-details {
  display: flex;
  flex-direction: column;
  text-align: left;
  flex-grow: 1;
  color: v-bind("ColorPick.primaryColor");
}

.product-title {
  font-size: 12px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}

.product-desc {
  font-size: 11px;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  color: v-bind("ColorPick.primaryColor");
}

.product-price {
  font-size: 11px;
  font-weight: 500;
  color: v-bind("ColorPick.fontColor");
}

.product-count {
  font-size: 11px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
}

.extra-order-details-title {
  font-size: 16px;
  font-weight: 600;
}

.remarks {
  font-size: 14px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}

.payment-details {
  margin-top: 20px;
  color: v-bind("ColorPick.primaryColor");
}

.payment-details-item {
  color: v-bind("ColorPick.primaryColor");
  font-size: 12px;
  font-weight: 500;
}

.payment-details-price {
  color: v-bind("ColorPick.primaryColor");
  font-size: 12px;
  font-weight: 500;
}

.payment-details-total-item {
  color: v-bind("ColorPick.primaryColor");
  font-size: 15px;
  line-height: 15px;
  font-weight: 500;
}

.payment-details-total-price {
  color: v-bind("ColorPick.primaryColor");
  font-size: 15px;
  line-height: 20px;
  font-weight: 500;
}

.order-details-title {
  font-size: 14px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
}

.order-details {
  margin-top: 40px;
  margin-bottom: 50px;
  padding: 20px 15px;
  margin: 10px;
  text-align: left;
  background-color: v-bind("ColorPick.secondaryColor");
}

.order-details-item {
  color: "#344054";
  font-size: 12px;
  font-weight: 500;
}

.order-details-value {
  color: v-bind("ColorPick.primaryColor");
  font-size: 12px;
  font-weight: 500;
}

.success-dialog {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.dialog-title {
  font-size: 24px;
  font-weight: 600;
}

.dialog-desc {
  font-size: 16px;
  font-weight: 500;
}

.points-earn {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
}
</style>
