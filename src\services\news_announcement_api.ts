import AppConfig from "@/appconfig";
import { NewsAnnouncementDto } from "@/dto/news_announcement.dto";
import { PaginationDto } from "@/dto/pagination.dto";
import ApiHelper from "@/helpers/api_helper";
import { translate } from "@/plugins/i18n";

class NewsAnnouncementApi {
  //Get News Announcement List
  public static async list(page: number, perPage?: number): Promise<PaginationDto> {
    try {
      var response = await ApiHelper.get(AppConfig.apiNewsAnnouncementUrl, undefined, {
        "page": page,
        "per_page": perPage ?? AppConfig.paginationLimit,
      });

      const tempNews: PaginationDto = new PaginationDto(response.data.data);

      return tempNews;
    } catch (error) {
      throw error;
    }
  }
  //Get News Announcement List

  //Get News Announcement Details
  public static async detail(id: string): Promise<NewsAnnouncementDto> {
    try {
      var response = await ApiHelper.get(AppConfig.apiNewsAnnouncementUrl + id);

      if (response.data.data == null) {
        throw translate('data_not_found');
      }

      const tempNews: NewsAnnouncementDto = new NewsAnnouncementDto(response.data.data);


      return tempNews;
    } catch (error) {
      throw error;
    }
  }
  //Get News Announcement Details
}

export default NewsAnnouncementApi;
