<script setup lang="ts">
import ColorPick from "@/helpers/colorpick";

import rightArrowIcon from "@/assets/icons/right_arrow.png";

interface Props {
  title?: string | undefined;
  isLast?: string | undefined;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "click"): void;
}>();

const onClick = () => {
  emit("click");
};
</script>

<template>
  <div class="d-flex settings-content" @click="onClick" :style="{}">
    <span class="settings-content-title" style="flex: 1">
      {{ props.title }}
    </span>
    <div class="d-flex" @click="">
      <VImg :src="rightArrowIcon" height="15" width="15"></VImg>
    </div>
  </div>
  <VDivider
    v-if="!props.isLast"
    opacity="1"
    thickness="1.5"
    style="color: #20174799"
  >
  </VDivider>
</template>

<style lang="scss" scoped>
.settings-content {
  padding-inline: 3px;
  padding-block: 10px;
  cursor: pointer;
  transition: all 0.4s ease;
  align-items: center;
}

.settings-content:hover {
  background-color: v-bind("ColorPick.secondaryColor");
}

.settings-content-title {
  font-size: 14px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
  text-align: left;
}
</style>
