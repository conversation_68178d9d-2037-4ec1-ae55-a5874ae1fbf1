<script lang="ts" setup>
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const model = defineModel();

interface Props {
    title?: string; //Show Title
    titleColor?: string,
    required?: boolean;
    placeholder?: string;
    readonly?: boolean;
    rules?: any[];
    errorMessages?: string[];
    itemValue?: string;
    itemTitle?: string;
    items?: any[];
}

const emits = defineEmits(['onChange'])

const props = defineProps<Props>();
</script>

<template>
    <div class="select-plus">
        <div class="text-field-plus-title" :style="{ color: props.titleColor }">
            {{ props.title }} <span class="text-required-field" v-if="props.required">*</span>
        </div>
        <VSelect v-model="model" :items="items" :item-value="itemValue" :item-title="itemTitle"
            :placeholder="placeholder" :readonly="readonly" :rules="rules" :error-messages="errorMessages"
            :no-data-text="t('no_data_found')" @update:model-value="emits('onChange')"></VSelect>
    </div>
</template>

<style lang="scss" scoped>
.select-plus {
    text-align: start;
}

.select-plus-title {
    font-size: 16px;
    font-weight: 600;
    padding-block: 10px 5px;
    text-align: start;
}
</style>