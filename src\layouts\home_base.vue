<script setup lang="ts">
import { VBottomNavigation, VIcon } from "vuetify/components";
import RoutersHelper from "@/helpers/routes_helper";
import HomeIcon from "@/assets/icons/home.png";
import ProductIcon from "@/assets/icons/products.png";
import OrderIcon from "@/assets/icons/orders.png";
import RewardsIcon from "@/assets/icons/rewards.png";
import AccountIcon from "@/assets/icons/account.png";
import { useRouter } from "vue-router";
import ColorPick from "@/helpers/colorpick";
import { useDisplay } from "vuetify";
import { useI18n } from "vue-i18n";
import { onMounted, ref } from "vue";
import localStorageHelper from "@/helpers/localstorage_helper";
import OrderTypeEnum from "@/enums/OrderTypeEnum";

import pickupLogo from "@/assets/icons/pickup.png";
import deliverylogo from "@/assets/icons/delivery.png";

//Variables
const { t } = useI18n();
const router = useRouter();
const { width, xs, smAndUp } = useDisplay();

const openOrderTypeDialog = ref(false);

onMounted(() => {
  //Force overflow scroll
  document.getElementsByTagName("html")[0].style.overflowY = "scroll";
});

// Select Order Type
function selectOrderType(type: string) {
  localStorageHelper.setOrderType(type);
  openOrderTypeDialog.value = false;

  if (
    localStorageHelper.getOutletId() === null &&
    localStorageHelper.getUserToken() !== null
  ) {
    router.push({
      path: RoutersHelper.storeLocator,
      query: { isFromProduct: "true" },
    });
  } else {
    router.push(RoutersHelper.product);
  }
}

// Handle Product Tab
function handleProductTabClick() {
  const orderType = localStorageHelper.getOrderType();

  if (!orderType && localStorageHelper.getUserToken() !== null) {
    openOrderTypeDialog.value = true; // show dialog
    return;
  }

  if (router.currentRoute.value.path !== RoutersHelper.product)
    if (
      localStorageHelper.getOutletId() === null &&
      localStorageHelper.getUserToken() !== null
    ) {
      router.push({
        path: RoutersHelper.storeLocator,
        query: { isFromProduct: "true" },
      });
    } else {
      router.push(RoutersHelper.product);
    }
}
</script>

<template>
  <RouterView />
  <VBottomNavigation
    color="primary"
    class="bottom-navigation-item"
    height="70"
    horizontal
  >
    <VBtn :to="RoutersHelper.home">
      <VIcon size="25">
        <div
          :style="{
            maskImage: `url(${HomeIcon})`,
            backgroundColor:
              router.currentRoute.value.path === RoutersHelper.home
                ? ColorPick.primaryColor
                : ColorPick.onSurfaceColor,
            width: '100%',
            height: '100%',
            maskSize: 'contain',
            maskPosition: 'center',
            maskRepeat: 'no-repeat',
          }"
        ></div>
      </VIcon>
      <div style="height: 5px"></div>
      <span v-if="width > 350">
        {{ t("home") }}
      </span>
    </VBtn>
    <VBtn
      @click="handleProductTabClick"
      :class="
        router.currentRoute.value.path === RoutersHelper.product
          ? 'v-btn--selected v-btn--active text-primary'
          : ''
      "
    >
      <VIcon size=" 25">
        <div
          :style="{
            maskImage: `url(${ProductIcon})`,
            backgroundColor:
              router.currentRoute.value.path === RoutersHelper.product
                ? ColorPick.primaryColor
                : ColorPick.onSurfaceColor,
            width: '100%',
            height: '100%',
            maskSize: 'contain',
            maskPosition: 'center',
            maskRepeat: 'no-repeat',
          }"
        ></div>
      </VIcon>
      <div style="height: 5px"></div>
      <span
        v-if="width > 350"
        :style="{
          color:
            router.currentRoute.value.path === RoutersHelper.product
              ? ColorPick.primaryColor
              : ColorPick.onSurfaceColor,
        }"
      >
        {{ t("products") }}
      </span>
    </VBtn>
    <VBtn
      :to="
        router.currentRoute.value.path === RoutersHelper.orders
          ? ''
          : RoutersHelper.orders
      "
      :class="
        router.currentRoute.value.path === RoutersHelper.orders
          ? 'v-btn--selected v-btn--active text-primary'
          : ''
      "
    >
      <VIcon size="25">
        <div
          :style="{
            maskImage: `url(${OrderIcon})`,
            backgroundColor:
              router.currentRoute.value.path === RoutersHelper.orders
                ? ColorPick.primaryColor
                : ColorPick.onSurfaceColor,
            width: '100%',
            height: '100%',
            maskSize: 'contain',
            maskPosition: 'center',
            maskRepeat: 'no-repeat',
          }"
        ></div>
      </VIcon>
      <div style="height: 5px"></div>
      <span v-if="width > 350">
        {{ t("orders") }}
      </span>
    </VBtn>
    <VBtn
      :to="
        router.currentRoute.value.path === RoutersHelper.rewards
          ? ''
          : RoutersHelper.rewards
      "
      :class="
        router.currentRoute.value.path === RoutersHelper.rewards
          ? 'v-btn--selected v-btn--active text-primary'
          : ''
      "
    >
      <VIcon size="25">
        <div
          :style="{
            maskImage: `url(${RewardsIcon})`,
            backgroundColor:
              router.currentRoute.value.path === RoutersHelper.rewards
                ? ColorPick.primaryColor
                : ColorPick.onSurfaceColor,
            width: '100%',
            height: '100%',
            maskSize: 'contain',
            maskPosition: 'center',
            maskRepeat: 'no-repeat',
          }"
        ></div>
      </VIcon>
      <div style="height: 5px"></div>
      <span
        v-if="width > 350"
        :style="{
          color:
            router.currentRoute.value.path === RoutersHelper.rewards
              ? ColorPick.primaryColor
              : ColorPick.onSurfaceColor,
        }"
      >
        {{ t("rewards") }}
      </span>
    </VBtn>
    <VBtn :to="RoutersHelper.account">
      <VIcon size="25">
        <div
          :style="{
            maskImage: `url(${AccountIcon})`,
            backgroundColor:
              router.currentRoute.value.path === RoutersHelper.account
                ? ColorPick.primaryColor
                : ColorPick.onSurfaceColor,
            width: '100%',
            height: '100%',
            maskSize: 'contain',
            maskPosition: 'center',
            maskRepeat: 'no-repeat',
          }"
        ></div>
      </VIcon>
      <div style="height: 5px"></div>
      <span v-if="width > 350">
        {{ t("account") }}
      </span>
    </VBtn>
  </VBottomNavigation>

  <VDialog v-model="openOrderTypeDialog" width="400">
    <VCard>
      <VCardTitle
        style="text-align: center; color: #201747; font-weight: 600"
        >{{ t("select_order_type") }}</VCardTitle
      >

      <!-- Action Buttons -->
      <div class="actions">
        <div class="action-item disabled">
          <!-- @click="selectOrderType(OrderTypeEnum.pickup)" -->
          <div style="height: 6px" />
          <VImg :src="pickupLogo" alt="Pick Up" class="action-pickup-icon" />
          <div style="height: 10px" />
          <p style="color: #201747">{{ t("pick_up") }}</p>
        </div>

        <VDivider
          :thickness="1"
          length="60"
          opacity="1"
          :vertical="true"
          :color="ColorPick.dividerColor"
        >
        </VDivider>

        <div
          class="action-item"
          @click="selectOrderType(OrderTypeEnum.delivery)"
        >
          <VImg
            :src="deliverylogo"
            alt="Delivery"
            class="action-delivery-icon"
          />
          <p style="color: #201747">{{ t("delivery") }}</p>
        </div>
      </div>
    </VCard>
  </VDialog>
</template>

<style lang="scss">
html {
  overflow-y: scroll !important;
}

.bottom-navigation-item {
  border-radius: v-bind('smAndUp ? "10px 10px 0 0" : "0"');
  max-width: 600px;
  left: 50% !important;
  transform: translateY(0px) translateX(-50%) !important;
}

.v-bottom-navigation .v-bottom-navigation__content > .v-btn {
  min-width: 70px !important;
  max-width: 120px !important;
  font-size: v-bind('xs ? "10px" : "12px"');
}

/* Action Buttons */
.actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 0px;
  color: v-bind("ColorPick.primaryColor");
}

.action-item.disabled {
  cursor: not-allowed;
  opacity: 0.5;
  pointer-events: none;
}

.action-item {
  flex: 1;
  text-align: center;
  font-size: 15px;
  font-weight: 400;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.action-pickup-icon {
  width: 40px;
  height: 38px;
  filter: grayscale(100%);
}

.action-delivery-icon {
  width: 55px;
  height: 55px;
}
</style>
