/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'vue-router'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    '/address/Address': RouteRecordInfo<'/address/Address', '/address/Address', Record<never, never>, Record<never, never>>,
    '/auth/Login': RouteRecordInfo<'/auth/Login', '/auth/Login', Record<never, never>, Record<never, never>>,
    '/auth/Otp': RouteRecordInfo<'/auth/Otp', '/auth/Otp', Record<never, never>, Record<never, never>>,
    '/auth/SetupProfile': RouteRecordInfo<'/auth/SetupProfile', '/auth/SetupProfile', Record<never, never>, Record<never, never>>,
    '/dashboard/Home': RouteRecordInfo<'/dashboard/Home', '/dashboard/Home', Record<never, never>, Record<never, never>>,
    '/dashboard/Orders': RouteRecordInfo<'/dashboard/Orders', '/dashboard/Orders', Record<never, never>, Record<never, never>>,
    '/dashboard/Product': RouteRecordInfo<'/dashboard/Product', '/dashboard/Product', Record<never, never>, Record<never, never>>,
    '/dashboard/Profile': RouteRecordInfo<'/dashboard/Profile', '/dashboard/Profile', Record<never, never>, Record<never, never>>,
    '/dashboard/Rewards': RouteRecordInfo<'/dashboard/Rewards', '/dashboard/Rewards', Record<never, never>, Record<never, never>>,
    '/error/Maintenance': RouteRecordInfo<'/error/Maintenance', '/error/Maintenance', Record<never, never>, Record<never, never>>,
    '/error/NotFound': RouteRecordInfo<'/error/NotFound', '/error/NotFound', Record<never, never>, Record<never, never>>,
    '/newsAnnouncement/NewsAnnouncementDetails': RouteRecordInfo<'/newsAnnouncement/NewsAnnouncementDetails', '/newsAnnouncement/NewsAnnouncementDetails', Record<never, never>, Record<never, never>>,
    '/newsAnnouncement/NewsAnnouncementList': RouteRecordInfo<'/newsAnnouncement/NewsAnnouncementList', '/newsAnnouncement/NewsAnnouncementList', Record<never, never>, Record<never, never>>,
    '/profile/AboutUs': RouteRecordInfo<'/profile/AboutUs', '/profile/AboutUs', Record<never, never>, Record<never, never>>,
    '/profile/Faq': RouteRecordInfo<'/profile/Faq', '/profile/Faq', Record<never, never>, Record<never, never>>,
    '/profile/MemberBenefits': RouteRecordInfo<'/profile/MemberBenefits', '/profile/MemberBenefits', Record<never, never>, Record<never, never>>,
    '/profile/MyProfile': RouteRecordInfo<'/profile/MyProfile', '/profile/MyProfile', Record<never, never>, Record<never, never>>,
    '/profile/MyWishlist': RouteRecordInfo<'/profile/MyWishlist', '/profile/MyWishlist', Record<never, never>, Record<never, never>>,
    '/profile/PrivacyPolicy': RouteRecordInfo<'/profile/PrivacyPolicy', '/profile/PrivacyPolicy', Record<never, never>, Record<never, never>>,
    '/profile/ReferralProgram': RouteRecordInfo<'/profile/ReferralProgram', '/profile/ReferralProgram', Record<never, never>, Record<never, never>>,
    '/profile/Settings': RouteRecordInfo<'/profile/Settings', '/profile/Settings', Record<never, never>, Record<never, never>>,
    '/profile/TermsAndConditions': RouteRecordInfo<'/profile/TermsAndConditions', '/profile/TermsAndConditions', Record<never, never>, Record<never, never>>,
    '/rewards/MyPoints': RouteRecordInfo<'/rewards/MyPoints', '/rewards/MyPoints', Record<never, never>, Record<never, never>>,
    '/rewards/RewardDetails': RouteRecordInfo<'/rewards/RewardDetails', '/rewards/RewardDetails', Record<never, never>, Record<never, never>>,
    '/shop/OrderConfirmation': RouteRecordInfo<'/shop/OrderConfirmation', '/shop/OrderConfirmation', Record<never, never>, Record<never, never>>,
    '/shop/OrderDetails': RouteRecordInfo<'/shop/OrderDetails', '/shop/OrderDetails', Record<never, never>, Record<never, never>>,
    '/shop/OrderPay': RouteRecordInfo<'/shop/OrderPay', '/shop/OrderPay', Record<never, never>, Record<never, never>>,
    '/shop/OrderPayResult': RouteRecordInfo<'/shop/OrderPayResult', '/shop/OrderPayResult', Record<never, never>, Record<never, never>>,
    '/shop/ProductDetails': RouteRecordInfo<'/shop/ProductDetails', '/shop/ProductDetails', Record<never, never>, Record<never, never>>,
    '/SplashScreen': RouteRecordInfo<'/SplashScreen', '/SplashScreen', Record<never, never>, Record<never, never>>,
    '/storeLocator/StoreLocatorDetails': RouteRecordInfo<'/storeLocator/StoreLocatorDetails', '/storeLocator/StoreLocatorDetails', Record<never, never>, Record<never, never>>,
    '/storeLocator/StoreLocatorList': RouteRecordInfo<'/storeLocator/StoreLocatorList', '/storeLocator/StoreLocatorList', Record<never, never>, Record<never, never>>,
    '/test': RouteRecordInfo<'/test', '/test', Record<never, never>, Record<never, never>>,
  }
}
