<script setup lang="ts">
// Helpers
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import { ref, onMounted, onUnmounted, computed, watch } from "vue";

// Components
import ButtonPlus from "@/components/custom_control/ButtonPlus.vue";
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";
import RequiredLogin from "@/components/RequiredLogin.vue";

// Dtos
import type { ProductDetailsDto } from "@/dto/product_details.dto";
import type { SettingDto } from "@/dto/setting.dto";

// Apis
import GeneralPageApi from "@/services/general_page_api";

import RoutersHelper from "@/helpers/routes_helper";
import ProductApi from "@/services/product_api";
import AppConfig from "@/appconfig";
import ThousandsHelper from "@/helpers/thousands_helper";
import CartsApi from "@/services/carts_api";
import localStorageHelper from "@/helpers/localstorage_helper";
import OrderTypeEnum from "@/enums/OrderTypeEnum";

// Variables
const isOrderNow = ref<boolean>(false);
const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");
const isTotalLoading = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>("");

const openLoginDialog = ref<boolean>(false);

const isWishItem = ref<boolean>(false);

// Language
const { t } = useI18n();

// Route
const route = useRoute();
const router = useRouter();

// Fetch Screen Width
const screenWidth = ref(window.innerWidth);

const totalDebounce = ref();

const productData = ref<ProductDetailsDto>();
const settingData = ref<SettingDto>();

const cartNum = ref<number>(0);

const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth;
};

onMounted(() => {
  window.scrollTo(0, 0);
  window.addEventListener("resize", updateScreenWidth);

  if (localStorageHelper.getUserToken() !== null) {
    loadBadge();
    loadSetting();
    getProductDetails();
  } else {
    loadSetting();
    getProductDetails();
  }
});

onUnmounted(() => {
  window.removeEventListener("resize", updateScreenWidth);
});

// Increment and Decrement item
function decrement(productData: ProductDetailsDto) {
  if (productData.item_count > 0) {
    productData.item_count--;
    totalListener();
  }
}
function increment(productData: ProductDetailsDto) {
  productData.item_count++;
  totalListener();
}
// Increment and Decrement item

// Get total Price
const totalPrice = computed(() => {
  const price = productData.value?.price ?? 0;
  const count = productData.value?.item_count ?? 0;
  return price * count;
});

const totalListener = () => {
  isTotalLoading.value = true;
  if (totalDebounce.value ?? false) {
    clearTimeout(totalDebounce.value);
    totalDebounce.value = undefined;
  }

  if (!totalDebounce.value) {
    totalDebounce.value = setTimeout(() => {
      isTotalLoading.value = false;
    }, 400);
  }
};

// Whatsapp
const goToWhatsApp = () => {
  window.open(
    "https://wa.me/" +
      settingData?.value?.support_contact +
      "?text=Hi, I'm interested " +
      productData.value?.name +
      " - " +
      productData.value?.product_subcategory?.name +
      " " +
      "(" +
      productData.value?.sku +
      ")",
    "_blank"
  );
};

function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

// Get Setting
async function loadSetting() {
  try {
    settingData.value = await GeneralPageApi.getSettings();
  } catch (_) {}
}

// Get Available Stores Detail
async function getProductDetails() {
  try {
    isLoading.value = true;

    productData.value = await ProductApi.details(route.params.id.toString());
  } catch (error) {
    dialogMsg.value = `${error}`;
    openDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}

async function addToCartButton() {
  isOrderNow.value = false;
  addToCart();
}
async function orderNowButton() {
  isOrderNow.value = true;
  addToCart();
}

async function addToCart() {
  try {
    var token: string | null = null;
    token = localStorageHelper.getUserToken();

    if (token !== null) {
      if (isOrderNow.value) {
        router.push(RoutersHelper.orderConfirmation);
      }

      if (productData.value!.item_count > 0) {
        await CartsApi.addToCart(
          productData.value?.id,
          productData.value?.item_count
        );
        getProductDetails();
        loadBadge();

        openSnackBar.value = true;
        snackBarMsg.value = t("successfully_added_to_cart");
      } else {
        openSnackBar.value = true;
        snackBarMsg.value = t("please_select_quantity");
      }
    } else {
      openLoginDialog.value = true;
    }
  } catch (error) {}
}

// Cart Badges
async function loadBadge() {
  try {
    cartNum.value = await CartsApi.badge();
  } catch (_) {}
}

// Toggle Wish Item
async function toggleWishItem() {
  try {
    var token: string | null = null;
    token = localStorageHelper.getUserToken();

    if (token !== null) {
      if (productData.value?.is_wishlist === 1) {
        await ProductApi.deleteWishList(productData.value?.id);
      } else {
        await ProductApi.addToWishList(productData.value?.id);
      }

      getProductDetails();
      loadBadge();
    } else {
      openLoginDialog.value = true;
    }
  } catch (error) {
  } finally {
    openSnackBar.value = true;
    snackBarMsg.value =
      productData.value?.is_wishlist === 1
        ? t("successfully_removed_from_wishlist")
        : t("successfully_added_to_wishlist");
  }
}

// Move to Cart (Order Confirmation)
function viewCart() {
  var token: string | null = null;
  token = localStorageHelper.getUserToken();

  if (token !== null) {
    router.push(RoutersHelper.orderConfirmation);
  } else {
    openLoginDialog.value = true;
  }
}
</script>

<template>
  <LoadingPlus v-if="isLoading" />
  <!-- Snack Bar -->
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>

  <AppBarPlus :title="t('product_details')" :back="true">
    <template v-slot:actions>
      <div>
        <VIcon
          size="25"
          :color="
            productData?.is_wishlist === 1
              ? ColorPick.primaryColor
              : ColorPick.shareIconColor
          "
          @click="toggleWishItem"
        >
          {{
            productData?.is_wishlist === 1 ? "mdi-heart" : "mdi-heart-outline"
          }}
        </VIcon>
      </div>

      <div style="width: 10px" />

      <div style="margin-top: 5px">
        <BadgePlus class="pointer" :content="cartNum" @click="viewCart">
          <VIcon size="24" :color="ColorPick.iconColor">mdi-cart-outline</VIcon>
        </BadgePlus>
      </div>
    </template></AppBarPlus
  >

  <!-- AppBarPlus Spacing -->
  <div style="height: 60px" />

  <!-- Body  -->
  <div class="product">
    <div
      class="product-container"
      :style="{
        width: `${screenWidth}px`,
      }"
    >
      <!-- Product Details Data -->
      <div class="product-details">
        <VImg
          v-if="productData?.image_url.length ?? 0 > 0"
          :src="productData?.image_url[0]"
        >
        </VImg>

        <div style="height: 30px" />

        <!-- Term and Conditions -->
        <VContainer v-if="productData?.desc !== null" class="pa-2">
          <p v-html="productData?.desc"></p>
        </VContainer>
        <!-- Term and Conditions -->

        <div style="height: 30px" />

        <div v-for="image in productData?.image_url.slice(1)">
          <VImg :src="image"></VImg>
          <div style="height: 10px" />
        </div>

        <div style="height: 200px" />
      </div>

      <!-- Order Action Bottom -->
      <div class="order-action" style="background-color: white">
        <!-- Order action -->
        <VProgressLinear v-if="isTotalLoading" indeterminate></VProgressLinear>
        <!-- <VDivider /> -->

        <div style="height: 10px" />

        <div class="d-flex">
          <div style="flex: 1; padding-left: 20px">
            <div class="total-price">
              {{ AppConfig.currencyMark }}
              {{
                totalPrice === 0
                  ? ThousandsHelper.format(productData?.price ?? 0, true)
                  : ThousandsHelper.format(totalPrice, true)
              }}
            </div>
            <div v-if="productData?.weight !== null" class="total-grams">
              ({{ productData?.item_count }}x{{ productData?.weight ?? 0 }})
              {{ AppConfig.weightMark }}
            </div>
          </div>
          <div
            v-if="!productData?.is_out_of_stock"
            class="product-variation-counter"
            :class="{
              disabled: productData?.product_category?.online_order === 0,
            }"
          >
            <div class="pointer" @click="decrement(productData!)">
              <VIcon size="18">mdi-minus</VIcon>
            </div>
            <span class="counter-number">{{ productData?.item_count }}</span>
            <div class="pointer" @click="increment(productData!)">
              <VIcon size="18">mdi-plus</VIcon>
            </div>
          </div>
          <div v-else class="product-out-of-stock">
            {{ t("out_of_stock") }}
          </div>
        </div>
        <div v-if="!productData?.is_out_of_stock" style="height: 20px" />
        <div
          v-if="
            productData?.product_category?.online_order === 0 &&
            productData?.is_out_of_stock
          "
          style="height: 20px"
        />

        <div
          v-if="
            productData?.product_category?.online_order !== 0 &&
            !productData?.is_out_of_stock
          "
          class="order-action-button"
        >
          <ButtonPlus
            :disable="isTotalLoading"
            variant="outlined"
            rounded=""
            max-width="600px"
            style="flex: 1"
            @click="orderNowButton()"
            >{{ t("order_now") }}
          </ButtonPlus>
          <div style="width: 25px" />
          <ButtonPlus
            :disable="isTotalLoading"
            rounded=""
            max-width="600px"
            style="flex: 1"
            @click="addToCartButton()"
          >
            {{ t("add_to_cart") }}
          </ButtonPlus>
        </div>

        <div
          v-else-if="productData?.product_category?.online_order === 0"
          class="order-action-button"
        >
          <ButtonPlus
            :disable="isTotalLoading"
            rounded=""
            :width="'100%'"
            max-width="600px"
            @click="goToWhatsApp()"
          >
            {{ t("ask_for_quote") }}
          </ButtonPlus>
        </div>

        <div style="height: 10px" />
      </div>
    </div>
    <!-- Order Action Bottom -->
  </div>

  <!-- Dialog -->
  <DialogPlus
    persistent
    v-model="openDialog"
    :confirmText="t('back')"
    :onClickConfirm="
      () => {
        openDialog = false;
        goBack();
      }
    "
  >
    {{ dialogMsg }}
  </DialogPlus>

  <!-- Required to login -->
  <RequiredLogin v-model="openLoginDialog" :isBack="true" />
</template>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
}

.icon-wrapper {
  display: flex;
  align-items: center;
  // justify-content: center;
  height: 25px;
  width: 25px;
}

// Product
.product {
  width: 100%;
  display: inline-table;
  text-align: -webkit-center;
}

.product-container {
  max-width: 600px;
  background-color: v-bind("ColorPick.secondaryColor");
}

.product-details {
  text-align: left;
}

.product-name {
  font-size: 20px;
  font-weight: 500;
}

.product-description {
  font-size: 15px;
  font-weight: 400;
}
// Product

// Term And Conditions
.terms-title {
  font-weight: bold;
  font-size: 20px;
  text-align: center;
  margin-bottom: 10px;
  color: v-bind("ColorPick.primaryColor");
}

.terms-list p {
  font-size: 14px;
  padding: 10px 15px;
  color: v-bind("ColorPick.primaryColor");
}

.highlighted-link {
  color: #0056b3;
  text-decoration: underline;
  font-weight: bold;
}
// Term And Conditions

// Action Button Bottom
.product-variation-counter {
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid #ccc;
  border-radius: 10px;
  padding: 0px 10px;
  height: 38px;
}

.product-variation-counter.disabled {
  opacity: 0.3;
  pointer-events: none;
}

.counter-icon:hover {
  border-radius: 12px;
  background-color: #ececec;
}

.counter-number {
  font-size: 15px;
  text-align: center;
  cursor: default;
  width: 25px;
  max-width: 600px;
}

.order-action {
  padding: 15px 10px;
  text-align: left;
  width: 100%;
  max-width: 600px;
  position: fixed;
  bottom: 0;
  z-index: 10;
  border-top: solid 1px #0000001a;
}

.total-price {
  font-size: 20px;
  font-weight: 400;
  line-height: 1;
  color: v-bind("ColorPick.primaryColor");
}

.total-grams {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.2;
  color: v-bind("ColorPick.primaryColor");
}

.order-action-button {
  max-width: 600px;
  display: flex;
}
// Action Button Bottom

.v-list-item {
  padding: 0px 0px !important;
}

.product-out-of-stock {
  font-size: 15px;
  font-weight: 600;
  color: red;
  max-lines: 1;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}
</style>
