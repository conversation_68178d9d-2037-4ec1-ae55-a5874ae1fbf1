<script setup lang="ts">
import ColorPick from "@/helpers/colorpick";
import { onMounted } from "vue";

onMounted(() => {
  //Force overflow hidden
  document.getElementsByTagName("html")[0].style.overflow = "hidden";
});
</script>

<template>
  <div class="auth-frame">
    <RouterView />
  </div>
</template>

<style>
html {
  overflow: auto !important;
}

.auth-frame {
  /* background-color: v-bind("ColorPick.primaryColor"); */
  background-size: 850px 500px;
  background-repeat: repeat;
  height: 100vh;
  width: 100%;
  background-image: url("@/assets/images/auth_screen2.png");
}
</style>
