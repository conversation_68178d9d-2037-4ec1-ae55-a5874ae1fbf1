<script setup lang="ts">
import { ref } from 'vue'
import ButtonPlus from '../components/custom_control/ButtonPlus.vue'
import BottomSheetPlus from '../components/custom_control/BottomSheetPlus.vue'
import AppBarPlus from '@/components/custom_control/AppBarPlus.vue';

const openSheet = ref(false);
const openDialog = ref(false);
const text = ref('');

</script>

<template>
  <AppBarPlus title="Test"></AppBarPlus>
  <div style="height: 80px;"></div>
  <div class="text-center">
    <ButtonPlus @click="openSheet = !openSheet">
      Sheet
    </ButtonPlus>
    <ButtonPlus @click="openDialog = !openDialog">
      Dialog
    </ButtonPlus>
    {{ text }}
    <div style="width: 100px">
      <TextFieldPlus title="Test" v-model="text">
      </TextFieldPlus>
    </div>
    <BottomSheetPlus v-model="openSheet" title="Test" :onClickConfirm="() => openSheet = false">
    </BottomSheetPlus>
    <DialogPlus v-model="openDialog" title="Test" :onClickConfirm="() => openDialog = false">
    </DialogPlus>
  </div>
</template>

<style lang="scss" scoped></style>
