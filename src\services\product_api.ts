import AppConfig from "@/appconfig";
import { PaginationDto } from "@/dto/pagination.dto";
import { ProductDto } from "@/dto/product.dto";
import { ProductDetailsDto } from "@/dto/product_details.dto";
import { TotalPriceDto } from "@/dto/total_price.dto";
import ApiHelper from "@/helpers/api_helper";

class ProductApi {
  //Get Product List
  public static async list(): Promise<ProductDto[]> {
    try {
      var response = await ApiHelper.get(AppConfig.apiProductsUrl, undefined);

      if (response.data.data == null) {
        return [];
      }
      const tempProducts: ProductDto[] = response.data.data.map(
        (item: any) => new ProductDto(item)
      );

      return tempProducts;
    } catch (error) {
      throw error;
    }
  }
  //Get Product List

  //Get Product Details
  public static async details(id: string): Promise<ProductDetailsDto> {
    try {
      var response = await ApiHelper.get(
        AppConfig.apiProductsUrl + id,
        undefined
      );

      const tempDetails: ProductDetailsDto = new ProductDetailsDto(
        response.data.data
      );

      return tempDetails;
    } catch (error) {
      throw error;
    }
  }
  //Get Product Details

  // Add Wishlist Product
  public static async addToWishList(product_id?: number): Promise<any> {
    try {
      var response = await ApiHelper.post(
        AppConfig.apiUserWishlistAddUrl,
        undefined,
        {
          product_id: product_id,
        }
      );
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  // Delete WishList Product
  public static async deleteWishList(id: number): Promise<any> {
    try {
      var response = await ApiHelper.delete(
        `${AppConfig.apiUserWishlistDeleteUrl}/${id}`,
        undefined
      );

      if (response.status) {
        return response.status;
      } else {
        return response.message;
      }
    } catch (error) {
      throw error;
    }
  }

  // Get Wishlist List
  public static async wishList(page: number, perPage?: number): Promise<PaginationDto> {
    try {
      var response = await ApiHelper.get(AppConfig.apiUserWishUrl, undefined, {
        page: page,
        per_page: perPage ?? AppConfig.paginationLimit,
      });

      const tempWishlist: PaginationDto = new PaginationDto(
        response.data.data
      );

      return tempWishlist;
    } catch (error) {
      throw error;
    }
  }

  // Cart update address 
  public static async updateProductQuantity(cart_product_id?: number, type?: string): Promise<any> {
    try {
      var response = await ApiHelper.post(
        AppConfig.apiProductQuantityUrl,
        undefined,
        {
          cart_product_id: cart_product_id,
          type: type
        }
      );

      return response.data.data;
    } catch (error) {
      console.log('error', error);
      throw error;
    }
  }
  // Cart update address
}

export default ProductApi;
