<script setup lang="ts">
import ButtonPlus from '@/components/custom_control/ButtonPlus.vue';
import { useI18n } from 'vue-i18n';
import ColorPick from '@/helpers/colorpick';

//Translations
const { t } = useI18n()


//Check if current path is dashboard redirect to dashboard
function goBackToHome(): string {
  return '/'
}

</script>

<template>
  <LogoWithContent class="logo-with-content-base">
    <ErrorHeader statusCode="503" :title="t('under_maintenance')"
      :description="t('under_maintenance_description')"  :style="{color: ColorPick.fontColorWhite}" />
    <div class="error-back-to-home-button">
      <ButtonPlus :to="goBackToHome()" @click="() => { }" class="mt-10 max-width-handle" variant="flat" :color="ColorPick.buttonColorAuth">
        {{ t('try_again') }}
      </ButtonPlus>
    </div>
  </LogoWithContent>
</template>

<style lang="scss" scoped>

.max-width-handle {
  inline-size: 100%;
  max-inline-size: 600px;
  text-align: center;
}

.error-back-to-home-button {
  display: flex;
  justify-content: center;
}
</style>
