<script lang="ts" setup>
import { onMounted } from 'vue';

onMounted(() => {
    //Force overflow scroll
    document.getElementsByTagName("html")[0].style.overflowY = "scroll";
})
</script>

<template>
  <div class="layout-wrapper layout-blank">
    <RouterView />
  </div>
</template>

<style>
html {
    overflow-y: scroll !important;
}


.layout-wrapper.layout-blank {
  flex-direction: column;
}
</style>
