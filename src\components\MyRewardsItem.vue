<script setup lang="ts">
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";
import placeholder from "@/assets/icons/logo.png";
import { computed } from "vue";

// Language
const { t } = useI18n();

interface Props {
  image?: string | undefined;
  status?: string | undefined;
  statusColor?: string | undefined;
  title?: string | undefined;
  description?: string | undefined;
  validDate?: string | undefined;
}

const props = withDefaults(defineProps<Props>(), {
  statusColor: ColorPick.statusActive as string,
});

const emit = defineEmits<{
  (e: "click"): void;
}>();

const onClick = () => {
  emit("click");
};

const statusColor = computed(() => {
  switch (props.status?.toLowerCase()) {
    case "active":
      return ColorPick.statusActive;
    case "expired":
      return ColorPick.statusExpired;
    default:
      return ColorPick.statusExpired;
  }
});
</script>

<template>
  <div class="my-rewards" @click="onClick">
    <div class="my-reward-image">
      <VImg
        :src="props.image ?? placeholder"
        :cover="true"
        rounded="lg"
        height="100%"
      />
    </div>

    <div style="width: 8px" />

    <div class="my-reward-details">
      <div class="my-reward-status" :style="{ backgroundColor: statusColor }">
        {{ props.status }}
      </div>
      <h3 class="my-reward-details-title">
        {{ props?.title }}
      </h3>
      <div style="height: 2px" />
      <div class="my-reward-details-desc">
        {{ props?.description }}
      </div>

      <div style="height: 10px" />

      <div class="my-reward-details-exp">
        {{ t("valid_until") }}
        {{ props.validDate }}
      </div>
    </div>
  </div>
  <!-- Show Voucher -->
</template>

<style lang="scss" scoped>
.my-rewards {
  display: flex;
  margin: 10px 10px;
  align-items: center;
  padding: 10px 10px;
  background-color: white;
  height: 100px;
  cursor: pointer;
}

.my-reward-details {
  display: flex;
  flex-direction: column;
  text-align: left;
  flex-grow: 1;
}

.my-reward-status {
  padding: 1px 12px;
  border-radius: 20px;
  margin-left: auto;
  font-size: 9px;
  color: white;
}

.my-reward-details-title {
  font-size: 14px;
  font-weight: 400;
}

.my-reward-image {
  height: 100%;
  aspect-ratio: 6 / 4;
}

.my-reward-details-desc {
  font-size: 9px;
  font-weight: 400;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  color: v-bind("ColorPick.greyFontColor02");
}

.my-reward-details-exp {
  font-size: 7px;
  font-weight: 400;
  color: v-bind("ColorPick.voucherValidUntilColor");
}
</style>
