<script lang="ts" setup>
import ColorPick from '@/helpers/colorpick';

const model = defineModel<string>();
interface Props {
    title?: string; //Show Title
    placeholder?: string;
    appendInnerIcon?: string;
    prependInnerIcon?: string;
    type?: string;
    rules?: any[];
    style?: any;
    errorMessages?: string[];
    required?: boolean;
    focused?: boolean;
    variant?: "filled" | "underlined" | "outlined" | "plain" | "solo" | "solo-inverted" | "solo-filled" | undefined;
    clearable?: boolean;
    persistentClear?: boolean;
    readonly?: boolean;
    onClickAppendInner?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
    clearable: false,
    persistentClear: false,
})
</script>

<template>
    <div class="text-field-plus">
        <div v-if="props.title " class="text-field-plus-title">
            {{ props.title }} <span class="text-required-field" v-if="props.required">*</span>
        </div>
        <VTextField v-model="model" :type="type" :placeholder="placeholder" :append-inner-icon="appendInnerIcon"
            :prepend-inner-icon="prependInnerIcon" :rules="rules" @click:append-inner="onClickAppendInner"
            :errorMessages="errorMessages" :style="style" :focused="focused" :variant="variant" :clearable="clearable"
            :persistentClear="persistentClear" :readonly="readonly">
            <template v-slot:prepend-inner>
                <slot name="prepend-inner"></slot>
            </template>
            <template v-slot:append-inner>
                <slot name="append-inner"></slot>
            </template>
            <slot></slot>
        </VTextField>
    </div>
</template>

<style lang="scss">
.text-field-plus {
    text-align: start;
}

.text-field-plus-title {
    font-size: 16px;
    font-weight: 600;
    padding-block: 10px 5px;
    text-align: start;
    color: v-bind('ColorPick.fontColor');
}

.v-field {
    border-radius: 10px !important;
}

.v-card-text {
    padding: 0 !important;
}
</style>