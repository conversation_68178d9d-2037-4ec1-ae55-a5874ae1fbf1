export class PaginationDto {
  current_page: number | undefined;
  last_page: number | undefined;
  per_page: number | undefined;
  total: number | undefined;
  data?: any;

  constructor(data: any) {
    try {
      this.data = data.data;
    } catch (_) {}

    this.current_page = data?.current_page;
    this.last_page = data?.last_page;
    this.per_page = data?.per_page;
    this.total = data?.total;
  }
}