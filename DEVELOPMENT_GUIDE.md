# MyBolehBoleh Frontend Development Guide

## Table of Contents
1. [Technology Stack](#technology-stack)
2. [Project Architecture](#project-architecture)
3. [Folder Structure](#folder-structure)
4. [Code Patterns & Standards](#code-patterns--standards)
5. [Development Workflow](#development-workflow)
6. [API Integration](#api-integration)
7. [Component Development](#component-development)
8. [State Management](#state-management)
9. [Internationalization (i18n)](#internationalization-i18n)
10. [Styling Guidelines](#styling-guidelines)
11. [Build & Deployment](#build--deployment)
12. [Best Practices](#best-practices)

---

## Technology Stack

### Core Framework
- **Vue 3** with Composition API
- **TypeScript** for type safety
- **Vite** as build tool and dev server

### UI Framework & Styling
- **Vuetify 3** - Material Design component library
- **SCSS/Sass** for custom styling
- **Material Design Icons** (@mdi/font)
- **Remixicon** for additional icons

### Routing & Navigation
- **Vue Router 4** with file-based routing (unplugin-vue-router)
- Automatic route generation from file structure

### State & Data Management
- **Local Component State** with Vue 3 Composition API
- **LocalStorage** for data persistence (via custom helper)
- **Axios** for HTTP requests
- **No centralized state management** (Pinia/Vuex not implemented)

### Additional Libraries
- **Vue I18n** for internationalization
- **Google Maps API** integration
- **Moment.js** for date handling
- **Vue Tel Input** for phone number inputs
- **Vue3 Carousel** for image carousels

---

## Project Architecture

### Mobile-First Design
This application is primarily designed for **mobile devices** with responsive considerations. The architecture includes:
- Mobile viewport optimization (320px width focus)
- Touch-friendly UI components
- Progressive Web App (PWA) capabilities

### Plugin-Based Architecture
The application uses a modular plugin system for registering services:

```typescript
// Main entry point registers all plugins
registerPlugins(app) // Automatically scans src/plugins/
```

### Auto-Import System
- **Components**: Automatically imported from `src/components/` and `src/@core/components/`
- **Composables**: Vue, Vue Router, and VueUse functions auto-imported
- **Types**: TypeScript definitions auto-generated

---

## Folder Structure

```
src/
├── @core/                  # Core utilities and components
│   └── utils/             # Shared utilities (plugins, helpers)
├── @layouts/              # Layout components and styles
│   └── styles/           # Global SCSS files
├── assets/               # Static assets
│   ├── dummy/           # Mock data and images
│   ├── icons/           # Custom icons
│   └── images/          # App images
├── components/           # Reusable Vue components
│   ├── address/         # Address-related components
│   └── custom_control/  # Custom form controls
├── dto/                 # Data Transfer Objects (TypeScript interfaces)
├── enums/               # TypeScript enums
├── helpers/             # Utility helper classes
├── interfaces/          # TypeScript interfaces
├── layouts/             # Page layout components
├── locales/             # i18n translation files
├── pages/               # Page components (auto-routing)
├── plugins/             # Vue plugins configuration
├── services/            # API service classes
└── styles/              # Global styles and Vuetify customization
```

### Naming Conventions

| Type | Convention | Example |
|------|------------|---------|
| Components | PascalCase | `ButtonContent.vue` |
| Pages | PascalCase | `SplashScreen.vue` |
| Services | snake_case with _api suffix | `auth_api.ts` |
| DTOs | PascalCase with .dto suffix | `user.dto.ts` |
| Helpers | snake_case with _helper suffix | `api_helper.ts` |
| Enums | PascalCase with Enum suffix | `OrderStatusEnum.ts` |

---

## Code Patterns & Standards

### Vue Component Structure
Follow this standard structure for all Vue components:

```vue
<script setup lang="ts">
// 1. Imports
import { ref, computed, onMounted } from 'vue'
import type { ComponentProps } from '@/interfaces/component.interface'

// 2. Props definition
interface Props {
  title?: string
  isVisible?: boolean
}
const props = defineProps<Props>()

// 3. Emits definition
const emit = defineEmits<{
  (e: 'click'): void
  (e: 'update', value: string): void
}>()

// 4. Reactive state
const isLoading = ref(false)

// 5. Computed properties
const displayTitle = computed(() => props.title || 'Default Title')

// 6. Methods
const handleClick = () => {
  emit('click')
}

// 7. Lifecycle hooks
onMounted(() => {
  // Initialization logic
})
</script>

<template>
  <!-- Template with semantic structure -->
  <div class="component-container">
    <h2 class="title">{{ displayTitle }}</h2>
    <VBtn @click="handleClick">
      {{ $t('common.click_me') }}
    </VBtn>
  </div>
</template>

<style lang="scss" scoped>
.component-container {
  // Component-specific styles
}
</style>
```

### TypeScript Standards

#### 1. DTO (Data Transfer Objects)
Create typed interfaces for all API responses:

```typescript
// src/dto/user.dto.ts
export class UserDto {
  point_balance: number
  accumulated_points: number
  name: string
  phone_number: string
  email_address: string
  
  constructor(data: any) {
    this.point_balance = data.point_balance || 0
    this.name = data.name || ''
    // ... initialize all properties
  }
}
```

#### 2. API Services
All API calls should be centralized in service classes:

```typescript
// src/services/auth_api.ts
class AuthPageApi {
  public static async sendOtp(phone: string): Promise<any> {
    try {
      const response = await ApiHelper.post(AppConfig.apiSendOtpUrl, undefined, {
        phone_number: phone,
      })
      return response.data.data
    } catch (error) {
      throw error
    }
  }
}
```

#### 3. Helper Classes
Create helper classes for common utilities:

```typescript
// src/helpers/api_helper.ts
class ApiHelper {
  private static getHeaders(): object {
    // Header logic
  }
  
  public static async post(url: string, headers?: any, data?: any) {
    // HTTP post logic
  }
}
```

---

## Development Workflow

### Getting Started
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Type checking
npm run type-check

# Linting
npm run lint
```

### Environment Configuration
- **Development**: `http://localhost:8000/` (in appconfig.ts)
- **Production**: Configure `BACKEND_URL` environment variable
- **Port**: Development server runs on port 3000

### File-Based Routing
Pages are automatically routed based on file structure in `src/pages/`:
- `src/pages/auth/login.vue` → `/auth/login`
- `src/pages/dashboard/index.vue` → `/dashboard`
- `src/pages/profile/[id].vue` → `/profile/:id`

---

## API Integration

### Configuration
API endpoints are centralized in `src/appconfig.ts`:

```typescript
class AppConfig {
  static backendUrl: string = import.meta.env.BACKEND_URL || "http://localhost:8000/"
  static apiUrl: string = `${AppConfig.backendUrl}api/`
  // Define all API endpoints here
}
```

### API Helper Pattern
All HTTP requests use the centralized `ApiHelper`:

```typescript
// Automatic token handling
// Error handling and status code checking
// Header management
const response = await ApiHelper.post(url, headers, data)
```

### Error Handling
- 401 responses trigger automatic logout
- Network errors are caught and handled consistently
- Use try-catch blocks in service methods

---

## Component Development

### Component Categories

1. **Layout Components** (`src/layouts/`)
   - `blank.vue` - Minimal layout
   - `home_base.vue` - Main app layout
   - `image_bg_frame.vue` - Background image layout

2. **Reusable Components** (`src/components/`)
   - Follow single responsibility principle
   - Use TypeScript interfaces for props
   - Emit events for parent communication

3. **Page Components** (`src/pages/`)
   - Top-level route components
   - Use appropriate layouts
   - Handle data fetching and state

### Props and Events Pattern
```vue
<script setup lang="ts">
interface Props {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '',
  disabled: false
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string]
}>()
</script>
```

---

## State Management

⚠️ **Important Note**: This project currently **does NOT use any centralized state management solution** like Pinia, Vuex, or similar libraries. State is managed through a combination of local component state, local storage, and prop/event communication.

### Current State Management Approach

#### 1. Local Component State
Each component manages its own state using Vue 3 Composition API:

```typescript
// Example from any page component
const isLoading = ref<boolean>(false)
const openSnackBar = ref<boolean>(false)
const snackBarMsg = ref<string>("")
const data = ref<UserDto>()
```

#### 2. Local Storage for Persistence
User authentication and app preferences are stored in localStorage:

```typescript
// Centralized local storage management via helper
import localStorageHelper from '@/helpers/localstorage_helper'

// Store user token
localStorageHelper.setUserToken(token)
localStorageHelper.setOrderType(OrderTypeEnum.delivery)
localStorageHelper.setOutletId(outletId)

// Retrieve data
const token = localStorageHelper.getUserToken()
const orderType = localStorageHelper.getOrderType()
```

#### 3. Parent-Child Communication
State is passed between components via props and events:

```vue
<!-- Parent component -->
<AddressList 
  :canSelect="true" 
  :onSelect="handleAddressSelect" 
/>

<!-- Child component emits events -->
const emit = defineEmits<{
  (e: 'select', addressId: string): void
}>()
```

#### 4. API-Based State Synchronization
Data is fetched fresh from APIs in each component that needs it:

```typescript
// Each component fetches its own data
onMounted(async () => {
  try {
    isLoading.value = true
    userData.value = await AuthPageApi.getProfile()
    cartData.value = await CartsApi.getCartSummary()
  } catch (error) {
    // Handle error
  } finally {
    isLoading.value = false
  }
})
```

### State Management Patterns Used

#### Authentication State
```typescript
// Check authentication status throughout the app
const token = localStorageHelper.getUserToken()
if (token) {
  // User is logged in
  await loadUserData()
} else {
  // Show login prompt
  openLoginDialog.value = true
}
```

#### Loading States
```typescript
// Each component manages its own loading state
const isLoading = ref<boolean>(false)
const isTotalLoading = ref<boolean>(false)

// Usage in API calls
try {
  isLoading.value = true
  const response = await SomeApi.getData()
} finally {
  isLoading.value = false
}
```

#### Error Handling
```typescript
// Each component handles its own errors
const openSnackBar = ref<boolean>(false)
const snackBarMsg = ref<string>("")

try {
  // API call
} catch (error) {
  snackBarMsg.value = `${error}`
  openSnackBar.value = true
}
```

### Limitations of Current Approach

1. **State Duplication**: Same data may be fetched multiple times across components
2. **No Global State**: User profile, cart count, etc. are re-fetched in each component
3. **Manual Synchronization**: Changes in one component don't automatically update others
4. **Performance Impact**: Multiple API calls for the same data
5. **Complex Prop Drilling**: Data must be passed through multiple component levels

### When Adding Centralized State Management

If the project grows and requires centralized state management, consider:

1. **Pinia** (recommended for Vue 3):
```typescript
// stores/auth.ts
export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isAuthenticated: false
  }),
  actions: {
    async login(credentials) {
      // Login logic
    }
  }
})
```

2. **Key stores to implement**:
   - `useAuthStore` - User authentication and profile
   - `useCartStore` - Shopping cart state
   - `useUIStore` - Loading states, notifications
   - `useLocationStore` - Selected outlet, delivery address

### Current State Flow Pattern

```
Component Mount
    ↓
Check localStorage for auth token
    ↓
Fetch required data from API
    ↓
Store in local component state
    ↓
Update UI with loading/error states
```

### Best Practices for Current Architecture

1. **Consistent Error Handling**: Use the same error pattern across components
2. **Centralized API Configuration**: Keep all API endpoints in `appconfig.ts`
3. **Helper Utilities**: Use `localStorageHelper` for all localStorage operations
4. **Loading States**: Always show loading indicators for async operations
5. **Token Management**: Handle token expiration consistently across all API calls

---

## Internationalization (i18n)

### Setup
- Translations stored in `src/locales/`
- Auto-import configured for translation files
- Default locale: `en-US`

### Usage in Components
```vue
<template>
  <div>
    <h1>{{ $t('pages.home.title') }}</h1>
    <p>{{ $t('pages.home.description') }}</p>
  </div>
</template>
```

### Usage in JavaScript
```typescript
import { translate } from '@/plugins/i18n'

const title = translate('pages.home.title')
```

### Translation File Structure
```json
{
  "app_name": "MyBolehBoleh",
  "pages": {
    "home": {
      "title": "Welcome",
      "description": "Your loyalty app"
    }
  },
  "common": {
    "loading": "Loading...",
    "error": "An error occurred"
  }
}
```

---

## Styling Guidelines

### SCSS Structure
- Global styles in `src/@layouts/styles/`
- Component-specific styles use `scoped`
- Vuetify customization in `src/styles/settings.scss`

### Vuetify Integration
```vue
<template>
  <!-- Use Vuetify components -->
  <VContainer>
    <VRow>
      <VCol cols="12" md="6">
        <VBtn color="primary">Click Me</VBtn>
      </VCol>
    </VRow>
  </VContainer>
</template>
```

### Color System
Centralized color management in `src/helpers/colorpick.ts`:

```typescript
class ColorPick {
  static primaryColor = '#your-primary-color'
  static secondaryColor = '#your-secondary-color'
}
```

### Mobile-First Approach
```scss
// Mobile first (default)
.component {
  padding: 1rem;
  
  // Tablet and up
  @media (min-width: 768px) {
    padding: 2rem;
  }
  
  // Desktop and up
  @media (min-width: 1024px) {
    padding: 3rem;
  }
}
```

---

## Build & Deployment

### Build Commands
```bash
# Type checking
npm run type-check

# Production build
npm run build

# Preview production build
npm run preview
```

### Environment Variables
- `BACKEND_URL` - API backend URL
- Set in deployment environment or `.env` files

### Build Output
- Built files in `dist/` directory
- Static assets optimized and versioned
- TypeScript compiled and tree-shaken

---

## Best Practices

### Code Organization
1. **Single Responsibility**: Each component/service has one clear purpose
2. **Consistent Naming**: Follow established naming conventions
3. **Type Safety**: Use TypeScript interfaces and types
4. **Error Handling**: Implement proper try-catch blocks
5. **Performance**: Use `computed` for derived state, `v-memo` for expensive renders

### Component Design
1. **Props Validation**: Always define prop types
2. **Event Emission**: Use typed emits
3. **Scoped Styles**: Avoid global style pollution
4. **Accessibility**: Include proper ARIA attributes
5. **Responsive Design**: Test on multiple screen sizes

### API Integration
1. **Centralized Configuration**: Use AppConfig for all URLs
2. **Error Handling**: Consistent error response handling
3. **Loading States**: Show loading indicators for async operations
4. **Type Safety**: Create DTOs for all API responses
5. **Authentication**: Automatic token management

### Performance Optimization
1. **Lazy Loading**: Use dynamic imports for large components
2. **Asset Optimization**: Compress images and minimize bundles
3. **Caching**: Implement appropriate caching strategies
4. **Bundle Analysis**: Regularly check bundle size

### Testing Considerations
1. **Component Testing**: Test component props, events, and rendering
2. **API Testing**: Mock API responses for unit tests
3. **E2E Testing**: Test critical user flows
4. **Type Checking**: Run TypeScript checks in CI/CD

### Security Best Practices
1. **Token Storage**: Secure token storage and automatic renewal
2. **Input Validation**: Validate all user inputs
3. **XSS Prevention**: Sanitize user-generated content
4. **HTTPS**: Use HTTPS in production
5. **Environment Variables**: Keep sensitive data in environment variables

---

## Common Patterns to Follow

### API Service Pattern
```typescript
class ExampleApi {
  public static async getData(id: string): Promise<DataDto> {
    try {
      const response = await ApiHelper.get(`${AppConfig.apiUrl}data/${id}`)
      return new DataDto(response.data.data)
    } catch (error) {
      throw error
    }
  }
}
```

### Component Composition Pattern
```vue
<script setup lang="ts">
// Composable for data fetching
const { data, loading, error } = useAsyncData()

// Composable for form validation
const { validate, errors } = useFormValidation()
</script>
```

### Route Meta Pattern
```typescript
// In route definition
{
  path: '/profile',
  component: () => import('@/pages/profile/index.vue'),
  meta: {
    title: 'Profile',
    requiresAuth: true
  }
}
```

---

This guide should be updated as the project evolves and new patterns emerge. Always prioritize consistency with existing code patterns over personal preferences.