<script setup lang="ts">
// Helpers
import localStorageHelper from "@/helpers/localstorage_helper";
import RoutersHelper from "@/helpers/routes_helper";
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { ref, onMounted, onUnmounted, watch, nextTick, watchEffect } from "vue";

// Apis
import CartsApi from "@/services/carts_api";
import storeLocatorApi from "@/services/homepage_store_locator_api";

// Dtos
import { ProductDataDto, ProductDto } from "@/dto/product.dto";
import {
  StoreLocatorDistanceDto,
  StoreLocatorDto,
  StoreLocatorPaginationDto,
} from "@/dto/store_locators.dto";

// Components
import BadgePlus from "@/components/custom_control/BadgePlus.vue";
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";
import RequiredLogin from "@/components/RequiredLogin.vue";

// Images
import storeIcon from "@/assets/icons/store_icon.png";
import placeholder from "@/assets/icons/logo.png";

import ProductApi from "@/services/product_api";
import OrderTypeEnum from "@/enums/OrderTypeEnum";
import { useDisplay } from "vuetify";
import HomePageStoreLocatorApi from "@/services/homepage_store_locator_api";

import AppConfig from "@/appconfig";
import ThousandsHelper from "@/helpers/thousands_helper";
import NoData from "@/components/custom_control/NoData.vue";

// Routers
const { t } = useI18n();
const router = useRouter();
const { md } = useDisplay();

// General Variable
const isLoading = ref<boolean>(false);
const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>("");
const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

// Variables

// Location Variable
const latitude = ref(0);
const longitude = ref(0);
const error = ref("");

const productList = ref<ProductDto[]>();
const outletDistance = ref<StoreLocatorDistanceDto>();
const storeLocationList = ref<StoreLocatorPaginationDto[]>();

const activeCategory = ref<number>(0);
const productSections = ref<HTMLElement[]>([]);
const productListRef = ref<HTMLElement | null>(null);

const openLoginDialog = ref<boolean>(false);

const orderType = ref<string | null>();
const outletId = ref<string | null>();
const outletInfo = ref<StoreLocatorDto | null>();
const cartNum = ref<number>(0);
const count = ref(0);

const token = localStorageHelper.getUserToken();

// Handle Scroll category and product section
const scrollToCategory = (index: number) => {
  if (!productSections.value || !productSections.value[index]) {
    return;
  }
  const section = productSections.value[index];
  section.scrollIntoView({
    behavior: "smooth",
    block: "start",
  });

  // Force scroll for index 0
  if (index === 0) {
    setTimeout(() => {
      productListRef.value?.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }, 100);
  }
};

const handleScroll = () => {
  if (!productListRef.value) return;
  let scrollPosition = productListRef.value.scrollTop;
  productSections.value.forEach((section, index) => {
    if (section.offsetTop - 250 <= scrollPosition) {
      activeCategory.value = index;
    }
  });
};

watchEffect(() => {
  productSections.value = Array.from(
    document.querySelectorAll(".product-section")
  );
});
// Handle Scroll category and product section

// Fetch Screen Width
const screenWidth = ref(window.innerWidth);
const screenHeight = ref(window.innerHeight);

const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth;
};
const updateScreenHeight = () => {
  screenHeight.value = window.innerHeight;
};

// Function
function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

// Get Current Location
async function getCurrentLocation(): Promise<{ lat: number; lng: number }> {
  return new Promise((resolve, reject) => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;
          latitude.value = lat;
          longitude.value = lng;
          error.value = "";
          resolve({ lat, lng });
        },
        (err) => {
          error.value = `Error: ${err.message}`;
          reject(err);
        }
      );
    } else {
      const errMsg = "Geolocation is not supported by this browser.";
      error.value = errMsg;
      reject(new Error(errMsg));
    }
  });
}

// Function - API
// Fetch Product List
async function loadProductListing() {
  try {
    isLoading.value = true;
    productList.value = (await ProductApi.list()) ?? [];
  } catch (error) {
    console.log("error", error);
    dialogMsg.value = `${error}`;
    openDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}

// Cart Badges
async function loadBadge() {
  try {
    cartNum.value = await CartsApi.badge();
  } catch (_) {}
}

// Get Outlet Distance
async function getDistance() {
  try {
    // get Current Location
    const { lat, lng } = await getCurrentLocation();

    outletDistance.value = await storeLocatorApi.storeLocatorDistance(
      lat,
      lng,
      "1"
    );
  } catch {}
}

// Fetch Store Location List
async function loadStoreLocatorList() {
  try {
    const response = await HomePageStoreLocatorApi.storeLocatorList(
      1,
      50,
      "",
      ""
    );

    outletId.value = localStorageHelper.getOutletId();

    if (outletId !== null) {
      const storeList = response.data;

      if (storeList && Array.isArray(storeList)) {
        const matchedStore = storeList.find(
          (store) => store.id === Number(outletId.value)
        );

        if (matchedStore) {
          outletInfo.value = matchedStore;
        } else {
          localStorageHelper.clearOutletId();
        }
      }
    }
  } catch (error) {
    dialogMsg.value = `${error}`;
    openDialog.value = true;
  } finally {
  }
}

// Decrement and Decrement item
async function decrement(product: ProductDataDto) {
  if (token) {
    if ((product.in_cart?.quantity ?? 0) > 1) {
      try {
        await ProductApi.updateProductQuantity(
          product?.in_cart?.cart_product_id,
          "minus"
        );

        openSnackBar.value = true;
        snackBarMsg.value = t("item_removed_from_cart");
        loadBadge();
        loadProductListing();

        openSnackBar.value = true;
        snackBarMsg.value = t("item_removed_from_cart");
      } catch (error) {
      } finally {
      }
    }
    if (product.in_cart?.quantity === 1) {
      try {
        await CartsApi.deleteProduct(product?.in_cart?.cart_product_id);

        openSnackBar.value = true;
        snackBarMsg.value = t("item_removed_from_cart");
        loadBadge();
        loadProductListing();

        openSnackBar.value = true;
        snackBarMsg.value = t("item_removed_from_cart");
      } catch (error) {
      } finally {
      }
    }
  } else {
    openLoginDialog.value = true;
  }
}

// Increate the Product Quantity
async function increment(product: ProductDataDto) {
  try {
    if (token) {
      // if (outletInfo.value?.is_open === false) {
      //   openSnackBar.value = true;
      //   snackBarMsg.value = t("store_closed");
      //   return;
      // }

      if (product.in_cart?.quantity ?? 0 > 1) {
        await ProductApi.updateProductQuantity(
          product?.in_cart?.cart_product_id,
          "plus"
        );

        openSnackBar.value = true;
        snackBarMsg.value = t("successfully_added_to_cart");
        loadBadge();
        loadProductListing();
      } else {
        await CartsApi.addToCart(product.id, 1);
        openSnackBar.value = true;
        snackBarMsg.value = t("successfully_added_to_cart");
        loadBadge();
        loadProductListing();
        // openSnackBar.value = true;
        // snackBarMsg.value = t("please_select_quantity");
      }
    } else {
      openLoginDialog.value = true;
    }
  } catch (error) {}
}
// Increment and Decrement item

onMounted(() => {
  window.scrollTo(0, 0);
  window.addEventListener("resize", updateScreenWidth);
  window.addEventListener("resize", updateScreenHeight);

  // Get user selected order Type
  orderType.value = localStorageHelper.getOrderType();

  // Temporary Change to Delivery (for those who have selected order type pickup)
  if (orderType.value === OrderTypeEnum.pickup) {
    orderType.value = OrderTypeEnum.delivery;
    localStorageHelper.setOrderType(OrderTypeEnum.delivery);
  }

  // API
  var token: string | null = null;
  token = localStorageHelper.getUserToken();

  if (token !== null) {
    loadBadge();
    getDistance();
    loadProductListing();
    loadStoreLocatorList();
  } else {
    loadProductListing();
  }

  // Handle Scroll category and product section
  nextTick(() => {
    setTimeout(() => {
      productSections.value = Array.from(
        document.querySelectorAll(".product-section")
      );
      productListRef.value = document.querySelector(".product-list-container");

      if (productListRef.value) {
        productListRef.value.addEventListener("scroll", handleScroll);
      }
    }, 800);
  });
});

onUnmounted(() => {
  window.removeEventListener("resize", updateScreenWidth);
  window.removeEventListener("resize", updateScreenHeight);
});

// Move to Cart (Order Confirmation)
function viewCart() {
  var token: string | null = null;
  token = localStorageHelper.getUserToken();

  if (token !== null) {
    router.push(RoutersHelper.orderConfirmation);
  } else {
    openLoginDialog.value = true;
  }
}

// Move to store locator
function moveToStoreLocator() {
  var token: string | null = null;
  token = localStorageHelper.getUserToken();

  if (token !== null) {
    router.push({
      path: RoutersHelper.storeLocator,
      query: { isFromProduct: "true" },
    });
  } else {
    openLoginDialog.value = true;
  }
}

// Move to Details Page
function toDetail(id: number) {
  router.push(`${RoutersHelper.productDetail}/${id}`);
}
</script>

<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading && !productList" />

  <!-- Snack Bar -->
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>

  <div class="product-container">
    <!-- Fixed Header -->
    <AppBarPlus
      class="fixed-header"
      :title="t('delivery')"
      :title-center="false"
    >
      <template v-slot:actions>
        <BadgePlus class="pointer" :content="cartNum" @click="viewCart()">
          <VIcon :color="ColorPick.iconColor">mdi-cart-outline</VIcon>
        </BadgePlus>
      </template>
    </AppBarPlus>

    <!-- AppBarPlus Spacing -->
    <div style="height: 70px" />

    <!-- Store Information -->
    <VContainer class="store-container">
      <div style="text-align: left">
        <!-- Toogle "Pickup" & "Delivery" -->
        <div v-if="localStorageHelper.getUserToken()" class="order-type-toggle">
          <div
            class="order-type-option"
            :class="{
              // active: orderType === OrderTypeEnum.pickup,
              disabled: true,
            }"
            @click="
              () => {
                orderType = OrderTypeEnum.delivery;
                localStorageHelper.setOrderType(OrderTypeEnum.delivery);
                // orderType = OrderTypeEnum.pickup;
                // localStorageHelper.setOrderType(OrderTypeEnum.pickup);
              }
            "
          >
            {{ t("pick_up") }}
          </div>
          <div
            class="order-type-option"
            :class="{ active: orderType === OrderTypeEnum.delivery }"
            @click="
              () => {
                orderType = OrderTypeEnum.delivery;
                localStorageHelper.setOrderType(OrderTypeEnum.delivery);
              }
            "
          >
            {{ t("delivery") }}
          </div>
        </div>
        <div style="height: 5px" />

        <div class="store-info" @click="moveToStoreLocator">
          <VImg
            :src="storeIcon"
            :width="22"
            :height="22"
            style="flex: 0 0 5%"
          />
          <div style="width: 10px" />
          <span>
            {{
              outletInfo !== undefined
                ? outletInfo?.name + ", " + outletInfo?.state
                : t("select_store")
            }}</span
          >
          <div style="width: 5px" />
          <VIcon
            class="ri-arrow-right-s-line"
            size="17"
            :color="ColorPick.dividerColor"
          ></VIcon>
        </div>
      </div>
      <div style="height: 10px" />

      <div v-if="outletDistance" class="store-distance">
        {{ outletDistance?.distance ?? "0 km" }} {{ t("away") }}
      </div>
    </VContainer>
    <!-- Store Information -->

    <div
      :style="{
        height: localStorageHelper.getUserToken() === null ? '50px' : '120px',
      }"
    />

    <!-- Product category and menu section -->
    <VContainer class="product-page d-flex">
      <!-- Left side - Categories -->
      <div class="categories-container">
        <VList>
          <VListItem
            v-for="(category, index) in productList"
            :key="index"
            style="padding: 10px 10px"
            :class="{ active: activeCategory === index }"
            @click="scrollToCategory(index)"
          >
            {{ category.name }}
          </VListItem>
          <div style="height: 300px" />
        </VList>
      </div>
      <!-- Left side - Categories -->

      <VDivider :vertical="true" color="white" opacity="1" thickness="2" />

      <!-- Right side - Product Section -->
      <div class="product-list-container" ref="productListRef">
        <NoData v-if="productList?.length === 0" />
        <div
          v-else
          v-for="(category, index) in productList"
          :key="index"
          class="product-section"
        >
          <h4>{{ category.name }}</h4>
          <div style="height: 12px" />
          <div
            v-for="product in category.products"
            :key="product.id"
            class="product-item"
            @click="toDetail(product.id)"
          >
            <VImg :src="product.image || placeholder" class="product-image" />
            <div class="product-details">
              <h4 class="product-title">
                {{ product.name }}
              </h4>
              <div
                :style="{
                  height: product.short_desc !== null ? '18px' : '25px',
                }"
              />
              <p class="product-description">{{ product.short_desc }}</p>
              <div
                :style="{
                  height: product.short_desc !== null ? '18px' : '25px',
                }"
              />
              <div class="d-flex align-center">
                <p class="product-price" style="flex: 1">
                  {{ AppConfig.currencyMark }}
                  {{ ThousandsHelper.format(product.price, true) }}
                </p>
                <div
                  v-if="category?.online_order && !product.is_out_of_stock"
                  class="product-counter"
                >
                  <div
                    class="counter-icon pointer"
                    @click.stop="decrement(product)"
                  >
                    <VIcon size="14">mdi-minus</VIcon>
                  </div>
                  <span class="counter-number">{{
                    product.in_cart?.quantity ?? 0
                  }}</span>
                  <div
                    class="counter-icon pointer"
                    @click.stop="increment(product)"
                  >
                    <VIcon size="14">mdi-plus</VIcon>
                  </div>
                </div>
                <div
                  class="product-out-of-stock"
                  v-else-if="!category?.online_order && product.is_out_of_stock"
                >
                  {{ t("out_of_stock") }}
                </div>
                <div
                  class="product-out-of-stock"
                  v-else-if="category?.online_order && product.is_out_of_stock"
                >
                  {{ t("out_of_stock") }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          :style="{
            height: screenHeight >= 800 ? '770px' : '650px',
          }"
        />
        <!-- Right side - Product Section -->
      </div>
    </VContainer>
    <!-- Product category and menu section -->
  </div>

  <div style="height: 70px" />

  <!-- Dialog -->
  <DialogPlus
    persistent
    v-model="openDialog"
    :confirmText="t('back')"
    :onClickConfirm="
      () => {
        openDialog = false;
        goBack();
      }
    "
  >
    {{ dialogMsg }}
  </DialogPlus>

  <!-- Required to login -->
  <RequiredLogin v-model="openLoginDialog" :isBack="true" />
</template>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
}

.order-type-toggle {
  display: flex;
  padding: 4px;
  width: 100%;
  max-width: 600px;
  justify-items: flex-start;
  justify-content: start;
  margin: auto;
}

.order-type-option {
  padding: 5px;
  border: 1px solid #201747;
  width: 80px;
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  font-size: 11px;
  transition: all 0.3s ease;
  background-color: white;
  color: #201747;
}

.order-type-option.active {
  background-color: #201747;
  color: white;
}

.order-type-option.disabled {
  cursor: not-allowed;
  opacity: 0.5;
  // pointer-events: none;
}

.product-container {
  position: fixed;
  width: 100%;
  justify-content: center;
  align-items: center;
}

.store-container.disabled {
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.5;
}

.store-container {
  position: fixed;
  top: 50;
  left: 0;
  right: 0;
  width: 100%;
  max-width: 600px;
  padding: 10px 20px;
  background-color: white;
  z-index: 1000;
  cursor: pointer;
  text-align: start;
}

.store-info {
  display: flex;
  text-align: left;
  align-items: center;
}

.store-name {
  font-size: 13px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
  text-align: start;
}

.store-distance {
  font-size: 14px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
}

.product-page {
  display: flex;
  padding: 8px 5px;
  max-width: 600px;
  height: 100vh;
  border-top-right-radius: 30px;
  border-top-left-radius: 30px;
  background-color: v-bind("ColorPick.secondaryColor");
}

.categories-container .v-list {
  width: 80px;
  height: 100vh;
  border-top-left-radius: 20px;
  scrollbar-width: none;
  padding: 20px 0px;
  color: "#201747";
  font-size: 13px;
  background-color: v-bind("ColorPick.secondaryColor");
  text-align: center;
}

.categories-container .active {
  font-weight: 500;
  font-size: 14;
  // overflow-y: auto;
  border-right: 2px solid v-bind("ColorPick.primaryColor");
  color: v-bind("ColorPick.primaryColor");
}

.product-list-container {
  padding: 30px 10px;
  flex: 2;
  overflow-y: auto;
  scrollbar-width: thin;
  max-height: 100vh;
  width: 100%;
}

.product-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 20px;
  height: 90px;
}

.product-image {
  margin-right: 10px;
  height: 90px;
  width: 90px;
}

.product-details {
  width: 100%;
}

.product-title {
  font-size: 12px;
  font-weight: 600;
  color: v-bind("ColorPick.primaryColor");
  max-lines: 1;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.product-description {
  font-size: 9px;
  color: gray;
  max-lines: 2;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.product-price {
  font-size: 12px;
  font-weight: 600;
  color: v-bind("ColorPick.primaryColor");
  max-lines: 1;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.product-out-of-stock {
  font-size: 12px;
  font-weight: 600;
  color: red;
  max-lines: 1;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.product-counter {
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid #ccc;
  background-color: v-bind("ColorPick.primaryColor");
  border-radius: 20px;
  height: 22px;
  padding: 0px 15px;
  line-height: 1;
}

.counter-icon {
  color: #ffffff;
}

.counter-icon:hover {
  border-radius: 20px;
}

.counter-number {
  // margin-top: 3px;
  font-size: 11px;
  font-weight: 600;
  text-align: center;
  cursor: default;
  color: #ffffff;
}
</style>
