// Membership
export class MembershipDto {
  id?: number;
  name?: string;
  image?: string;
  point_from?: number;
  point_to?: number;
  min_purchase?: number;
  min_spent?: number;
  min_days?: number;
  next_level_name?: string;
  benefits?: MembershipBenefitsDto[];
  total_spent?: number; // Pass by Current Membership
  expiry_date?: Date; // Pass by Current Membership

  constructor(data: any) {
    try {
      this.benefits = data.benefits.map(
        (item: any) => new MembershipBenefitsDto(item)
      );
    } catch (_) {}
    
    this.id = data?.id;
    this.name = data?.name;
    this.image = data?.image;
    this.point_from = data?.point_from;
    this.point_to = data?.point_to;
    this.min_purchase = data?.min_purchase;
    this.min_spent = data?.min_spent;
    this.min_days = data?.min_days;
    this.next_level_name = data?.next_level_name;
    this.total_spent = data?.total_spent;
    this.expiry_date = data?.expiry_date;
  }
}


// Membership Benefits
export class MembershipBenefitsDto {
  id?: number;
  name?: string;
  icon?: string;
  icon_url?: string;
  constructor(data: any) {
    this.id = data?.id;
    this.name = data?.name;
    this.icon = data?.icon;
    this.icon_url = data?.icon_url;
  }
}
