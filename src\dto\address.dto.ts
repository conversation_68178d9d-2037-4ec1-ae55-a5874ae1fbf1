import moment from "moment";
export class AddressDto {
  id: number;
  user_id: number;
  label: string;
  recipient: string;
  phone_number: string;
  address: string;
  city: string;
  country: string;
  state: string;
  postcode: string;
  created_at: string | undefined;
  updated_at: string | undefined;
  latitude: number | undefined;
  longitude: number | undefined;

  constructor(data: any) {
    this.id = data.id;
    this.user_id = data.user_id;
    this.label = data.label ?? "";
    this.recipient = data.recipient;
    this.phone_number = data.phone_number;
    this.address = data.address;
    this.city = data.city;
    this.country = data.country;
    this.state = data.state;
    this.postcode = data.postcode;
    this.created_at = data?.created_at
      ? moment(data.created_at).format("DD MMMM YYYY")
      : undefined;
    this.updated_at = data?.updated_at
      ? moment(data.updated_at).format("DD MMMM YYYY")
      : undefined;
    try {
      this.latitude = Number(data.latitude);
    } catch (_) {}
    try {
      this.longitude = Number(data.longitude);
    } catch (_) {}
  }
}
