<script lang="ts" setup>
import { Carousel, Navigation, Pagination, Slide } from 'vue3-carousel';
import '@/../node_modules/vue3-carousel/dist/carousel.css';

interface Props {
    data: any,
    showItemAmount?: number,
    scrollItemAmount?: number,
    transitionDuration?: number,
    autoplay?: number,
    infiniteLoop?: boolean,
    breakPoints?: Record<string, any> | undefined,
    mouseDrag?: boolean,
    touchDrag?: boolean,
    pauseAutoplayOnHover?: boolean,
    navigator?: boolean,
    paginator?: boolean,
    contentPadding?: number,
    // navigator style
    navigatorColor?: string,
    navigatorBackgroundColor?: string,
    navigatorRadius?: number,
    navigatorHeight?: number,
    navigatorWidth?: number,
    // paginator style
    paginatorWidth?: number,
    paginatorHeight?: number,
    paginatorRadius?: number,
    paginatorBackgroundColor?: string,
    paginatorActiveBackgroundColor?: string,
}

const props = withDefaults(defineProps<Props>(), {
    showItemAmount: 1,
    scrollItemAmount: 1,
    transitionDuration: 300,
    autoplay: 0,
    infiniteLoop: false,
    mouseDrag: true,
    touchDrag: true,
    pauseAutoplayOnHover: false,
    navigator: true,
    paginator: true,
    contentPadding: 0,
    // navigator style
    navigatorColor: '#693915',
    navigatorBackgroundColor: 'none',
    navigatorRadius: 0,
    navigatorHeight: 36,
    navigatorWidth: 36,
    // paginator style
    paginatorWidth: 20,
    paginatorHeight: 4,
    paginatorRadius: 2,
    paginatorBackgroundColor: '#ffffffb3',
    paginatorActiveBackgroundColor: '#693915',
});
</script>

<template>
    <Carousel  :items-to-show="props.showItemAmount" :itemsToScroll="props.scrollItemAmount"
        :transition="props.transitionDuration" :autoplay="props.autoplay"
        :pause-autoplay-on-hover="props.pauseAutoplayOnHover" :mouseDrag="props.mouseDrag" :touchDrag="props.touchDrag"
        :wrap-around="props.infiniteLoop" :breakpoints="props.breakPoints">

        <Slide v-for="(item, index) in props.data" :key="index">
            <slot :item="item"></slot>
        </Slide>

        <template #addons="{ slidesCount }">
            <Navigation v-if="slidesCount > 1 && props.navigator" />
            <Pagination v-if="slidesCount > 1 && props.paginator" />
        </template>

    </Carousel>
</template>

<style lang="scss">
.carousel__slide {
    padding: v-bind("`${props.contentPadding}px`");
}

.carousel__prev,
.carousel__next {
    color: v-bind("props.navigatorColor");
    background-color: v-bind("props.navigatorBackgroundColor");
    width: v-bind("`${props.navigatorWidth}px`");
    height: v-bind("`${props.navigatorHeight}px`");
    box-sizing: content-box;
    border-radius: v-bind("`${props.navigatorRadius}px`");
}

.carousel__pagination-button {
    width: v-bind("`${props.paginatorWidth}px`");
    height: v-bind("`${props.paginatorHeight}px`");
    border-radius: v-bind("`${props.paginatorRadius}px`");
    background-color: v-bind("props.paginatorBackgroundColor");
}

.carousel__pagination-button--active,
.carousel__pagination-button:hover {
    background-color: v-bind("props.paginatorActiveBackgroundColor");
}
</style>
