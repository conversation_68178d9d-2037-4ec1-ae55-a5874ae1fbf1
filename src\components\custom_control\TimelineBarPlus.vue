<script setup lang="ts">
import ColorPick from "@/helpers/colorpick";
import { translate } from "../../plugins/i18n";
import type { OrderStatus, StatusProgressDto } from "@/dto/order.dto";
import OrderStatusCheckIcon from "@/assets/icons/order_status_check.png";
import OrderStatusUnCheckIcon from "@/assets/icons/order_status_uncheck.png";

interface Props {
  statuses?: OrderStatus[];
  color?: string;
  statusProgress?: StatusProgressDto[];
}

const props = withDefaults(defineProps<Props>(), {
  statuses: () => [
    { label: translate("ordered_cap"), completed: true },
    { label: translate("preparing_cap"), completed: false },
    { label: translate("collection_cap"), completed: false },
    { label: translate("completed_cap"), completed: false },
  ],
  color: ColorPick.primaryColor,
  statusProgress: () => [],
});

</script>

<template>
  <div class="timeline">
    <div
      v-for="(status, index) in statusProgress"
      :key="index"
      class="timeline-step"
    >
      <!-- Status + Label -->
      <div class="status-container">
        <img
          :src="status.checked ? OrderStatusCheckIcon : OrderStatusUnCheckIcon"
          class="status-icon"
        />
        <div class="timeline-label">{{ status.status }}</div>
      </div>

      <!-- Line (only if not the last step) -->
      <div v-if="index < statuses.length - 1" class="timeline-line" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.timeline {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 600px;
  padding: 20px 10px;
}

.timeline-step {
  display: flex;
  align-items: center;
}

.status-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.status-icon {
  width: 30px;
  height: 30px;
}

.timeline-label {
  font-size: 9px;
  color: #333;
  margin-top: 5px;
  text-align: center;
}

.timeline-line {
  flex-grow: 1;
  height: 2px;
  background-color: #ddd;
  width: 40px;
  margin-bottom: 20px; /* Adjust spacing */
}
</style>
