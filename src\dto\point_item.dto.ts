import { translate } from '@/plugins/i18n';

export class PointItemDto {
    id: number;
    type: string;
    type_id: number;
    points_prefix: string;
    points: number;
    status: string | undefined;
    remarks: string;
    created_at: Date;
    updated_at: Date;

    constructor(data: any) {
        switch (data.status) {
            case 'pending':
                this.status = translate('pending');
                break;
            case 'approved':
                this.status = translate('approved');
                break;
            default:
                break;
        }

        this.id = data.id;
        this.type = data.type;
        this.type_id = data.type_id;
        this.points_prefix = data.points_prefix;
        this.points = data.points;
        this.remarks = data.remarks;
        this.created_at = data.created_at;
        this.updated_at = data.updated_at;
    }
}
