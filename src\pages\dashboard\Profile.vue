<script setup lang="ts">
// Helpers
import { useDisplay } from "vuetify";
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";

import { useRouter } from "vue-router";
import { computed, onMounted, ref } from "vue";
import AppConfig from "@/appconfig";
import RoutersHelper from "@/helpers/routes_helper";
import localStorageHelper from "@/helpers/localstorage_helper";
import { VPullToRefresh } from "vuetify/labs/VPullToRefresh";

// Components
import SettingItem from "@/components/SettingItem.vue";
import RequiredLogin from "@/components/RequiredLogin.vue";

// Images
import logo from "@/assets/icons/logo.png";
import progressBarIcon from "@/assets/icons/progress_bar_icon.png";
import rightArrowIcon from "@/assets/icons/right_arrow.png";
import rewardIcon from "@/assets/icons/reward_icon.png";
import voucherIcon from "@/assets/icons/voucher_icon.png";

// Dtos
import type { UserDto } from "@/dto/user.dto";
import type { HomepageBannersDto } from "@/dto/homepage_banners.dto";

// APIs
import AuthPageApi from "@/services/auth_api";
import HomepageBannersApi from "@/services/homepage_banners_api";

// Dummy Images
import dummyMembershipCard from "@/assets/dummy/dummy_membership_card1.png";

// Language
const { t } = useI18n();
const router = useRouter();

//Handle Display
const { xs, smAndDown } = useDisplay();

// Variables
const userData = ref<UserDto>();
const bannerList = ref<HomepageBannersDto[]>([]);

const openLoginDialog = ref<boolean>(false);
const pullDownThreshold = ref(60);

// Function
// Move to My Profile
function moveToMyProfile() {
  router.push(RoutersHelper.myProfile);
}

// Move to My Wishlist
function moveToMyWishlist() {
  router.push(RoutersHelper.myWishlist);
}

// Move to Faqs
function moveToFaqs() {
  router.push(RoutersHelper.faq);
}

// Move to Settings
function moveToSettings() {
  router.push(RoutersHelper.settings);
}

// Move to Member benefits
function toMemberBenefits() {
  router.push(RoutersHelper.memberBenefits);
}

// Move to point history
function moveToPointHistory() {
  router.push(RoutersHelper.rewards);
}

// Move to My voucher
function moveToMyVoucher() {
  router.push({ path: RoutersHelper.rewards, query: { tab: "my_rewards" } });
}

// Move to Home page
function toHomePage() {
  router.push(RoutersHelper.home);
}

// onRefresh
async function refresh({ done }: { done: () => void }) {
  loadUser();
  loadBanners();

  setTimeout(() => {
    done(); // Stop the loading
  }, 500);
}

// Calaculation progress bar
function percentageSpend(current: number, total: number) {
  return (current / total) * 100;
}

onMounted(() => {
  var token: string | null = null;
  token = localStorageHelper.getUserToken();

  if (token !== null) {
    loadUser();
    loadBanners();
  } else {
    openLoginDialog.value = true;
  }
});

// Function - API
async function loadUser() {
  try {
    userData.value = await AuthPageApi.getProfile();
  } catch (_) {}
}

async function loadBanners() {
  try {
    bannerList.value = await HomepageBannersApi.profileList();
  } catch (_) {}
}
</script>

<template>
  <div class="profile">
    <div>
      <v-pull-to-refresh
        :pull-down-threshold="pullDownThreshold"
        @load="refresh"
      >
        <div class="profile-container">
          <div v-if="localStorageHelper.getUserToken()" style="height: 15px" />

          <div
            v-if="localStorageHelper.getUserToken()"
            class="profile-user-info"
            @click="moveToMyProfile"
          >
            <VImg
              :src="userData?.profile_image || logo"
              :width="60"
              :height="60"
              rounded="circle"
              :cover="true"
            />
            <div class="profile-user-info-content">
              <div class="profile-user-info-text">
                {{ userData?.name }}
              </div>

              <div style="height: 5px" />

              <div class="profile-user-info-text">
                {{ userData?.phone_number }}
              </div>
            </div>
          </div>

          <div v-if="localStorageHelper.getUserToken()" style="height: 10px" />

          <!-- MemberShip Card -->
          <div v-if="localStorageHelper.getUserToken()" class="membership-card">
            <VImg
              class="membership-card-image"
              :src="userData?.current_membership?.membership_tier_image ?? ''"
              :cover="true"
            >
              <div class="membership-card-overlay">
                <div class="membership-card-top">
                  <div style="height: 5px" />
                  <div class="membership-card-overlay-title">
                    {{
                      userData?.current_membership?.membership_name ??
                      t("unknown")
                    }}
                  </div>
                  <div class="membership-card-overlay-subtitle">
                    {{
                      t("earn_to_upgrade_to")
                        .replace(
                          "[level]",
                          userData?.next_membership?.name ?? t("unknown")
                        )
                        .replace(
                          "[points]",
                          userData?.current_membership?.point_to?.toString() ??
                            "0"
                        )
                    }}
                  </div>

                  <div
                    v-if="localStorageHelper.getUserToken()"
                    style="height: 25px"
                  />

                  <!-- Progress Bar -->
                  <div class="progress-container">
                    <div
                      class="progress-bar"
                      :style="{
                        background: `linear-gradient(to right, ${
                          ColorPick.primaryColor
                        } ${percentageSpend(
                          userData?.current_membership?.total_spent ?? 0,
                          userData?.current_membership?.point_to ?? 0
                        )}%, ${
                          ColorPick.memberProgressBarInActive
                        } ${percentageSpend(
                          userData?.current_membership?.total_spent ?? 0,
                          userData?.current_membership?.point_to ?? 0
                        )}%)`,
                      }"
                    >
                      <div
                        :style="{
                          width:
                            percentageSpend(
                              userData?.current_membership?.total_spent ?? 0,
                              userData?.current_membership?.point_to ?? 0
                            ) + '%',
                          backgroundColor: ColorPick.primaryColor,
                        }"
                      >
                        <div
                          class="progress-icon"
                          :style="{
                            left: `calc(${percentageSpend(
                              userData?.current_membership?.total_spent ?? 0,
                              userData?.current_membership?.point_to ?? 0
                            )}%)`,
                          }"
                        >
                          <VImg
                            :src="progressBarIcon"
                            height="25"
                            width="25"
                          ></VImg>
                        </div>
                      </div>
                    </div>
                    <!-- Progress Bar -->

                    <div style="height: 5px" />

                    <!-- Earnings (Current/Total) -->
                    <div
                      class="membership-card-overlay-subtitle"
                      style="display: flex; justify-content: flex-end"
                    >
                      {{ AppConfig.currencyMark
                      }}{{ userData?.current_membership?.total_spent ?? 0 }} /
                      {{ AppConfig.currencyMark
                      }}{{ userData?.current_membership?.point_to ?? 0 }}
                    </div>
                    <!-- Earnings (Current/Total) -->
                  </div>
                </div>

                <!-- Membership Card Bottom -->
                <div class="membership-card-bottom">
                  <div
                    class="membership-card-content"
                    @click="toMemberBenefits()"
                  >
                    <div class="membership-card-bottom-text">
                      {{ t("view_my_member_benefits") }}
                    </div>
                    <VImg :src="rightArrowIcon" :width="13" :height="13"></VImg>
                  </div>
                </div>
                <!-- Membership Card Bottom -->
              </div>
            </VImg>
          </div>

          <div v-if="localStorageHelper.getUserToken()" style="height: 30px" />

          <!-- My Account -->
          <div
            v-if="localStorageHelper.getUserToken()"
            class="my-account-title"
          >
            {{ t("my_account_cap") }}
          </div>

          <div v-if="localStorageHelper.getUserToken()" style="height: 15px" />

          <div
            v-if="localStorageHelper.getUserToken()"
            class="my-account-section-container"
          >
            <div class="my-account-section">
              <!-- Points Section -->
              <div class="account-item" @click="moveToPointHistory()">
                <VImg :src="rewardIcon" class="account-icon" />
                <div class="account-value">
                  {{ userData?.point_balance ?? 0 }}
                </div>
                <div class="account-label">{{ t("points") }}</div>
              </div>

              <!-- Divider -->
              <div class="account-divider"></div>

              <!-- Vouchers Section -->
              <div class="account-item" @click="moveToMyVoucher()">
                <VImg :src="voucherIcon" class="account-icon" />
                <div class="account-value">
                  {{ userData?.unused_vouchers ?? 0 }}
                </div>
                <div class="account-label">{{ t("vouchers") }}</div>
              </div>
            </div>
          </div>
          <!-- My Account -->

          <div v-if="localStorageHelper.getUserToken()" style="height: 20px" />

          <!-- Banner -->
          <div
            v-if="bannerList.length > 0"
            class="profile-account-image-container"
          >
            <VCarousel
              cycle
              show-arrows="hover"
              height="180px"
              interval="5000"
              hide-delimiter-background
              delimiterIcon="none"
              hide-delimiters
            >
              <VCarouselItem
                v-for="(slide, i) in bannerList"
                :key="i"
                :src="slide.image"
                :alt="slide.title"
                cover
              >
              </VCarouselItem>
            </VCarousel>
          </div>
          <!-- Banner -->

          <div style="height: 15px" />

          <!-- General Content -->
          <SettingItem
            @click="() => moveToMyProfile()"
            :title="t('edit_profile')"
          />

          <SettingItem
            @click="() => moveToMyWishlist()"
            :title="t('my_wishlist')"
          />

          <SettingItem @click="() => moveToFaqs()" :title="t('faqs')" />

          <SettingItem
            @click="() => moveToSettings()"
            :title="t('settings')"
            :isLast="'true'"
          />

          <div style="height: 30px" />
        </div>
        <div style="height: 100px"
      /></v-pull-to-refresh>
    </div>
  </div>

  <!-- Required login dialog -->
  <RequiredLogin v-model="openLoginDialog" :isBack="false" />
</template>

<style lang="scss" scoped>
.profile {
  height: 100vh;
  width: 100%;
  text-align: -webkit-center;
}

.profile-container {
  max-width: 600px;
  padding: 20px 20px;
}

.profile-user-info {
  cursor: pointer;
  display: flex;
  -webkit-box-align: center;
  padding: 10px 15px;
  width: 100%;
  align-items: anchor-center;
}

.profile-user-info-content {
  padding-inline: 10px;
  width: calc(100% - 50px);
}

.profile-user-info-text {
  width: 100%;
  text-align: left;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  max-lines: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  font-size: 16px;
  font-weight: 600;
  color: v-bind("ColorPick.primaryColor");
}

// Membership Card
.membership-card {
  width: 100%;
  height: 160px;
  text-align: start;
}

.membership-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: inherit;
}

.membership-card-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.membership-card-top {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 10px 20px 0px 20px;
}

.membership-card-overlay-title {
  font-size: 19px;
  font-weight: 600;
  padding: 0px 10px;

  color: white;
}

.membership-card-overlay-subtitle {
  font-size: 11px;
  font-weight: 400;
  padding: 0px 0px 0px 10px;
  color: white;
}

.progress-container {
  width: 100%;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 5px;
  background: v-bind("ColorPick.memberProgressBarInActive");
  border-radius: 2px;
}

.progress-icon {
  position: absolute;
  left: 10;
  top: -10px;
  font-size: 14px;
  transform: translateX(-50%);
}

.membership-card-bottom {
  width: 100%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 7px 20px;
  opacity: 0.7;
}

.membership-card-content {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

.membership-card-bottom-text {
  font-size: 11px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
  align-items: center;
}
// Membership Card

// My Account
.my-account-title {
  display: flex;
  font-size: 20px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
  text-align: start;
}

.my-account-section-container {
  display: flex;
  flex-direction: column;
  padding: 20px 20px;
  gap: 10px;
  background-color: v-bind("ColorPick.secondaryColor");
}

.my-account-section {
  display: flex;
  justify-content: space-around;
  align-items: center;
  width: 100%;
}

.account-item {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 5px;
  flex: 1;
  min-width: 0;
}

.account-icon {
  width: 40px;
  height: 40px;
}

.account-value {
  font-size: 20px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
}

.account-label {
  font-size: 11px;
  font-weight: 400;
  color: v-bind("ColorPick.primaryColor");
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.account-divider {
  width: 1.5px;
  margin-top: 50px;
  height: 40px;
  background-color: rgb(212, 209, 209);
}
// My Account

.settings-content-title {
  font-size: 15px;
  font-weight: 500;
  color: v-bind("ColorPick.settingsTitleFontColor");
  text-align: left;
}

.profile-account-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 25px;
}
</style>
