interface TransformedPaymentMethodData {
  id: string;
  label: string;
}
export class PaymentMethod {
  static transformedPaymentMethodData(
    input: Record<string, string>
  ): TransformedPaymentMethodData[] {
    return Object.entries(input).map(([id, label]) => {
      return {
        id,
        label,
      };
    });
  }
}

export class PaymentMethodDto {
  id: string;
  label: string;

  constructor(data: any) {
    this.id = data.id;
    this.label = data.label;
  }
}
