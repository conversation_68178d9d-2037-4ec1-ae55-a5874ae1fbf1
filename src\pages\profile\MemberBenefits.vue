<script setup lang="ts">
// Images
import membershipCardPlaceholder from "@/assets/images/membership_card_placeholder.png";
import appLogo from "@/assets/icons/logo.png";
import progressBarIcon from "@/assets/icons/progress_bar_icon.png";

// Components
import NoData from "@/components/custom_control/NoData.vue";

// Helpers
import { onMounted, onUnmounted, ref } from "vue";
import { useDisplay } from "vuetify";
import { VPullToRefresh } from "vuetify/labs/VPullToRefresh";
import AppConfig from "@/appconfig";

import ColorPick from "@/helpers/colorpick";
import "vue3-carousel/carousel.css";
import { Carousel, Slide, } from "vue3-carousel";

// Routes
import { useRouter } from "vue-router";

// Language
import { useI18n } from "vue-i18n";
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";
import type { MembershipDto } from "@/dto/membership.dto";
import MembershipTiersApi from "@/services/membership_tiers_api";
import LoadingPlus from "@/components/custom_control/LoadingPlus.vue";
import SnackBarPlus from "@/components/custom_control/SnackBarPlus.vue";

const { t } = useI18n();
const router = useRouter();
//Handle Display
const { xs } = useDisplay();

//Data

// Data List
const memberCardList = ref<MembershipDto[]>([]);

const isLoading = ref<boolean>(false);
const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

const carouselConfig = ref({
  itemsToShow: getItemsToShow(),
  wrapAround: true, // Infinite scrolling enabled
});

const activeSlide = ref(0);
const pullDownThreshold = ref(60);

// Api
async function loadMemberTierList() {
  //If Loading will no do anything
  if (isLoading.value) {
    snackBarMsg.value = t("please_wait...");
    openSnackBar.value = true;
    return;
  }

  try {
    isLoading.value = true;

    memberCardList.value = await MembershipTiersApi.list();

  } catch (error) {
    snackBarMsg.value = `${error}`;
    openSnackBar.value = true;

    goBack();
  } finally {
    isLoading.value = false;
  }

}


// Function
function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}


async function refresh({ done }: { done: () => void }) {
  try {
    await loadMemberTierList();
  } catch (_) {

  } finally {
    done();
  }

}

// Handle the banner size
function getItemsToShow() {
  const gap = 15; // Fixed gap of 20px between items
  const itemWidth = 310; // Width of each item
  const availableWidth = window.innerWidth >= 600 ? 400 : window.innerWidth;

  return (availableWidth + gap) / (itemWidth + gap);
}

// Handle the banner size
function handleResize() {
  carouselConfig.value.itemsToShow = getItemsToShow();
}

// Calaculation progress bar
function percentageSpend(current: number, total: number) {
  return (current / total) * 100;
}

onMounted(() => {
  window.addEventListener("resize", handleResize);

  // Load Member Tier Data
  loadMemberTierList();

  // Set Active Slide as Current Member
  if (memberCardList.value && memberCardList.value.length > 0) {
    const tempCurrentMemberIndex = memberCardList.value.findIndex((item) => {
      return item.total_spent !== undefined && item.total_spent !== null;
    });

    if (tempCurrentMemberIndex >= 0) {
      activeSlide.value = tempCurrentMemberIndex;
    }
  }
  // Load Member Tier Data

});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading && memberCardList.length <= 0" />
  <!-- Snack Bar -->
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>

  <div class="member-benefits">
    <AppBarPlus :title="t('member_benefits')" :back="true" />
    <!-- AppBarPlus Spacing -->
    <div style="height: 70px" />
    <v-pull-to-refresh v-if="memberCardList.length > 0" :pull-down-threshold="pullDownThreshold" @load="refresh">
      <div class="member-benefits-carousel-container" :style="{
        padding: xs ? '0' : '15px',
      }">
        <Carousel v-model="activeSlide" v-bind="carouselConfig">
          <Slide v-for="(slide, index) in memberCardList" :key="index">
            <div class="carousel__item" :style="{
              transform: index === activeSlide ? 'scale(1)' : 'scale(0.9)',
              opacity: index === activeSlide ? 1 : 0.8,
            }">
              <!-- MemberShip Card -->
              <div class="membership-card">
                <VImg class="membership-card-image" :src="slide.image ?? membershipCardPlaceholder" :cover="true">
                  <div class="membership-card-overlay">
                    <div class="membership-card-top">
                      <div style="height: 5px" />
                      <div class="membership-card-overlay-title">
                        {{ slide.name ?? t('app_name') }}
                      </div>
                      <div :style="{ height: '5px' }"></div>
                      <div v-if="slide?.next_level_name" class="membership-card-overlay-subtitle">
                        {{
                          t("earn_to_upgrade_to")
                            .replace(
                              "[level]",
                              slide?.next_level_name ?? t("unknown")
                            )
                            .replace(
                              "[points]",
                              slide?.point_to?.toString() ??
                              t("unknown")
                            )
                        }}
                      </div>

                      <div style="height: 25px" />

                      <!-- Progress Bar -->
                      <div class="progress-container">
                        <div
                          v-if="slide.total_spent !== undefined && slide.point_to !== undefined && slide.total_spent !== null && slide.point_to !== null"
                          class="progress-bar" :style="{
                            background: `linear-gradient(to right, ${ColorPick.primaryColor} ${percentageSpend(slide.total_spent ?? 0, slide.point_to ?? 0)}%, ${ColorPick.memberProgressBarInActive} ${percentageSpend(slide.total_spent ?? 0, slide.point_to ?? 0)}%)`,
                          }">
                          <div :style="{
                            width: percentageSpend(slide.total_spent ?? 0, slide.point_to ?? 0) + '%',
                            backgroundColor: ColorPick.primaryColor,
                          }">
                            <div class="progress-icon"
                              :style="{ left: `calc(${percentageSpend(slide.total_spent ?? 0, slide.point_to ?? 0)}%)` }">
                              <VImg :src="progressBarIcon" height="25" width="25"></VImg>
                            </div>
                          </div>
                        </div>
                        <!-- Progress Bar -->

                        <div
                          v-if="slide.total_spent !== undefined && slide.point_to !== undefined && slide.total_spent !== null && slide.point_to !== null"
                          style="height: 5px" />

                        <!-- Earnings (Current/Total) -->
                        <div
                          v-if="slide.total_spent !== undefined && slide.point_to !== undefined && slide.total_spent !== null && slide.point_to !== null"
                          class="membership-card-overlay-subtitle" style="display: flex; justify-content: flex-end">
                          {{ AppConfig.currencyMark }}{{ slide.total_spent ?? 0 }} /
                          {{ AppConfig.currencyMark }}{{ slide.point_to }}
                        </div>
                        <!-- Earnings (Current/Total) -->
                      </div>
                    </div>

                    <!-- Membership Card Bottom -->
                    <div class="membership-card-bottom"></div>
                    <!-- Membership Card Bottom -->
                  </div>
                </VImg>
              </div>
            </div>
          </Slide>
        </Carousel>
      </div>
      <div v-if="memberCardList[activeSlide].benefits && memberCardList[activeSlide]!.benefits!.length > 0"
        style="height: 10px" />
      <div v-if="memberCardList[activeSlide].benefits && memberCardList[activeSlide]!.benefits!.length > 0"
        class="member-benefits-container">
        <div class="member-benefits-title">{{ t("member_benefits") }}</div>
        <div style="height: 20px" />
        <div v-if="memberCardList[activeSlide].benefits && memberCardList[activeSlide]!.benefits!.length > 0"
          v-for="(item, index) in memberCardList[activeSlide].benefits">
          <div class="voucher-item">
            <VImg :src="item.icon_url ?? appLogo" height="25" width="25" class="voucher-icon"></VImg>

            <div style="width: 5px" />

            <div class="voucher-text">{{ item.name }}</div>
          </div>
          <div style="height: 20px" />
        </div>

      </div>

      <div style="height: 100px" />
    </v-pull-to-refresh>
    <NoData v-else-if="!isLoading" />
  </div>
</template>

<style lang="scss" scoped>
.member-benefits {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  /* Center horizontally */
  // background-color: #e3ddd6;
  text-align: -webkit-center;
}

.member-benefits-carousel-container {
  width: 100%;
  max-width: 600px;
  justify-content: center;
  align-items: center;
  text-align: left;
  position: sticky;
}

// Carousel Slider CSS
.carousel__item {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
  padding: 10px 0px;
  flex: 0 0 auto;
  width: 100%;
}

/* Enlarge the active slide */
.carousel__item.active {
  transform: scale(1);
  opacity: 1;
  border-radius: 8px;
}

// Carousel Slider CSS

// Membership Card
.membership-card {
  width: 100%;
  height: 150px;
}

.membership-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.membership-card-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.membership-card-top {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 10px 15px 0px 15px;
}

.membership-card-overlay-title {
  padding: 0px 10px;
  font-size: 19px;
  font-weight: 600;
  color: white;
}

.membership-card-overlay-subtitle {
  font-size: 11px;
  padding: 0px 0px 0px 10px;
  font-weight: 400;
  color: white;
}

.progress-container {
  width: 100%;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 5px;
  background: v-bind("ColorPick.memberProgressBarInActive");
  border-radius: 2px;
}

.progress-icon {
  position: absolute;
  left: 10;
  top: -10px;
  font-size: 14px;
  transform: translateX(-50%);
}

.membership-card-bottom {
  width: 100%;
  height: 30px;
  background-color: #ffffff;
  display: flex;
  padding: 5px;
  opacity: 0.7;
}

// Membership Card

.member-benefits-container {
  padding: 20px;
}

.member-benefits-title {
  display: flex;
  font-size: 23px;
  font-weight: 600;
  color: #000000;
}

.voucher-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  max-width: 600px;
}

.voucher-icon {
  width: 20px;
  align-items: center;
  justify-content: center;
}

.voucher-text {
  width: 100%;
  flex-grow: 1;
  text-align: left;
  font-size: 15px;
  font-weight: 600;
  color: v-bind("ColorPick.primaryColor");
}
</style>
