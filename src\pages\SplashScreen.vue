<script setup lang="ts">
import RoutersHelper from "@/helpers/routes_helper";
import splashScreen from "@/assets/images/splash_screen.png";
import { useDisplay } from "vuetify";
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";
import ButtonPlus from "@/components/custom_control/ButtonPlus.vue";
import splashScreenBanner from "@/assets/images/splash_screen_banner.png";

//Handle Display
const { xs } = useDisplay();
const { t } = useI18n();
</script>

<template>
  <div class="splash-screen">
    <div>
      <div class="splash-screen-image-container">
        <VImg
          class="splash-screen-image"
          :src="splashScreenBanner"
          :cover="true"
        >
        </VImg>
      </div>
      <div :style="{ 'max-width': '600px', margin: 'auto' }">
        <div style="height: 5px"></div>
        <h1 class="splash-screen-title">
          {{ t("splash_screen_title") }}
        </h1>
        <div style="height: 10px"></div>
        <div class="splash-screen-description">
          {{ t("splash_screen_description") }}
        </div>
        <div style="height: 10px"></div>
        <div class="splash-screen-description">
          {{ t("splash_screen_description2") }}
        </div>
        <div style="height: 10px"></div>
        <div class="splash-screen-description">
          {{ t("splash_screen_description3") }}
        </div>
        <div style="height: 10px"></div>
        <div class="splash-screen-description">
          {{ t("splash_screen_description4") }}
        </div>
        <div style="height: 10px"></div>
        <div class="splash-screen-description">
          {{ t("splash_screen_description5") }}
        </div>
        <div style="height: 10px"></div>
        <div class="splash-screen-description">
          {{ t("splash_screen_description6") }}
        </div>
        <div style="height: 10px"></div>
        <div class="splash-screen-description">
          {{ t("splash_screen_description7") }}
        </div>
        <div style="height: 50px"></div>
        <div class="splash-screen-description">
          {{ t("splash_screen_description8") }}
        </div>
        <div style="height: 100px"></div>
      </div>
      <div class="splash-screen-get-started-container">
        <ButtonPlus class="splash-screen-get-started" :to="RoutersHelper.home">
          {{ t("get_started") }}
        </ButtonPlus>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.splash-screen {
  background-color: v-bind("ColorPick.secondaryColor");
  height: 100vh;
  width: 100%;
  display: inline-table;
}

.splash-screen-image-container {
  padding: v-bind('xs ? "0" : "15px"');
  display: flex;
  justify-content: center;
  align-items: center;
}

.splash-screen-image {
  max-width: v-bind('xs ? null : "450px"');
  max-height: 450px;
  border-radius: v-bind('xs ? "0 0 25px 25px" : "25px"');
}

.splash-screen-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  padding: 5px 20px;
}

.splash-screen-description {
  text-align: center;
  font-size: 15px;
  font-weight: 500;
  padding: 0px 20px;
}

.splash-screen-get-started-container {
  background-color: v-bind("ColorPick.secondaryColor");
  padding: 20px;
  position: fixed;
  bottom: 0px;
  width: 100%;
  text-align: -webkit-center;
}
.splash-screen-get-started {
  width: 100%;
  max-width: 600px;
  justify-content: center;
  justify-self: center;
}
</style>
