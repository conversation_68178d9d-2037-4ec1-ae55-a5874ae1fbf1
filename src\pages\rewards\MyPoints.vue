<script setup lang="ts">
import { useDisplay } from 'vuetify';
import { onMounted, ref, watch } from 'vue';
import ColorPick from '@/helpers/colorpick';
import { useI18n } from 'vue-i18n';
import { useRouter, useRoute } from 'vue-router';
import ThousandsHelper from '@/helpers/thousands_helper';
import pointCard from '@/assets/icons/point_card.png'
import moment from 'moment';
import MyPointsItem from '@/components/MyPointsItem.vue';
import type { UserDto } from '@/dto/user.dto';
import AuthPageApi from '@/services/auth_api';
import PointTypeEnum from '@/enums/PointTypeEnum';
import SnackBarPlus from '@/components/custom_control/SnackBarPlus.vue';
import type { PointDto } from '@/dto/point.dto';
import PointsApi from '@/services/points_api';
import LoadingPlusItem from '@/components/custom_control/LoadingPlusItem.vue';
import NoData from '@/components/custom_control/NoData.vue';

//Variables
const isLoading = ref<boolean>(false);

// Route
const route = useRoute();
const router = useRouter();

//Handle Display
const { xs } = useDisplay();

// Language
const { t } = useI18n();

//Data
const userData = ref<UserDto>();
const page = ref<number>(+(route.query?.page ?? 1));
const last_page = ref<number>();
const listData = ref<PointDto[]>([]);

const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>('');

// tabs
const activeTab = ref<string>(route.query.tab?.toString() ?? PointTypeEnum.all);
const tabs = [
    { title: t('all_history'), tab: PointTypeEnum.all },
    { title: t('points_earned'), tab: PointTypeEnum.earned },
    { title: t('points_spent'), tab: PointTypeEnum.spent },
]


onMounted(() => {
    router.replace({ query: { tab: activeTab.value, page: page.value } });
    loadUser();
    loadList();
});

watch(activeTab, (newValue) => {
    page.value = 1;
    router.replace({ query: { tab: activeTab.value, page: page.value } });
    loadList();
});

watch(page, (value) => {
    var tempPage: number = isNaN(value) ? 1 : value;

    router.replace({ query: { tab: activeTab.value, page: tempPage } });
    loadList();
});

watch(route, (value) => {
    page.value = isNaN(+(value.query?.page ?? 1)) ? 1 : +(value.query?.page ?? 1);
    activeTab.value = route.query.tab?.toString() ?? PointTypeEnum.all;
    loadList();
})

//Function
function goBack() {
    if (window.history.state.back === null) {
        router.push('/');
    } else {
        router.back();
    }
}

//Call Api
async function loadUser() {
    try {
        userData.value = await AuthPageApi.getProfile();
    } catch (_) {
    }
}

async function loadList() {
    try {
        isLoading.value = true;

        listData.value = [];

        var tempData = await PointsApi.list(page.value, undefined, activeTab.value ?? PointTypeEnum.all,);

        last_page.value = tempData.last_page ?? undefined;

        listData.value = tempData.data ?? [];

    } catch (error) {

        snackBarMsg.value = `${error}`;
        openSnackBar.value = true;
    } finally {
        isLoading.value = false;
    }
}

</script>

<template>

    <SnackBarPlus v-model="openSnackBar">
        {{ snackBarMsg }}
    </SnackBarPlus>
    <div class="my-points">
        <div class="my-points-container">
            <div class="my-points-banner-container">
                <div class="my-points-banner">
                    <VBtn class="my-points-banner-back-button" :color="ColorPick.fontColor" @click="goBack"
                        variant="text" :style="{ color: ColorPick.fontColorWhite }">
                        <VIcon class="ri-arrow-left-s-line"></VIcon>
                    </VBtn>
                    <div class="my-points-banner-content">
                        <div>
                            <div class="my-points-banner-title">
                                {{ t('my_points') }}
                            </div>
                            <div style="height: 25px;">
                            </div>
                            <div v-if="userData?.point_balance != undefined" :style="{
                                display: 'inline-flex',
                                alignItems: 'anchor-center',
                            }">
                                <VImg :src="pointCard" :width="33" :height="33" />
                                <span class="my-points-banner-text">
                                    {{ ThousandsHelper.format(userData?.point_balance ?? 0) }}
                                    <span style="font-size: 16px;">{{ t('points') }}</span>
                                </span>
                            </div>
                            <div
                                v-if="userData?.expiring_points?.expiry_date && userData?.expiring_points?.points">
                                <span class="my-points-banner-remark">
                                    <VIcon>mdi-clock</VIcon>
                                    {{
                                        t('points_will_expire_by').replace('[point]',
                                            ThousandsHelper.format(userData?.expiring_points?.points?.toString()
                                                ?? '0') ?? '0').replace('[date]',
                                                    moment(userData?.expiring_points?.expiry_date).format('D MMM YYYY')) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <VTabs v-model="activeTab" fixed-tabs grow :style="{
                    maxWidth: '600px',
                }">
                    <VTab v-for="item in tabs" :value="item.tab">
                        {{ item.title }}
                    </VTab>

                </VTabs>
                <VWindow v-model="activeTab">
                    <VWindowItem :value="PointTypeEnum.all">
                        <div v-if="isLoading" :style="{ padding: '10px' }">
                            <LoadingPlusItem></LoadingPlusItem>
                        </div>
                        <div v-else class="my-points-tab-container">
                            <div v-if="listData.length > 0">
                                <div v-for="item in listData">
                                    <div :style="{ height: '10px' }"></div>
                                    <div class="my-points-tab-month">
                                        {{ item.month }}
                                    </div>
                                    <div v-for="itemData in item.data">
                                        <MyPointsItem :title="itemData.type" :description="itemData.remarks"
                                            :date="itemData.created_at" :prefix="itemData.points_prefix"
                                            :points="itemData.points">
                                        </MyPointsItem>
                                    </div>
                                </div>
                            </div>
                            <NoData v-else></NoData>
                        </div>
                    </VWindowItem>
                    <VWindowItem :value="PointTypeEnum.earned">
                        <div v-if="isLoading" :style="{ padding: '10px' }">
                            <LoadingPlusItem></LoadingPlusItem>
                        </div>
                        <div v-else class="my-points-tab-container">
                            <div v-if="listData.length > 0">
                                <div v-for="item in listData">
                                    <div :style="{ height: '10px' }"></div>
                                    <div class="my-points-tab-month">
                                        {{ item.month }}
                                    </div>
                                    <div v-for="itemData in item.data">
                                        <MyPointsItem :title="itemData.type" :description="itemData.remarks"
                                            :date="itemData.created_at" :prefix="itemData.points_prefix"
                                            :points="itemData.points">
                                        </MyPointsItem>
                                    </div>
                                </div>
                            </div>
                            <NoData v-else></NoData>
                        </div>
                    </VWindowItem>
                    <VWindowItem :value="PointTypeEnum.spent">
                        <div v-if="isLoading" :style="{ padding: '10px' }">
                            <LoadingPlusItem></LoadingPlusItem>
                        </div>
                        <div v-else class="my-points-tab-container">
                            <div v-if="listData.length > 0">
                                <div v-for="item in listData">
                                    <div :style="{ height: '10px' }"></div>
                                    <div class="my-points-tab-month">
                                        {{ item.month }}
                                    </div>
                                    <div v-for="itemData in item.data">
                                        <MyPointsItem :title="itemData.type" :description="itemData.remarks"
                                            :date="itemData.created_at" :prefix="itemData.points_prefix"
                                            :points="itemData.points">
                                        </MyPointsItem>
                                    </div>
                                </div>
                            </div>
                            <NoData v-else></NoData>
                        </div>
                    </VWindowItem>
                </VWindow>
                <div style="height: 70px"></div>

                <div class="pagination">
                    <VPagination v-model="page" :length="last_page ?? 1" rounded="circle" />
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.my-points {
    width: 100%;
    display: inline-table;
    text-align: -webkit-center;
}

.my-points-container {
    height: 100%;
    max-width: 600px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.my-points-banner-container {
    padding: v-bind('xs ? "0" : "15px"');
}

.my-points-banner {
    max-width: 600px;
    width: 100%;
    height: 180px;
    background-color: v-bind('ColorPick.primaryColor');
    border-radius: v-bind('xs ? "0" : "25px"');
    position: relative;
}

.my-points-banner-content {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px;
}

.my-points-banner-title {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    padding: 5px;
    color: v-bind('ColorPick.fontColorWhite');
    max-lines: 1;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}

.my-points-banner-text {
    display: -webkit-box;
    font-size: 22px;
    font-weight: 600;
    text-align: center;
    padding: 5px;
    color: v-bind('ColorPick.fontColorWhite');
    max-lines: 1;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}


.my-points-banner-back-button {
    position: absolute;
    top: 10px;
    left: 10px;
}

.my-points-banner-remark {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    color: v-bind('ColorPick.fontColorWhite');
    max-lines: 1;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
}

.my-points-tab-container {
    max-width: 600px;
    width: 100%;
}
</style>

<style lang="scss">
.v-tabs {
    overflow-x: auto !important;
    white-space: nowrap;
}

.v-tab {
    text-transform: none !important;
}

@media (max-width: 385px) {
    .v-tab {
        font-size: 12px;
        padding: 8px 12px;
    }
}

.pagination {
    position: fixed;
    width: 100%;
    max-width: 600px;
    bottom: 0;
    background-color: v-bind('ColorPick.backgroundColor');
    padding: 10px 0;

}

.my-points-tab-month {
    font-size: 18px;
    font-weight: 600;
    text-align: left;
    padding: 5px;
    color: v-bind('ColorPick.greyFontColor07');
}
</style>