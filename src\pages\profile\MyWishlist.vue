<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import type { PaginationDto } from "@/dto/pagination.dto";
import ColorPick from "@/helpers/colorpick";
import RoutersHelper from "@/helpers/routes_helper";
import { ProductDataDto } from "@/dto/product.dto";
import ProductApi from "@/services/product_api";
import NoData from "@/components/custom_control/NoData.vue";
import AppConfig from "@/appconfig";
import appLogo from "@/assets/icons/logo.png";
import { VPullToRefresh } from "vuetify/labs/VPullToRefresh";

import "@/interfaces/removehtmltag";
import ThousandsHelper from "@/helpers/thousands_helper";

const { t } = useI18n();

const router = useRouter();

const isLoading = ref<boolean>(false);
const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>("");
const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

const page = ref<number>(1);
const hasMore = ref(true);
let doneCopy: (status: "error" | "loading" | "empty" | "ok") => void;
const pullDownThreshold = ref(60);
//data
const wishlistList = ref<ProductDataDto[]>([]);

// Function
function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

async function loadWishlistListing(): Promise<PaginationDto | undefined> {
  try {
    const response = await ProductApi.wishList(page.value);
    return response;
  } catch (_) {
  }

  return undefined;
}

// Wish List - Infinite Scroll
async function infiniteload({
  done,
}: {
  done: (status: "error" | "loading" | "empty" | "ok") => void;
}) {
  if (!hasMore.value) {
    done("empty");
    return;
  }

  try {
    const res = await loadWishlistListing();

    if (res?.data !== undefined) {

      if (page.value <= 1) {
        wishlistList.value = [];
      }

      res?.data.forEach((item: any) => {
        if (item.product) {
          try {
            wishlistList.value.push(new ProductDataDto(item.product));

          } catch (_) {
          }
        }
      });

      const lastPage = res.last_page ?? 1;

      if (page.value >= lastPage) {
        hasMore.value = false;

        // For refresh the page
        doneCopy = done;
      } else {
        hasMore.value = true;

        page.value += 1;
      }
    } else {
      hasMore.value = false;
      done("empty");
      doneCopy = done;
    }
  } catch (e) {
  } finally {
    done("ok");
  }
}

// On Refresh
async function refresh({ done }: { done: () => void }) {
  try {
    page.value = 1;
    hasMore.value = true;

    doneCopy('ok');
    await infiniteload({ done: doneCopy });
  } catch (_) { } finally {
    done();
  }
}

onMounted(() => {
  window.scrollTo(0, 0);
});

// Move to Product Details
function toDetail(id: number) {
  router.push(`${RoutersHelper.productDetail}/${id}`);
}


</script>
<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading" />

  <!-- Snack Bar -->
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>

  <!-- Dialog -->
  <DialogPlus persistent v-model="openDialog" :confirmText="t('back')" :onClickConfirm="() => {
    openDialog = false;
    goBack();
  }
    ">
    {{ dialogMsg }}
  </DialogPlus>

  <AppBarPlus :title="t('my_wishlist')" :back="true" />

  <!-- AppBarPlus Spacing -->
  <div style="height: 70px" />

  <div class="wishlist">
    <div class="wishlist-container">
      <VSpacer />
      <VPullToRefresh @load="refresh" :pullDownThreshold="pullDownThreshold">
        <div class="product-list-container">
          <VInfiniteScroll :height="'100%'" :items="wishlistList" @load="infiniteload" v-model="hasMore">
            <template v-slot:empty>
              <NoData v-if="wishlistList?.length <= 0" />
              <span v-else>{{ t("no_more") }}</span>
            </template>
            <div v-for="product in wishlistList" :key="product.id" class="product-item" @click="toDetail(product.id)">
              <VImg :src="product.image ?? appLogo" class="product-image" :cover="true" rounded="lg" />
              <div class="product-details">
                <h4 class="product-title">{{ product.name }}</h4>
                <div style="height: 18px" />
                <p class="product-description">{{ `${product.desc ?? ''}`.removeHtmlTags() }}</p>
                <div style="height: 18px" />
                <div class="d-flex align-center">
                  <p class="product-price" style="flex: 1">
                    {{ AppConfig.currencyMark }} {{ ThousandsHelper.format(product.price, true,) }}
                  </p>
                </div>
              </div>
            </div>
          </VInfiniteScroll>
        </div>
      </VPullToRefresh>

    </div>
  </div>
</template>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
}

.wishlist {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  width: 100%;
}

.wishlist-container {
  max-width: 600px;
  width: 100%;
}

.product-list-container {
  padding: 0px 10px;
  // overflow-y: auto;
  scrollbar-width: thin;
  max-height: 100vh;
  width: 100%;
  max-width: 600px;

}

.product-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 20px;
  height: 90px;
}

.product-image {
  margin-right: 5px;
  height: 90px;
  width: 90px;
}

.product-details {
  width: 100%;
}

.product-title {
  font-size: 12px;
  font-weight: 600;
  color: v-bind("ColorPick.primaryColor");
  max-lines: 1;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.product-description {
  font-size: 9px;
  color: gray;
  max-lines: 2;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  width: 100%;
}

.product-price {
  font-size: 12px;
  font-weight: 600;
  color: v-bind("ColorPick.primaryColor");
  max-lines: 1;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.product-counter {
  display: flex;
  align-items: center;
  gap: 10px;
  border: 1px solid #ccc;
  background-color: v-bind("ColorPick.primaryColor");
  color: "#ffffff";
  border-radius: 20px;
  // width: 60px;
  height: 22px;
  padding: 0px 12px;
}
</style>
