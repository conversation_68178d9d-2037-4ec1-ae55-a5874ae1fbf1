import { AddressDto } from "./address.dto";
import { CouponDto } from "./coupon.dto";
import { PaymentDetailsDto } from "./payment_details.dto";
import { RedeemedRewardsDto } from "./rewards.dto";

export class CartSummaryDto {
  show_msg: boolean;
  currency: string;
  products: CartProductsDto;
  address: AddressDto | undefined;
  vouchers: RedeemedRewardsDto[];
  payment_details: PaymentDetailsDto;
  removed_products: string[];
  removed_vouchers: string[];
  coupon: CouponDto | null;

  constructor(data: any) {
    try {
      this.vouchers = data.vouchers.map(
        (item: any) => new RedeemedRewardsDto(item)
      );
    } catch (_) {
      this.vouchers = [];
    }

    try {
      this.removed_products = data.removed_products.map((item: any) => `${item}`);
    } catch (_) {
      this.removed_products = [];
    }

    try {
      this.removed_vouchers = data.removed_vouchers.map((item: any) => `${item}`);

    } catch (_) {
      this.removed_vouchers = [];
    }

    this.show_msg = data.show_msg == 1;
    this.currency = data.currency;
    this.products = new CartProductsDto(data.products);
    this.address = data.address;
    this.payment_details = new PaymentDetailsDto(data.payment_details);
    this.coupon = data.coupon ? new CouponDto(data.coupon) : null;
  }
}

export class CartProductsDto {
  normal: CartProductDataDto[];

  constructor(data: any) {
    try {
      this.normal = data.normal.map((item: any) => new CartProductDataDto(item));
    } catch (_) {
      this.normal = [];
    }
  }
}

export class CartProductDataDto {
  cart_product_id: number;
  is_birthday: boolean;
  is_free: boolean;  
  variation: string;
  name: string;
  image: string;
  quantity: number;
  unit_price: string;
  total_price: string;
  unit_tax: string;
  total_tax: string;
  product_category: string;
  product_subcategory: string;
  unit_weight: string;


  constructor(data: any) {
    this.cart_product_id = data.cart_product_id;
    this.is_birthday = data.is_birthday;
    this.is_free = data.is_free;
    this.name = data.name;
    this.image = data.image;
    this.variation = data.variation || data.variation_name;
    this.quantity = data.quantity;
    this.unit_price = data.unit_price;
    this.total_price = data.total_price;
    this.unit_tax = data.unit_tax;
    this.total_tax = data.total_tax;
    this.product_category = data.product_category;
    this.product_subcategory = data.product_subcategory;
    this.unit_weight = data.unit_weight;
  }
}