<script setup lang="ts">
import GeneralContent from "@/components/GeneralContent.vue";
import { onMounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";

import type { GeneralPageDto } from "@/dto/general_page.dto";
import GeneralPageApi from "@/services/general_page_api";

const { t } = useI18n();
const openDialog = ref<boolean>(false);
const isLoading = ref<boolean>(false);
const router = useRouter();

//Data
const data = ref<GeneralPageDto>();
const dialogMsg = ref<string>("");

//Function
function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

// Function - API
async function loadData() {
  try {
    isLoading.value = true;
    data.value = await GeneralPageApi.getAboutUs();
  } catch (error) {
    dialogMsg.value = `${error}`;
    openDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}

onMounted(() => {
  loadData();
});
</script>

<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading"></LoadingPlus>
  <!-- Success Dialog -->
  <DialogPlus
    persistent
    v-model="openDialog"
    :title="t('about_us')"
    :confirmText="t('okay')"
    :onClickConfirm="
      () => {
        openDialog = false;
        goBack();
      }
    "
  >
    {{ dialogMsg }}
  </DialogPlus>
  <GeneralContent :title="t('about_us')" :htmlContent="data?.content ?? ''" />
  <!-- Body  -->
</template>
