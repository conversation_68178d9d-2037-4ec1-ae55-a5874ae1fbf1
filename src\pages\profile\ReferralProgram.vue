<script setup lang="ts">
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";
import ColorPick from "@/helpers/colorpick";
import { useI18n } from "vue-i18n";
import { onMounted, ref, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import SnackBarPlus from "@/components/custom_control/SnackBarPlus.vue";
import LoadingPlus from "@/components/custom_control/LoadingPlus.vue";
import copyIcon from "../../assets/icons/copy.png";
import circle1 from "../../assets/icons/circle1.png";
import circle2 from "../../assets/icons/circle2.png";
import circle3 from "../../assets/icons/circle3.png";
import type { UserDto } from "@/dto/user.dto";
import AuthPageApi from "@/services/auth_api";
import RoutersHelper from "@/helpers/routes_helper";
import ReferralTypeEnum from "@/enums/ReferralTypeEnum";
import ReferralApi from "@/services/referral_api";
import LoadingPlusItem from "@/components/custom_control/LoadingPlusItem.vue";
import type { ReferralDto } from "@/dto/referral.dto";
import NoData from '@/components/custom_control/NoData.vue';
import ReferralItem from "@/components/ReferralItem.vue";

const { t } = useI18n();


//Variables
const isLoading = ref<boolean>(false);
const isListLoading = ref<boolean>(false);

// Route
const route = useRoute();
const router = useRouter();

const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>('');

//Data
const userData = ref<UserDto>();
const page = ref<number>(+(route.query?.page ?? 1));
const last_page = ref<number>();
const listData = ref<ReferralDto[]>([]);


// How it works Steps
const steps = [
  {
    image: circle1,
    title: "Share Your Unique Referral Code",
    description:
      "Access your personalized referral code in the Nattõme™ mobile app under the 'Referral Program' section.",
  },
  {
    image: circle2,
    title: "Invite Friends and Family",
    description:
      "Access your personalized referral code in the Nattõme™ mobile app under the 'Referral Program' section.",
  },
  {
    image: circle3,
    title: "Earn Rewards",
    description:
      "When someone uses your referral code to make their first purchase, both you and the referred customer receive exclusive discounts or points.",
  },
];



// tabs
const activeTab = ref<string>(route.query.tab?.toString() ?? ReferralTypeEnum.all);

const tabs = [
  { title: t("all_history"), tab: ReferralTypeEnum.all },
  { title: t("signed_up"), tab: ReferralTypeEnum.signed },
  { title: t("in_app_purchased"), tab: ReferralTypeEnum.purchased },
];

//Functions
function copyReferralLink() {
  if (userData.value?.referral_code) {
    navigator.clipboard.writeText(`${window.location.protocol}//${window.location.host}${RoutersHelper.referral}?referralCode=${userData.value?.referral_code}`);

    snackBarMsg.value = t('referral_link_copied');
    openSnackBar.value = true;
  }
}

onMounted(() => {
  router.replace({ query: { tab: activeTab.value, page: page.value } });
  loadUser();
  loadList();
})

//Call Api
async function loadUser() {
  try {
    isLoading.value = true;

    userData.value = await AuthPageApi.getProfile();
  } catch (error) {

    snackBarMsg.value = `${error}`;
    openSnackBar.value = true;
  } finally {
    isLoading.value = false;
  }
}

async function loadList() {
  try {
    isListLoading.value = true;

    listData.value = [];

    var tempData = await ReferralApi.list(page.value, undefined, activeTab.value ?? ReferralTypeEnum.all,);

    last_page.value = tempData.last_page ?? undefined;

    listData.value = tempData.data ?? [];
  } catch (error) {

    snackBarMsg.value = `${error}`;
    openSnackBar.value = true;
  } finally {
    isListLoading.value = false;
  }
}
//isListLoading

watch(activeTab, (newValue) => {
  page.value = 1;
  router.replace({ query: { tab: activeTab.value, page: page.value } });
  loadList();
});

watch(page, (value) => {
  var tempPage: number = isNaN(value) ? 1 : value;

  router.replace({ query: { tab: activeTab.value, page: tempPage } });
  loadList();
});

watch(route, (value) => {
  page.value = isNaN(+(value.query?.page ?? 1)) ? 1 : +(value.query?.page ?? 1);
  activeTab.value = route.query.tab?.toString() ?? ReferralTypeEnum.all;
  loadList();
})
</script>

<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading" />

  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>

  <AppBarPlus :title="t('referral_program')" :back="true" />

  <!-- AppBarPlus Spacing -->
  <div style="height: 20px"></div>

  <div class="referral-program">
    <div class="referral-program-container">
      <div class="referral-program-content">


        <div style="height: 60px"></div>

        <!-- How It Works -->
        <div class="referral-program-how-it-works">
          <div class="referral-program-how-it-works-title">
            {{ t("how_it_works") }}
          </div>

          <v-list lines="three" class="referral-program-how-it-works-container">
            <v-list-item v-for="(step, index) in steps" :key="index">
              <template v-slot:prepend>
                <v-avatar size="24">
                  <img :src="step.image" style="width: 24px; height: 24px" />
                </v-avatar>
              </template>
              <template v-slot:title>
                <span class="v-list-item-title">{{ step.title }}</span>
              </template>
              <template v-slot:subtitle>
                <span class="v-list-item-subtitle">{{ step.description }}</span>
              </template></v-list-item>
          </v-list>
        </div>
        <!-- How It Works -->

        <div style="height: 20px"></div>

        <!-- Referral Link Button -->
        <div v-if="userData?.referral_code">
          <div class="referral-link-title">
            {{ t("your_referral_code_is") }}
            <br />
            <span>
              {{ userData?.referral_code }}
            </span>
          </div>
          <VBtn class="button-plus" width="100%" :color="ColorPick.referralLinkButton" @click="copyReferralLink">
            <template v-slot>
              <span class="button-plus-text">{{
                t("tap_here_to_copy_your_referral_link")
                }}</span>

              <div style="width: 20px"></div>
              <VImg :src="copyIcon" :width="22" :height="22" />
            </template>
          </VBtn>
        </div>
        <!-- Referral Link Button -->

        <div style="height: 30px"></div>

        <!-- Bottom Tab  -->
        <VTabs v-model="activeTab" fixed-tabs grow :style="{
          maxWidth: '600px',
        }">
          <VTab v-for="item in tabs" :value="item.tab">
            {{ item.title }}
          </VTab>
        </VTabs>
        <VWindow v-model="activeTab">
          <VWindowItem :value="ReferralTypeEnum.all">
            <div v-if="isListLoading" :style="{ padding: '10px' }">
              <LoadingPlusItem></LoadingPlusItem>
            </div>
            <div v-else>
              <div v-if="listData.length > 0">
                <div v-for="(item, index) in listData" :key="index">
                  <ReferralItem :name="item.name" :date="item.date" :status="item.status" :rewardStatus="item.rewards">
                  </ReferralItem>
                </div>
              </div>
              <NoData v-else></NoData>
            </div>
          </VWindowItem>

          <VWindowItem :value="ReferralTypeEnum.signed">
            <div v-if="isListLoading" :style="{ padding: '10px' }">
              <LoadingPlusItem></LoadingPlusItem>
            </div>
            <div v-else>
              <div v-if="listData.length > 0">
                <div v-for="(item, index) in listData" :key="index">
                  <ReferralItem :name="item.name" :date="item.date" :status="item.status" :rewardStatus="item.rewards">
                  </ReferralItem>
                </div>
              </div>
              <NoData v-else></NoData>
            </div>
          </VWindowItem>
          <VWindowItem :value="ReferralTypeEnum.purchased">
            <div v-if="isListLoading" :style="{ padding: '10px' }">
              <LoadingPlusItem></LoadingPlusItem>
            </div>
            <div v-else>
              <div v-if="listData.length > 0">
                <div v-for="(item, index) in listData" :key="index">
                  <ReferralItem :name="item.name" :date="item.date" :status="item.status" :rewardStatus="item.rewards">
                  </ReferralItem>
                </div>
              </div>
              <NoData v-else></NoData>
            </div>
          </VWindowItem>
        </VWindow>
        <!-- Bottom Tab  -->

        <div style="height: 70px"></div>

        <div class="pagination">
          <VPagination v-model="page" :length="last_page ?? 1" rounded="circle" />
        </div>
      </div>
    </div>
  </div>

  <!-- Body  -->
</template>

<style lang="scss" scoped>
.referral-program {
  width: 100%;
  display: inline-table;
  text-align: -webkit-center;
}

.referral-program-container {
  height: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  padding: 0px 10px;
}

.referral-program-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.referral-program-how-it-works {
  padding: 20px;
  background-color: v-bind("ColorPick.referraProgramBackgroudColor");
  text-align: left;
}

.referral-program-how-it-works-title {
  font-size: 17px;
  font-weight: 600;
  color: v-bind("ColorPick.fontColor");
}

.referral-program-how-it-works-container {
  background-color: v-bind("ColorPick.referraProgramBackgroudColor");
}

.v-list-item-title {
  font-size: 15px;
  /* Adjust title size */
  font-weight: 500px;
  color: #281d1b;
}

.v-list-item-subtitle {
  font-size: 12px;
  /* Adjust subtitle size */
  font-weight: 400px;
  color: #4b4b4b;
}

.button-plus {
  border-radius: 11px;
  text-transform: none;
  block-size: 45px;
}

.button-plus-text {
  font-size: 15px;
  font-weight: 600;
  color: white;
}


.referral-link-title {
  font-size: 18px;
  font-weight: 500;
  color: v-bind("ColorPick.fontColor");
  padding: 15px;
  text-align: center;

  span {
    font-size: 20px;
    font-weight: 600;
  }
}

.pagination {
  position: fixed;
  width: 100%;
  max-width: 600px;
  bottom: 0;
  background-color: v-bind('ColorPick.backgroundColor');
  padding: 10px 0;

}
</style>

<style lang="scss">
.v-tabs {
  overflow-x: auto !important;
  white-space: nowrap;
}

.v-tab {
  text-transform: none !important;
}

@media (max-width: 385px) {
  .v-tab {
    font-size: 12px;
    padding: 8px 12px;
  }
}
</style>