import { VoucherTypeEnum, DiscountTypeEnum, VoucherStatusEnum } from "@/enums/VoucherEnum";

export class RewardsDto {
  id: number;
  brand: string;
  country: string;
  type: VoucherTypeEnum;
  name: string;
  banner: string;
  image: string;
  image_url: string;
  list_image: string;
  list_image_url: string;
  banner_url: string;
  description?: string;
  how_to_use?: string;
  terms_and_condition?: string;
  redeem_points?: number;
  min_spend: number;
  discount_type?: DiscountTypeEnum;
  value?: number;
  voucher_value?: string;
  max_quantity?: number;
  redeemed_quantity?: number;
  first_time_purchase: boolean;
  is_birthday: boolean;
  is_stackable: boolean;
  is_active: boolean;
  fully_redeemed: boolean;
  can_redeem: boolean;
  free_product?: FreeProductDto[];
  available_start_date?: string;
  available_end_date?: string;
  effective_start_date?: string;
  effective_end_date?: string;
  status: string;

  constructor(data: any) {
    let tempVoucherType: VoucherTypeEnum;
    try {
      tempVoucherType = VoucherTypeEnum[data.type as keyof typeof VoucherTypeEnum];
    } catch (_) {
      tempVoucherType = VoucherTypeEnum.discount;
    }

    let tempDiscountType: DiscountTypeEnum | undefined;
    try {
      tempDiscountType = DiscountTypeEnum[data.discount_type as keyof typeof DiscountTypeEnum];
    } catch (_) { }

    let tempDiscountValue: number | undefined;
    try {
      tempDiscountValue = parseInt(data.value);
    } catch (_) { }

    let tempFreeProduct: FreeProductDto[] | undefined;
    try {
      tempFreeProduct = data.free_product.map((item: any) => new FreeProductDto(item));
    } catch (_) { }

    this.id = data.id;
    this.brand = data.brand;
    this.country = data.country;
    this.type = tempVoucherType;
    this.name = data.name;
    this.banner = data.banner;
    this.image = data.image;
    this.image_url = data.image_url;
    this.list_image = data.list_image;
    this.list_image_url = data.list_image_url;
    this.banner_url = data.banner_url;
    this.description = data.description;
    this.how_to_use = data.how_to_use;
    this.terms_and_condition = data.terms_condition;
    this.redeem_points = data.redeem_points;
    this.min_spend = data.min_spend;
    this.discount_type = tempDiscountType;
    this.value = tempDiscountValue;
    this.voucher_value = data.voucher_value;
    this.max_quantity = data.max_quantity;
    this.redeemed_quantity = data.redeemed_quantity;
    this.first_time_purchase = data.first_time_purchase == 1;
    this.is_birthday = data.is_birthday == 1;
    this.is_stackable = data.is_stackable == 1;
    this.is_active = data.is_active == 1;
    this.fully_redeemed = data.fully_redeemed;
    this.can_redeem = data.can_redeem;
    this.free_product = tempFreeProduct;
    this.available_start_date = data.available_start_date;
    this.available_end_date = data.available_end_date;
    this.effective_start_date = data.effective_start_date;
    this.effective_end_date = data.effective_end_date;
    this.status = data.status;
  }
}

export class FreeProductDto {
  image?: string;
  product_name?: string;
  variation_name?: string;
  quantity?: number;

  constructor(data: any) {
    this.image = data.image;
    this.product_name = data.product_name;
    this.variation_name = data.variation_name;
    this.quantity = data.quantity;
  }
}


export class RedeemedRewardsDto {
  id: number;
  user_id: number;
  voucher_id: number;
  voucher_value: string;
  voucher_data?: RewardsDto;
  is_effective: boolean;
  cannot_use_reason?: string;
  status?: VoucherStatusEnum;
  free_product?: FreeProductDto[];
  used_date?: string;
  created_at: string;
  effective_start_date: string;
  effective_end_date: string;
  is_selected?: boolean;

  constructor(data: any) {
    let tempVoucher: RewardsDto | undefined;
    try {
      tempVoucher = new RewardsDto(data.voucher_data);
    } catch (_) { }

    let tempFreeProduct: FreeProductDto[] | undefined;
    try {
      tempFreeProduct = data.free_product.map((item: any) => new FreeProductDto(item));
    } catch (_) { }

    let tempVoucherStatus: VoucherStatusEnum | undefined;
    try {
      tempVoucherStatus = VoucherStatusEnum[data.status as keyof typeof VoucherStatusEnum];
    } catch (_) { }

    this.id = data.id;
    this.user_id = data.user_id;
    this.voucher_id = data.voucher_id;
    this.voucher_value = data.voucher_value;
    this.voucher_data = tempVoucher;
    this.is_effective = data.is_effective;
    this.status = tempVoucherStatus;
    this.free_product = tempFreeProduct;
    this.used_date = data.used_date;
    this.created_at = data.created_at;
    this.effective_start_date = data.effective_start_date;
    this.effective_end_date = data.effective_end_date;
    this.is_selected = data.is_selected;
  }
}