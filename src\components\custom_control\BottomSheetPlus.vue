<script setup lang="ts">
import { translate } from "../../plugins/i18n";
import ColorPick from "@/helpers/colorpick";

interface Props {
  title?: string | undefined;
  persistent?: boolean;
  confirmText?: string;
  cancelText?: string;
  onClickConfirm?: () => void;
  onClickCancel?: () => void;
  onClickClose?: () => void;
}

const props = withDefaults(defineProps<Props>(), {
  persistent: false,
  confirmText: translate("okay"),
  cancelText: translate("cancel"),
});
const model = defineModel<boolean>();
</script>

<template>
  <VBottomSheet v-model="model" scrollable inset :persistent="persistent">
    <VCard class="bottom-sheet-plus" style="background-color: #f7f5f3">
      <!-- <VIcon v-if="!persistent" class="bottom-sheet-plus-close ri-close-line" size="35" @click="onClickClose ? onClickClose() : model = false">
            </VIcon> -->
      <div style="height: 20px" />
      <VCardTitle v-if="props.title">
        <div class="bottom-sheet-plus-title">
          {{ props.title }}
        </div>
        <!-- <VDivider
          tickness="2"
          :style="{ color: ColorPick.fontColor, opacity: 0.2 }"
        /> -->
      </VCardTitle>
      <VCardText>
        <slot></slot>
      </VCardText>
      <VCardActions class="bottom-sheet-plus-actions">
        <ButtonPlus
          @click="onClickConfirm"
          variant="elevated"
          :disable="!onClickConfirm"
        >
          {{ confirmText }}
        </ButtonPlus>
        <ButtonPlus
          v-if="onClickCancel"
          @click="onClickCancel"
          variant="outlined"
        >
          {{ cancelText }}
        </ButtonPlus>
      </VCardActions>
    </VCard>
  </VBottomSheet>
</template>

<style lang="scss">
@media (min-width: 600px) {
  .v-bottom-sheet.v-bottom-sheet--inset {
    max-width: 600px !important;
  }
}

.v-bottom-sheet__content {
  border-radius: 20px 20px 0 0 !important;
  max-width: 600px !important;
  justify-self: center !important;
}

.bottom-sheet-plus {
  border-radius: 20px 20px 0 0 !important;
  max-width: 600px !important;
}

.bottom-sheet-plus-title {
  font-size: 23px;
  color: #000000;
  font-weight: 400;
  text-align: center;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  padding: 5px;
}

.bottom-sheet-plus-close {
  position: absolute;
  right: 15px;
  top: 10px;
}

.bottom-sheet-plus-actions {
  display: flex;
  justify-content: start;
  // block-size: 60px;
  inline-size: 100%;
  padding: 20px;
}

.bottom-sheet-plus-actions .v-btn {
  flex: 1;
}

.v-card .v-card-text {
  padding: 16px 24px 24px !important;
}
</style>
