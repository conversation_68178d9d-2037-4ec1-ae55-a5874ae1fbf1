// String extension to remove HTML tags
interface String {
  removeHtmlTags(): string;
}

String.prototype.removeHtmlTags = function (): string {
  let htmlString: string = this.toString();

  // Remove HTML tags
  htmlString = htmlString.replace(/<[^>]*>/g, '');
  // Replace &nbsp; with space
  htmlString = htmlString.replace(/&nbsp;/g, ' ');
  // Replace <br> and <br/> with newline
  htmlString = htmlString.replace(/<br\s*\/?>/gi, '\n');
  // Replace & with &amp;
  htmlString = htmlString.replace(/&amp;/g, '&');
  // Replace < with &lt;
  htmlString = htmlString.replace(/&lt;/g, '<');
  // Replace > with &gt;
  htmlString = htmlString.replace(/&gt;/g, '>');
  // Replace <p>, <ol>, </ol>, <ul>, </ul> with newline
  htmlString = htmlString.replace(/<\/?[pou]l\s*\/?>/gi, '\n');
  // Remove <a href='...'> tags
  htmlString = htmlString.replace(/<a\s[^>]*>/gi, '');
  // Remove </a> tags
  htmlString = htmlString.replace(/<\/a>/gi, '');

  return htmlString;
};