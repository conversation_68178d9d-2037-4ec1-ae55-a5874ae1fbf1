import AppConfig from "@/appconfig";
import { PaginationDto } from "@/dto/pagination.dto";
import { ReferralDto } from "@/dto/referral.dto";
import ReferralTypeEnum from "@/enums/ReferralTypeEnum";
import ApiHelper from "@/helpers/api_helper";

class ReferralApi {
  public static async list(
    page: number,
    perPage?: number,
    type: string = ReferralTypeEnum.all
  ): Promise<PaginationDto> {
    try {
      var response = await ApiHelper.get(AppConfig.apiReferralUrl, undefined, {
        page: page,
        per_page: perPage ?? AppConfig.paginationLimit,
        type: type === ReferralTypeEnum.all ? null : type,
      });

      const tempReferral: PaginationDto = new PaginationDto({
        current_page: response?.data?.data?.current_page,
        last_page: response?.data?.data?.last_page,
        per_page: response?.data?.data?.per_page,
        total: response?.data?.data?.total,
        data: response?.data?.data?.data?.map(
          (itemData: any) => new ReferralDto(itemData)
        ),
      });

      return tempReferral;
    } catch (error) {
      throw error;
    }
  }
}

export default ReferralApi;
