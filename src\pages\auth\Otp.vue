<script setup lang="ts">
import logo from "@/assets/icons/logo.png";
import { useI18n } from "vue-i18n";
import ColorPick from "@/helpers/colorpick";
import RoutersHelper from "@/helpers/routes_helper";
import { onMounted, onUnmounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import LoadingPlus from "@/components/custom_control/LoadingPlus.vue";
import AuthPageApi from "@/services/auth_api";
import localStorageHelper from "@/helpers/localstorage_helper";
import { UserDto } from "@/dto/user.dto";

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

//Values
const isLoading = ref<boolean>(false);

const phone = ref<string>("");
const otp = ref<string>("");

const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

//Timer
const timerDefault: number = 60;
const timeRemaining = ref(timerDefault);
const isTimerRunning = ref(false);
let timer: any = null;

//Functions
//Timer
const startTimer = () => {
  if (!isTimerRunning.value && timeRemaining.value > 0) {
    isTimerRunning.value = true;
    timer = setInterval(() => {
      timeRemaining.value--;
      if (timeRemaining.value === 0) {
        resetTimer();
      }
    }, 1000);
  }
};

const resetTimer = () => {
  clearInterval(timer);
  timeRemaining.value = timerDefault;
  isTimerRunning.value = false;
};

const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, "0")}:${secs
    .toString()
    .padStart(2, "0")}`;
};

async function sentOtp() {
  //If Loading will no do anything
  if (isLoading.value) {
    snackBarMsg.value = t("please_wait...");
    openSnackBar.value = true;
    return;
  }

  try {
    isLoading.value = true;
    await AuthPageApi.sendOtp(phone.value);
    startTimer();
  } catch (e) {
    snackBarMsg.value = `${e}`;
    openSnackBar.value = true;
  } finally {
    isLoading.value = false;
  }
}

//Functions
async function otpSubmit() {
  if (otp?.value !== "" && otp?.value.length === 6) {
    otpVerify();
  } else {
    snackBarMsg.value = t("please_enter_otp");
    openSnackBar.value = true;
  }
}

//Call API
async function otpVerify() {
  //If Loading will no do anything
  if (isLoading.value) {
    snackBarMsg.value = t("please_wait...");
    openSnackBar.value = true;
    return;
  }

  try {
    isLoading.value = true;
    var tempdata = await AuthPageApi.login(phone.value, otp.value);

    if (tempdata["need_register"] === 1) {
      router.push({
        path: RoutersHelper.setupProfile,
        query: {
          phoneNumber: phone.value,
          referralCode: route.query.referralCode,
        },
      });
    } else {
      const tempUserDto = new UserDto(tempdata);

      if (tempUserDto.token) {
        localStorageHelper.setUserToken(tempUserDto.token);
        router.push(RoutersHelper.home);
      }
    }
  } catch (e) {
    snackBarMsg.value = `${e}`;
    openSnackBar.value = true;
  } finally {
    isLoading.value = false;
  }
}

onMounted(() => {
  if (route.query.phoneNumber) {
    phone.value = route.query.phoneNumber as string;

    sentOtp();
  } else {
    router.push(RoutersHelper.login);
  }
});

onUnmounted(() => {
  clearInterval(timer);
});
</script>

<template>
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>
  <!-- Loading Plus -->
  <LoadingPlus v-if="isLoading" />
  <div class="otp">
    <div class="otp-container">
      <div style="height: 50px"></div>
      <div class="otp-logo">
        <VImg :src="logo" :cover="false" height="60" width="60" />
      </div>
      <h1 class="otp-title">
        {{ t("otp_validation") }}
      </h1>
      <div class="otp-description">
        {{ t("otp_description").replace("[phone_number]", phone) }}
      </div>
      <div style="height: 20px"></div>
      <VCard class="otp-card">
        <VCardText>
          <div>
            <VOtpInput v-model="otp" length="6"></VOtpInput>
            <div class="otp-resend-text">
              {{ t("didnt_receive_code") }}
              <a v-if="!isTimerRunning" @click="sentOtp">
                {{ t("resend") }}
              </a>
              <span v-else>
                {{ formatTime(timeRemaining) }}
              </span>
            </div>
          </div>
          <div style="height: 20px"></div>
        </VCardText>
        <VCardActions>
          <ButtonPlus
            class="otp-continue"
            :color="ColorPick.buttonColorAuth"
            @click="otpSubmit"
          >
            {{ t("continue") }}
          </ButtonPlus>
        </VCardActions>
      </VCard>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.otp {
  width: 100%;
  position: absolute;
  justify-items: center;
  text-align: -webkit-center;
  overflow: auto;
  height: 100%;
}

.otp-container {
  padding: 20px;
  max-width: 600px;
}

.otp-logo {
  padding: 10px;
}

.otp-title {
  font-size: 32px;
  font-weight: 700;
  text-align: center;
  padding: 3px;
  color: v-bind("ColorPick.fontColorWhite");
}

.otp-description {
  font-size: 17px;
  font-weight: 500;
  text-align: center;
  padding: 3px;
  color: v-bind("ColorPick.fontColorWhite");
}

.otp-card {
  width: 100%;
  max-width: 400px;
  border-radius: 12px;
  padding: 15px;
  overflow: visible;
}

.otp-resend-text {
  text-align: center;
  padding: 10px;
  font-size: 14px;
  font-weight: 500;
}

.otp-resend-text span {
  font-size: 15px;
  font-weight: 600;
}

.otp-continue {
  width: 100%;
}
</style>
