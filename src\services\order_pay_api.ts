import AppConfig from "@/appconfig";
import { OrderPaymentDto } from "@/dto/order_payment.dto";
import ApiHelper from "@/helpers/api_helper";
import { translate } from "@/plugins/i18n";

class OrderPayApi {

  //Get Order Payment
  public static async orderPayment(order_no?: string): Promise<OrderPaymentDto> {
    try {

      var response = await ApiHelper.get(
        AppConfig.apiOrderPaymentUrl + order_no,
        undefined,
      );
      const tempSummary: OrderPaymentDto = new OrderPaymentDto(
        response.data.data
      );
      console.log(tempSummary);

      return tempSummary;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  //Get Order Payment

  // Apply Voucher
  public static async applyVoucher(
    voucher_id: string,
    order_id: string
  ): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiOrderApplyVoucherUrl}`,
        undefined,
        {
          id: order_id,
          voucher_id: voucher_id,
        }
      );

      return response.data.data;
    } catch (error) {
      throw error;
    }
  }
  // Apply Voucher

  // Remove Voucher from cart
  public static async removeVoucher(voucher_id: string, order_id: string): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiOrderRemoveVoucherUrl}`,
        undefined,
        {
          voucher_id: voucher_id,
          id: order_id,
        }
      );
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }
  // Remove Voucher from cart

  // Apply Coupon
  public static async applyCoupon(
    coupon_code: string,
    order_id: string
  ): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiOrderApplyCouponUrl}`,
        undefined,
        {
          coupon_code: coupon_code,
          id: order_id,
        }
      );

    //   const tempCouponData: CreateCouponDto = new CreateCouponDto(response.data.data);

      return response.data.data;
    } catch (error) {
      throw error;
    }
  }
  // Apply Coupon

  // Remove Coupon from cart
  public static async removeCoupon(coupon_id: string, order_id: string): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiOrderRemoveCouponUrl}`,
        undefined,
        {
          coupon_id: coupon_id,
          id: order_id,
        }
      );
      return response.data.data;
    } catch (error) {
      console.log('error', error);
      throw error;
    }
  }
  // Remove Coupon from cart

//   Pay Order
  public static async orderCreate(
    remark?: string,
    order_id?: string,
  ): Promise<string> {
    try {

        console.log("order_id", order_id);
        console.log("remark", remark);

      var response = await ApiHelper.post(
        AppConfig.apiOrderPayUrl,
        undefined,
        {
          remark: remark,
          id: order_id,
        }
      );
    
      var url: URL | undefined;

      try {
        url = new URL(response?.data?.data?.payment_url);
      } catch (_) {}

      if (url) {
        return url.toString();
      } else {
        throw translate("payment_url_not_found");
      }
    } catch (error) {
      console.log('error',error);
      throw error;
    }
  }
}


export default OrderPayApi;