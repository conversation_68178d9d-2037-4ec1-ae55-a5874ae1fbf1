<script lang="ts" setup>
import { VueTelInput } from "vue-tel-input";
import "vue-tel-input/vue-tel-input.css";
import ColorPick from '@/helpers/colorpick';
import { ref, watch, onMounted } from "vue";

// Props
const props = defineProps({
    modelValue: String,
});

// Events
const emit = defineEmits(["update:modelValue", "validate"]);

// Local State
const value = ref<string | undefined>();

// watch for change Local State
watch(value, (val) => {
    emit("update:modelValue", val);
});

// watch for change Props
watch(
    () => props.modelValue,
    (val) => {
        value.value = val;
    }
);

// set initial value
onMounted(() => {
    value.value = props.modelValue;
});
</script>

<template>
    <VueTelInput v-model="value" @validate="emit('validate', $event)"></VueTelInput>
</template>

<style lang="scss">
.vue-tel-input {
    border-radius: 10px;
    padding: 10px;
    box-shadow: none !important;
    font-size: 16px;
}

.vue-tel-input:hover {
    border-color: v-bind('ColorPick.primaryColor') !important;
}

.vue-tel-input:focus-within {
    border-color: v-bind('ColorPick.primaryColor') !important;
    border-width: 2px !important;
}

.vti__dropdown-list {
    width: 300px;
}

.vti__dropdown-item {
    padding: 15px;
}
</style>