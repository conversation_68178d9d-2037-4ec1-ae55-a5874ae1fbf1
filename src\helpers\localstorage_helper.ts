import type { StoreLocatorDto } from "@/dto/store_locators.dto"

class localStorageHelper {
  //User Token
  static setUserToken(value: string) {
    localStorage.setItem('token', value)
  }
  static getUserToken(): string | null {
    return localStorage.getItem('token')
  }

  static clearUser() {
    localStorage.removeItem('token')
  }
  static clearAll() {
    localStorage.clear()
  }

  static setOrderType(value: string) {
    localStorage.setItem('orderType', value)
  }

   static getOrderType(): string | null {
    return localStorage.getItem('orderType')
  }

  static clearOrderType() {
    localStorage.removeItem('orderType')
  }

  static setOutletId(value: string) {
    localStorage.setItem('outletId', value)
  }

  static getOutletId(): string | null {
    return localStorage.getItem('outletId')
  }

  static clearOutletId() {
    localStorage.removeItem('outletId')
  }
}

export default localStorageHelper
