import AppConfig from "@/appconfig";
import { PaginationDto } from "@/dto/pagination.dto";
import { RedeemedRewardsDto, RewardsDto } from "@/dto/rewards.dto";
import ApiHelper from "@/helpers/api_helper";

class RewardApi {
  //Get Redeemed Orders List
  public static async redeemedOrderList(
    page: number,
    perPage?: number,
    isStackable?: number | undefined
  ): Promise<PaginationDto> {
    try {
      const body: any = {
        page: page,
        per_page: perPage ?? AppConfig.paginationLimit,
        type: "order",
      };

      if (isStackable !== undefined) {
        body.is_stackable = isStackable;
      }

      var response = await ApiHelper.post(
        AppConfig.apiRedeemedVoucherUrl,
        undefined,
        body
      );

      const tempRewards: PaginationDto = new PaginationDto(response.data.data);

      return tempRewards;
    } catch (error) {
      throw error;
    }
  }
  //Get Redeemable Orders List

  //Get Redeemable Rewards List
  public static async redeemableList(
    page: number,
    perPage?: number,
    isFeatured?: number,
    created_at?: string
  ): Promise<PaginationDto> {

    var body: any = {
      page: page,
      per_page: perPage ?? AppConfig.paginationLimit,
      type: "reward",
    };

    if (isFeatured !== undefined) {
      body.is_featured = isFeatured;
    }

    if (created_at !== undefined) {
      body.created_at = created_at;
    }

    try {
      var response = await ApiHelper.post(
        AppConfig.apiRedeemableVoucherUrl,
        undefined,
        body
      );

      const tempRewards: PaginationDto = new PaginationDto(response.data.data);

      return tempRewards;
    } catch (error) {
      throw error;
    }
  }
  //Get Redeemable Rewards List

  //Get Redeemable Rewards Details
  public static async redeemableDetail(id: string): Promise<RewardsDto> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiRedeemableVoucherUrl}detail`,
        undefined,
        {
          id: id,
        }
      );

      const tempRewardsDetail: RewardsDto = new RewardsDto(response.data.data);

      return tempRewardsDetail;
    } catch (error) {
      throw error;
    }
  }
  //Get Redeemable Rewards Details

  //Get Redeem Voucher
  public static async redeemVoucher(id: string): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiRedeemableVoucherUrl}claim`,
        undefined,
        {
          id: id,
        }
      );

      if (response.data.status) {
        return response.data.status;
      } else {
        throw response.data.message;
      }
    } catch (error) {
      throw error;
    }
  }
  //Get Redeem Voucher

  //Get Redeemed Rewards List
  public static async redeemedList(
    page: number,
    perPage?: number,
    is_available?: number | null,
  ): Promise<PaginationDto> {
    try {

      const body: any = {
        page: page,
        per_page: perPage ?? AppConfig.paginationLimit,
      };
      
      if (is_available !== null && is_available !== undefined) {
      body.is_available = is_available;
       }

      var response = await ApiHelper.post(
        AppConfig.apiRedeemedVoucherUrl,
        undefined,
        body,
      );

      const tempRewards: PaginationDto = new PaginationDto(response.data.data);

      return tempRewards;
    } catch (error) {
      throw error;
    }
  }
  //Get Redeemed Rewards List

  //Get Redeemed Rewards Details
  public static async redeemedDetail(id: string): Promise<RewardsDto> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiRedeemedVoucherUrl}detail`,
        undefined,
        {
          id: id,
        }
      );

      const tempRewards: RewardsDto = new RewardsDto(response.data.data);

      return tempRewards;
    } catch (error) {
      throw error;
    }
  }
  //Get Redeemed Rewards Details

  //Redeemed Voucher Use Now
  public static async voucherUseNow(id: string): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiRedeemedVoucherUseNowUrl}${id}`,
        undefined
      );

      if (response.data.status) {
        return response.data.status;
      } else {
        throw response.data.message;
      }
    } catch (error) {
      throw error;
    }
  }
  //Redeemed Voucher Use Now
}

export default RewardApi;
