<script setup lang="ts">
// Helpers
import ColorPick from "@/helpers/colorpick";
import RoutersHelper from "@/helpers/routes_helper";
import localStorageHelper from "@/helpers/localstorage_helper";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import { ref } from "vue";

// Components
import SettingItem from "@/components/SettingItem.vue";
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";

// Apis
import AuthPageApi from "@/services/auth_api";

const { t } = useI18n();
const router = useRouter();

//Values
const isLoading = ref<boolean>(false);
const openLogoutDialog = ref<boolean>(false);

const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

// Function
function toAboutUs() {
  router.push(RoutersHelper.aboutUs);
}

function toTermsAndConditions() {
  router.push(RoutersHelper.termsAndCondition);
}

function toPrivacyPolicy() {
  router.push(RoutersHelper.privacyPolicy);
}

function toAddress() {
  router.push({ path: RoutersHelper.address, query: { page: "1" } });
}

function toLogOut() {
  openLogoutDialog.value = false;
  submitLogout();
}

//Api
async function submitLogout() {
  //If Loading will no do anything
  if (isLoading.value) {
    snackBarMsg.value = t("please_wait...");
    openSnackBar.value = true;
    return;
  }

  try {
    isLoading.value = true;

    await AuthPageApi.logout();
  } catch (e) {
    snackBarMsg.value = `${e}`;
    openSnackBar.value = true;
  } finally {
    isLoading.value = false;
    localStorageHelper.clearAll();
    router.push(RoutersHelper.login);
  }
}
</script>

<template>
  <!-- Loading Plus -->
  <LoadingPlus v-if="isLoading" />

  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>

  <!-- Logout Dialog -->
  <DialogPlus
    v-model="openLogoutDialog"
    :title="t('log_out')"
    :confirmText="t('yes')"
    :cancelText="t('no')"
    :onClickConfirm="toLogOut"
    :onClickCancel="
      () => {
        openLogoutDialog = false;
      }
    "
  >
    {{ t("are_you_sure_you_want_to_logout") }}
  </DialogPlus>

  <!-- <AppBarPlus :title="t('settings')" :back="true" /> -->

  <VAppBar elevation="0">
    <VBtn
      :color="ColorPick.fontColor"
      @click="
        () => {
          router.push(RoutersHelper.account);
        }
      "
    >
      <VIcon class="ri-arrow-left-s-line"></VIcon>
    </VBtn>
    <VAppBarTitle
      :style="{
        color: ColorPick.fontColor,
        textAlign: 'left',
        fontFamily: `'Nunito Sans', sans-serif`,
      }"
    >
      {{ t("settings") }}
    </VAppBarTitle>
  </VAppBar>

  <!-- AppBarPlus Spacing -->
  <div style="height: 20px"></div>

  <div class="settings">
    <div class="settings-container" :style="{ padding: '10px 20px 10px 20px' }">
      <div style="height: 60px"></div>
      <div>
        <!-- Address -->
        <SettingItem @click="() => toAddress()" :title="t('address')" />

        <!-- Abouts Us -->
        <SettingItem @click="() => toAboutUs()" :title="t('about_us')" />

        <!--Terms & Conditions -->
        <SettingItem
          @click="() => toTermsAndConditions()"
          :title="t('terms_and_condition')"
        />

        <!-- Privacy Policy -->
        <SettingItem
          @click="() => toPrivacyPolicy()"
          :title="t('privacy_policy')"
          :isLast="'true'"
        />

        <div style="height: 20px"></div>

        <!-- Log out Button -->
        <div>
          <ButtonPlus @click="() => (openLogoutDialog = true)" width="100%">{{
            t("log_out")
          }}</ButtonPlus>
        </div>
        <!-- Log out Button -->
      </div>
    </div>
  </div>

  <!-- Body  -->
</template>

<style lang="scss" scoped>
.settings {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
}

.settings-container {
  max-width: 600px;
  width: 100%;
  height: 100%;
}

.settings-content-title {
  font-size: 15px;
  font-weight: 500;
  color: v-bind("ColorPick.settingsTitleFontColor");
  text-align: left;
}
</style>
