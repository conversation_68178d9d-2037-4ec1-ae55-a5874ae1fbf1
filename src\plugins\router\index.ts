import { translate } from '../i18n'
import type { App } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { routes } from './routes'

const router = createRouter({
  history: createWebHistory('/'),
  routes,
})

export default function (app: App) {
  router.beforeEach((to, from, next) => {
    document.title = translate('app_name')
    if (to.meta?.title !== undefined && to.meta?.title !== null) {
      document.title = `${to.meta?.title.toString()} | ${translate('app_name')}`
    }
    next()
  })

  app.use(router)
}

export { router }
