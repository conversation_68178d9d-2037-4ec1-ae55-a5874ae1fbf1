import AppConfig from "@/appconfig";
import { StoreLocatorDistanceDto, StoreLocatorDto, StoreLocatorPaginationDto } from '@/dto/store_locators.dto'
import ApiHelper from "@/helpers/api_helper";
import { translate } from "@/plugins/i18n";

class HomePageStoreLocatorApi {
   //Get Home Store Locator with featured List
  public static async homeStoreLocatorList(page: number, perPage?: number): Promise<StoreLocatorPaginationDto> {
    try {
      var response = await ApiHelper.get(AppConfig.apiStoreLocatorUrl, undefined, {
        "page": page,
        "per_page": perPage ?? AppConfig.paginationLimit,
        "featured": 1,
      });
      const tempSotreLocator: StoreLocatorPaginationDto = new StoreLocatorPaginationDto(response.data.data);

      return tempSotreLocator;
    } catch (error) {
      throw error;
    }
  }

   //Get all Store Locator List
  public static async storeLocatorList(page: number, perPage?: number, state?: string, searchText?: string): Promise<StoreLocatorPaginationDto> {
    try {

      var body  = {
      page: page,
      per_page : perPage ?? AppConfig.paginationLimit,
      search: searchText,
      state: state,
    };

    var response = await ApiHelper.post(AppConfig.apiOutletsUrl, undefined, body);

    const tempSotreLocator: StoreLocatorPaginationDto = new StoreLocatorPaginationDto(response.data.data);

      return tempSotreLocator;
    } catch (error) {
      throw error;
    }
  }

  //Get  Store Locator Details
  public static async outletDetail(id: string): Promise<StoreLocatorDto> {
    try {

      var response = await ApiHelper.get(AppConfig.apiOutletsUrl + id);

      if (response.data.data == null) {
        throw translate('data_not_found');
      }

      const tempNews: StoreLocatorDto = new StoreLocatorDto(response.data.data);

      return tempNews;
    } catch (error) {
      throw error;
    }
  }

   //Get all Store Locator List
  public static async storeLocatorDistance(lat: number, long?: number, outlet_id?: string): Promise<StoreLocatorDistanceDto> {
    try {

      var body  = {
      lat: lat,
      long : long,
      outlet_id: outlet_id,
    };
    var response = await ApiHelper.post(AppConfig.apiOutletDistanceUrl, undefined, body);

    const tempSotreLocator: StoreLocatorDistanceDto = new StoreLocatorDistanceDto(response.data.data);

      return tempSotreLocator;
    } catch (error) {
      throw error;
    }
  }
}


export default HomePageStoreLocatorApi;
