import AppConfig from "@/appconfig";
import { AddressDto } from "@/dto/address.dto";
import { PaginationDto } from "@/dto/pagination.dto";
import ApiHelper from "@/helpers/api_helper";

class AddressApi {
  //Get Address List
  public static async list(
    page: number,
    perPage?: number
  ): Promise<PaginationDto> {
    try {
      var response = await ApiHelper.get(AppConfig.apiAddressUrl, undefined, {
        page: page,
        per_page: perPage ?? AppConfig.paginationLimit,
        
      });

      const tempRewards: PaginationDto = new PaginationDto(response.data.data);

      return tempRewards;
    } catch (error) {
      throw error;
    }
  }
  //Get Address List

  //Get Address Details
  public static async details(id: string): Promise<AddressDto> {
    try {
      var response = await ApiHelper.get(AppConfig.apiAddressUrl + id);

      const tempRewards: AddressDto = new AddressDto(response.data.data);

      return tempRewards;
    } catch (error) {
      throw error;
    }
  }
  //Get Address Details

  //Create Address
  public static async create(
    label: string,
    recipient: string,
    phone_number: string,
    address: string,
    city: string,
    postcode: string,
    country: string,
    state: string,
    latitude: number,
    longitude: number
  ): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiAddressUrl}create`,
        undefined,
        {
          label: label,
          recipient: recipient,
          phone_number: phone_number,
          address: address,
          city: city,
          postcode: postcode,
          country: country, 
          state: state,
          latitude: latitude,
          longitude: longitude,
        }
      );

      if (response.status) {
        return response.status;
      } else {
        return response.message;
      }
    } catch (error) {
      throw error;
    }
  }
  //Create Address

  //Edit Address
  public static async edit(
    id: string,
    label: string,
    recipient: string,
    phone_number: string,
    address: string,
    city: string,
    postcode: string,
    country: string,
    state: string,
    latitude: number,
    longitude: number
  ): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiAddressUrl}update-address`,
        undefined,
        {
          id: id,
          label: label,
          recipient: recipient,
          phone_number: phone_number,
          address: address,
          city: city,
          postcode: postcode,
          country: country,
          state: state,
          latitude: latitude,
          longitude: longitude,
        }
      );

      if (response.status) {
        return response.status;
      } else {
        return response.message;
      }
    } catch (error) {
      throw error;
    }
  }
  //Edit Address

  //Delete Address
  public static async delete(id: string): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiAddressUrl}delete`,
        undefined,
        {
          id: id,
        }
      );

      if (response.status) {
        return response.status;
      } else {
        return response.message;
      }
    } catch (error) {
      throw error;
    }
  }
  //Delete Address
}
export default AddressApi;
