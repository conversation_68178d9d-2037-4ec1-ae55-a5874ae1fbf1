import AppConfig from "@/appconfig";
import { UserDto } from "@/dto/user.dto";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/helpers/api_helper";
import moment from "moment";

class AuthPageApi {
  //Send OTP
  public static async sendOtp(phone: string): Promise<any> {
    try {
      var response = await ApiHelper.post(AppConfig.apiSendOtpUrl, undefined, {
        phone_number: phone,
      });
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  //Login
  public static async login(phone: string, otp: string): Promise<any> {
    try {
      var response = await ApiHelper.post(AppConfig.apiLoginUrl, undefined, {
        phone_number: phone,
        otp: otp,
      });
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  //Register
  public static async register(
    name: string,
    email_address: string,
    dob: Date,
    phone_number: string,
  ): Promise<any> {
    try {
      var response = await ApiHelper.post(AppConfig.apiRegisterUrl, undefined, {
        name: name,
        email_address: email_address,
        dob: moment(dob).format("YYYY-MM-DD"),
        phone_number: phone_number,
      });
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  // Profile
  public static async getProfile(): Promise<UserDto> {
    try {
      var response = await ApiHelper.post(AppConfig.apiProfileUrl);

      const tempUser: UserDto = new UserDto(response.data.data);

      return tempUser;
    } catch (error) {
      throw error;
    }
  }

  // Edit Profile
  public static async editProfile(
    image: File | null,
    name: string,
    email_address: string,
    phone_number: string
  ): Promise<any> {
    try {
      let body: any = {
        name,
        email_address,
        phone_number,
      };

      if (image) {
        body.profile_image = image;
      }

      var response = await ApiHelper.post(
        AppConfig.apiEditProfileUrl,
        undefined,
        body,
        "multipart/form-data"
      );
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  // Verify profile Phone OTP
  public static async profileVerifyOTP(
    phone_number: string,
    otp: string
  ): Promise<any> {
    try {
      var body = {
        phone_number: phone_number,
        otp: otp,
      };
      var response = await ApiHelper.post(
        AppConfig.apiVerifyPhoneOtp,
        undefined,
        body
      );
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  //Logout
  public static async logout(): Promise<any> {
    try {
      var response = await ApiHelper.post(AppConfig.apiLogoutUrl);
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }
}

export default AuthPageApi;
