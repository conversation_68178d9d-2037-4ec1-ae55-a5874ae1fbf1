import moment from "moment";

export class CurrentMembershipDto {
    id?: number;
    membership_id?: number;
    membership_name?: string;
    remaining_maintain_purchase?: number;
    point_from?: number;
    point_to?: number;
    total_spent: number;
    expiry_date?: Date;
    membership_tier_image?: string;


    constructor(data: any) {
        try{
            this.expiry_date = moment(data.expiry_date, "YYYY-MM-DD HH:mm:ss").toDate();
        }catch(_){}

        this.id = data?.id;
        this.membership_id = data?.membership_id;
        this.membership_name = data?.membership_name;
        this.remaining_maintain_purchase = data?.remaining_maintain_purchase;
        this.point_from = data?.point_from;
        this.point_to = data?.point_to;
        this.total_spent = data?.total_spent ?? 0;
        this.membership_tier_image = data?.membership_tier_image;
    }
}