<script setup lang="ts">
import { onMounted, watch } from 'vue';
import ColorPick from './helpers/colorpick';
import { useDisplay } from 'vuetify';

//Handle Display
// const { smAndUp,smAndDown } = useDisplay();

// onMounted(() => {
//   //pc
//   if (smAndUp.value) {
//  let ifrTag = document.getElementsByTagName('iframe')[0]
    
//   ifrTag = document.createElement('iframe')
//   document.body.innerHTML = ''
//   document.body.style = `background-color: ${ColorPick.primaryColor}`
//   ifrTag.setAttribute('src', window.location.href)
//   let styleObj = {
//     width: '320px',
//     height: '640px',
//     position: 'absolute',
//     left: '50%',
//     top: '50%',
//     transform: 'translateX(-50%) translateY(-50%)',
//     border: 'none',
//    }
//    Object.entries(styleObj).forEach(([key, value]: [any, string]) => {
//      ifrTag.style[key] = value
//    })
//    document.body.append(ifrTag)
//   }
// })

// watch([smAndUp,smAndDown], () => {
//   let ifrTag = document.getElementsByTagName('iframe')[0]

//   if (smAndUp.value) {
//     ifrTag = document.createElement('iframe')
//     document.body.innerHTML = ''
//     document.body.style = `background-color: ${ColorPick.primaryColor}`
//     ifrTag.setAttribute('src', window.location.href)
//     let styleObj = {
//       width: '320px',
//       height: '640px',
//       position: 'absolute',
//       left: '50%',
//       top: '50%',
//       transform: 'translateX(-50%) translateY(-50%)',
//       border: 'none',
//     }
//     Object.entries(styleObj).forEach(([key, value]: [any, string]) => {
//       ifrTag.style[key] = value
//     })
//     document.body.append(ifrTag)
//   }else{
//     if(ifrTag){
//       location.reload();
//     }
//   }
// })
</script>

<template>
  <VApp>
    <RouterView />
  </VApp>
</template>

<style lang="scss">
// Global Href Primary Color
a {
  color: "primary";
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
}

a {
    color: v-bind('ColorPick.primaryColor');
    background: linear-gradient(to right, v-bind('ColorPick.primaryColor'), v-bind('ColorPick.primaryColor')) no-repeat;
    background-size: 0 2px;
    background-position: right bottom;
    transition: background-size 0.3s;
}

a:hover {
  background-size: 100% 2px;
  background-position: left bottom;
}
</style>
