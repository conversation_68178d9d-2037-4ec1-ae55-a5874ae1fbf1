import AppConfig from "@/appconfig";
import { CartSummaryDto } from "@/dto/cart_summary.dto";
import { CouponDto, CreateCouponDto } from "@/dto/coupon.dto";
import { MyOrderDto } from "@/dto/order.dto";
import ApiHelper from "@/helpers/api_helper";
import { translate } from "@/plugins/i18n";

class CartsApi {
  //Add to Cart
  public static async addToCart(
    product_id?: number,
    product_qty?: number,
  ): Promise<any> {
    try {
      var response = await ApiHelper.post(AppConfig.apiCartAddUrl, undefined, {
        product_id: product_id,
        product_qty: product_qty,
      });
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }

  //Get Cart Summary
  public static async summary(outlet_id?: number, address_id?: number): Promise<CartSummaryDto> {
    try {

      var response = await ApiHelper.get(
        AppConfig.apiCartSummaryUrl,
        undefined,
        {
          outlet_id: outlet_id !== 0 ? outlet_id : '',
          address_id:address_id !== 0 ? address_id : '',
        }
      );
      const tempSummary: CartSummaryDto = new CartSummaryDto(
        response.data.data
      );

      return tempSummary;
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  //Get Cart Summary

  // Cart update address
  public static async updateAddress(address_id?: number): Promise<any> {
    try {
      var response = await ApiHelper.post(
        AppConfig.apiCartUpdateAddressUrl,
        undefined,
        {
          address_id: address_id,
        }
      );
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }
  // Cart update address

  // Cart delete Item
  public static async deleteProduct(cart_product_id?: number): Promise<any> {
    try {
      var response = await ApiHelper.post(
        AppConfig.apiCartDeleteProductUrl,
        undefined,
        {
          cart_product_id: cart_product_id,
        }
      );
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }
  // Cart update address

  //Badge
  public static async badge(): Promise<number> {
    var tempNumber: number = 0;
    try {
      var response = await ApiHelper.get(AppConfig.apiCartBadgeUrl);

      tempNumber = parseInt(response.data.data.cart_count);
    } catch (_) {}

    return tempNumber;
  }
  //Badge

  public static async orderCreate(
    paymentMethod: string,
    remark?: string,
    delivery_method?: string,
    outlet_id?: number,
  ): Promise<string> {
    try {

      var response = await ApiHelper.post(
        AppConfig.apiOrderCreateUrl,
        undefined,
        {
          payment_method: paymentMethod,
          remarks: remark,
          delivery_method: delivery_method,
          outlet_id: outlet_id,
        }
      );
    
      var url: URL | undefined;

      try {
        url = new URL(response?.data?.data?.payment_url);
      } catch (_) {}

      if (url) {
        return url.toString();
      } else {
        throw translate("payment_url_not_found");
      }
    } catch (error) {
      console.log('error',error);
      throw error;
    }
  }

  public static async orderDetails(id: string): Promise<MyOrderDto> {
    try {
      var response = await ApiHelper.get(AppConfig.apiOrderUrl + id);
      const tempOrderDetails: MyOrderDto = new MyOrderDto(
        response.data.data
      );
      return tempOrderDetails;
    } catch (error) {
      throw error;
    }
  }

  // Apply Voucher
  public static async applyVoucher(
    voucher_id: string,
    amount: string
  ): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiRedeemedVoucherUrl}apply`,
        undefined,
        {
          amount: amount,
          voucher_id: voucher_id,
        }
      );

      return response.data.data;
    } catch (error) {
      throw error;
    }
  }
  // Apply Voucher

  // Remove Voucher from cart
  public static async removeVoucher(voucher_id: string): Promise<any> {
    try {
      var response = await ApiHelper.get(
        `${AppConfig.apiRedeemedVoucherUrl}${voucher_id}/remove`,
        undefined,
        {
          voucher_id: voucher_id,
        }
      );
      return response.data.data;
    } catch (error) {
      throw error;
    }
  }
  // Remove Voucher from cart

  // Apply Coupon
  public static async applyCoupon(
    coupon_code: string,
    amount: string
  ): Promise<CreateCouponDto> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiCouponApplyUrl}`,
        undefined,
        {
          amount: amount,
          coupon_code: coupon_code,
        }
      );

      const tempCouponData: CreateCouponDto = new CreateCouponDto(response.data.data);

      return tempCouponData;
    } catch (error) {
      throw error;
    }
  }
  // Apply Coupon

  // Remove Coupon from cart
  public static async removeCoupon(coupon_id: string): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiCouponRemoveUrl}/${coupon_id}`,
        undefined
      );
      return response.data.data;
    } catch (error) {
      console.log('error', error);
      throw error;
    }
  }
  // Remove Coupon from cart


    // Create Rating Order
  public static async ratingOrder(order_id: string, rating: number ,review: string): Promise<any> {
    try {
      var response = await ApiHelper.post(
        `${AppConfig.apiOrderUrl}rating`,
        undefined ,
        {
          order_id: order_id,
          rating: rating,
          review: review
        }
      );
      return response.data.data;
    } catch (error) {
      console.log('error', error);
      throw error;
    }
  }
  // Remove Coupon from cart
}

export default CartsApi;
