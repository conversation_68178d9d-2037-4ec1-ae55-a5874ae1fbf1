import AppConfig from "@/appconfig";
import { MembershipDto } from "@/dto/membership.dto";
import ApiHelper from "@/helpers/api_helper";
import AuthPageApi from "./auth_api";
import type { UserDto } from "@/dto/user.dto";

class MembershipTiersApi {
  //Get Membership Tiers List
  public static async list(): Promise<MembershipDto[]> {
    try {
      var userData: UserDto = await AuthPageApi.getProfile();

      var response = await ApiHelper.post(AppConfig.apiMembershipTiersUrl);

      const tempMemberships: MembershipDto[] = response.data.data.map(
        (item: any) => {
          var total_spent: number | undefined;
          var expiry_date: Date | undefined;

          if (userData.current_membership?.membership_id == item.id) {
            total_spent = userData.current_membership?.total_spent;
            expiry_date = userData.current_membership?.expiry_date;
          }


          return  new MembershipDto({
              id: item.id,
              name: item.name,
              image: item.image,
              point_from: item.point_from,
              point_to: item.point_to,
              min_purchase: item.min_purchase,
              min_spent: item.min_spent,
              min_days: item.min_days,
              next_level_name: item.next_level_name,
              benefits: item.benefits,
              total_spent: total_spent,
              expiry_date: expiry_date,
          });
        }
      );

      return tempMemberships;
    } catch (error) {
      throw error;
    }
  }
  //Get Membership Tiers List
}

export default MembershipTiersApi;
