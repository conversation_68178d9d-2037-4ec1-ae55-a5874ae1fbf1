import { CartProductsDto } from "./cart_summary.dto";
import { RedeemedRewardsDto } from "./rewards.dto";
import { StoreLocatorDto } from "./store_locators.dto";

export class OrderPaymentDto {
  id: number;
  order_no: string;
  show_msg: boolean;
  user_id: number;
  retailer_id: number;
  tracking_number: string;
  delivery_method: string;
  shipping_fee: string;
  voucher_id: string;
  voucher: string;
  delivery_discount: string;
  coupon_id: string;
  coupon: string;
  subtotal: string;
  total: string;
  total_tax: string;
  point_earned: number;
  name: string;
  email_address: string;
  phone_number: string;
  address_recipient: string;
  address_phone: string;
  address: string;
  city: string;
  postcode: string;
  state: string;
  address_country: string;
  receipt: string;
  rating: number;
  review: string;
  payment_method: string;
  sales_channel: string;
  outlet_id: number;
  delivery_vehicle: string;
  lalamove_quotation_id: string;
  lalamove_status: string;
  share_link: string;
  status: string;
  remark: string;
  dinlr_id: string;
  currency: string;
  created_date: string;
  payment_date: string;
  payment_ref_no: string;
  products: CartProductsDto;
  avaiable_vouchers: RedeemedRewardsDto[];
  tracking_link: string;
  outlet: StoreLocatorDto;
  coupon_detail?: CouponDetailDto;
  applied_vouchers?: RedeemedRewardsDto[];

  constructor(data: any) {
    try {
      this.avaiable_vouchers = data.avaiable_vouchers.map(
        (item: any) => new RedeemedRewardsDto(item)
      );
    } catch (_) {
      this.avaiable_vouchers = [];
    }

    this.id = data.id;
    this.order_no = data.order_no;
    this.show_msg = data.show_msg == 1;
    this.user_id = data.user_id;
    this.retailer_id = data.retailer_id;
    this.tracking_number = data.tracking_number;
    this.delivery_method = data.delivery_method;
    this.shipping_fee = data.shipping_fee;
    this.voucher_id = data.voucher_id;
    this.voucher = data.voucher;
    this.delivery_discount = data.delivery_discount;
    this.coupon_id = data.coupon_id;
    this.coupon = data.coupon;
    this.subtotal = data.subtotal;
    this.total = data.total;
    this.total_tax = data.total_tax;
    this.point_earned = data.point_earned;
    this.name = data.name;
    this.email_address = data.email_address;
    this.phone_number = data.phone_number;
    this.address_recipient = data.address_recipient;
    this.address_phone = data.address_phone;
    this.address = data.address;
    this.city = data.city;
    this.postcode = data.postcode;
    this.state = data.state;
    this.address_country = data.address_country;
    this.receipt = data.receipt;
    this.rating = data.rating;
    this.review = data.review;
    this.payment_method = data.payment_method;
    this.sales_channel = data.sales_channel;
    this.outlet_id = data.outlet_id;
    this.delivery_vehicle = data.delivery_vehicle;
    this.lalamove_quotation_id = data.lalamove_quotation_id;
    this.lalamove_status = data.lalamove_status;
    this.share_link = data.share_link;
    this.status = data.status;
    this.remark = data.remark;
    this.dinlr_id = data.dinlr_id;
    this.created_date = data.created_date;
    this.payment_date = data.payment_date;
    this.payment_ref_no = data.payment_ref_no;
    this.tracking_link = data.tracking_link;
    this.outlet = new StoreLocatorDto(data.outlet);
    this.currency = data.currency;
    this.products = new CartProductsDto(data.products);
    this.address = data.address;
    if(data.coupon_detail){
      this.coupon_detail = new CouponDetailDto(data.coupon_detail);
    }

    try {
      this.applied_vouchers = data.applied_vouchers.map(
        (item: any) => new RedeemedRewardsDto(item)
      );
    } catch (_) {
      this.applied_vouchers = [];
    }
  }
}

export class CouponDetailDto{
  id: number;  
  name: string;
  code: string;
  type: string;
  value: string;
  minimum_amount: number;
  limit_per_user: number;
  product_ids: string;
  category_ids: string
  start_datetime: string;
  expiry_datetime: string;
  is_active: number;
  created_at : string;
  updated_at : string;
  deleted_at : string;
  coupon_value: string;
  available_date: string;

  constructor(data: any) {
    this.id = data.id;
    this.name = data.name;
    this.code = data.code;
    this.type = data.type;
    this.value = data.value;
    this.minimum_amount = data.minimum_amount;
    this.limit_per_user = data.limit_per_user;
    this.product_ids = data.product_ids;
    this.category_ids = data.category_ids;
    this.start_datetime = data.start_datetime;
    this.expiry_datetime = data.expiry_datetime;
    this.is_active = data.is_active;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
    this.deleted_at = data.deleted_at;
    this.coupon_value = data.coupon_value;
    this.available_date = data.available_date;
  }

}