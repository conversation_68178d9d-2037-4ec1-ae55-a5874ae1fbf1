<script setup lang="ts">
// Components
import AppBarPlus from "@/components/custom_control/AppBarPlus.vue";

// Helpers
import { useI18n } from "vue-i18n";
import { ref, onMounted, onUnmounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";

// Apis
import HomePageStoreLocatorApi from "@/services/homepage_store_locator_api";

// Dtos
import type { StoreLocatorDto } from "@/dto/store_locators.dto";

// Images
import placeholder from "@/assets/icons/logo.png";
import locationLogo from "@/assets/icons/location.png";
import operationHoursLogo from "@/assets/icons/operation_hour_icon.png";
import contactLogo from "@/assets/icons/contact_icon.png";

import ColorPick from "@/helpers/colorpick";

// Language
const { t } = useI18n();
const route = useRoute();
const router = useRouter();

// General Variable
const isLoading = ref<boolean>(false);
const openDialog = ref<boolean>(false);
const dialogMsg = ref<string>("");

// Variables
const data = ref<StoreLocatorDto>();

// Capitalize first letter of the day
const capitalize = (text: string) =>
  text.charAt(0).toUpperCase() + text.slice(1);

// Convert "08:00:00" → "08:00 AM"
const formatTime = (time: string) => {
  if (!time) return ""; // Handle empty values
  const [hour, minute] = time.split(":").map(Number);
  const ampm = hour >= 12 ? "PM" : "AM";
  const formattedHour = hour % 12 || 12; // Convert to 12-hour format
  return `${formattedHour}:${minute.toString().padStart(2, "0")} ${ampm}`;
};

// Fetch Screen Width
const screenWidth = ref(window.innerWidth);

const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth;
};

function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

const goToMap = () => {
  const latitude = data.value?.latitude;
  const longitude = data.value?.longitude;

  if (latitude && longitude) {
    const latlong = `${latitude},${longitude}`;
    window.open(`https://maps.google.com/?q=${latlong}`, "_blank");
  } else {
    console.warn("Latitude or Longitude is missing");
  }
};

onMounted(() => {
  window.scrollTo(0, 0);
  window.addEventListener("resize", updateScreenWidth);

  getAvailableStoreDetails();
});

onUnmounted(() => {
  window.removeEventListener("resize", updateScreenWidth);
});

// Get Available Stores Detail
async function getAvailableStoreDetails() {
  try {
    isLoading.value = true;

    data.value = await HomePageStoreLocatorApi.outletDetail(
      route.params.id.toString()
    );

    if (data.value) {
      try {
        // Parse operating hours
        if (typeof data.value?.operating_hours === "string") {
          data.value.operating_hours = JSON.parse(data.value.operating_hours);
        }
      } catch (_) {}
    }
  } catch (error) {
    dialogMsg.value = `${error}`;
    openDialog.value = true;
  } finally {
    isLoading.value = false;
  }
}
</script>

<template>
  <LoadingPlus v-if="isLoading" />

  <AppBarPlus :title="t('store_location')" :back="true" />

  <!-- AppBarPlus Spacing -->
  <div style="height: 60px" />

  <div v-if="data" class="store-locator-detials">
    <div
      class="store-locator-detials-container"
      :class="screenWidth < 615 ? '' : 'pad'"
      :style="{ width: `${screenWidth}px` }"
    >
      <!-- Store Image -->
      <div class="store-locator-detials-image">
        <VImg
          :src="data?.image_url === null ? placeholder : data?.image_url"
          :rounded="screenWidth < 615 ? 0 : 'lg'"
          maxWidth="615"
          :cover="true"
        />

        <div style="height: 10px" />

        <div class="store-details">
          <!-- Store Name -->
          <div class="store-locator-details-title">{{ data.name }}</div>

          <div style="height: 10px" />

          <!-- Store Address -->
          <div class="store-info">
            <img :src="locationLogo" class="icon mt-1" />
            <div style="width: 10px" />
            <span>{{ data.address }}</span>
          </div>

          <div style="height: 10px" />

          <!-- Operation Hours -->
          <div class="store-info-operating-hours">
            <img :src="operationHoursLogo" class="icon mt-1" />

            <div v-if="data?.operating_hours">
              <div
                v-for="(time, day) in data.operating_hours"
                :key="day"
                class="store-info"
              >
                <strong>{{ capitalize(day.toString()) }}: </strong>

                <div style="width: 10px" />

                <span v-if="time.from && time.to">
                  {{ formatTime(time.from) }} - {{ formatTime(time.to) }}
                </span>
                <span v-else> {{ t("closed") }} </span>
              </div>
            </div>
            <!-- <span>{{ dummyData.operation_hours }}</span> -->
          </div>

          <div style="height: 10px" />

          <!-- Contact -->
          <div class="store-info">
            <img :src="contactLogo" class="icon mt-1" />
            <span>{{ t("contact") }}: </span>
            <div style="width: 10px" />
            <a
              :href="'tel:' + data.contact_number"
              target="_blank"
              class="store-info"
              >{{ data.contact_number }}</a
            >
          </div>

          <div style="height: 10px" />

          <!-- Get Direction Button -->
          <VContainer @click="goToMap">
            <VBtn variant="text" class="get-direction">
              <span>{{ t("get_direction") }}</span>
            </VBtn>
          </VContainer>
        </div>
      </div>
    </div>
  </div>
  <!-- Dialog -->
  <DialogPlus
    persistent
    v-model="openDialog"
    :confirmText="t('back')"
    :onClickConfirm="
      () => {
        openDialog = false;
        goBack();
      }
    "
  >
    {{ dialogMsg }}
  </DialogPlus>
</template>

<style lang="scss" scoped>
.pad {
  padding: 5px 10px;
}

.store-locator-detials {
  width: 100%;
  display: inline-table;
  text-align: -webkit-center;
}

.store-locator-detials-container {
  max-width: 600px;
}

.store-locator-detials-image {
  max-width: 600px;
  width: "100%";
  aspect-ratio: 1/1;
}

.store-details {
  padding: 20px;
  text-align: left;
}

.store-locator-details-title {
  font-size: 15px;
  font-weight: 600;
  color: v-bind("ColorPick.primaryColor");
  margin-bottom: 15px;
}

.store-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 13px;
  font-weight: 400;
  color: #5f5e5e;
  align-items: start;
}

img.icon {
  width: 15px;
  height: 15px;
  object-fit: contain;
  margin-right: 10px;
}

.store-info-operating-hours {
  display: flex;
  align-items: start;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 300;
  color: #5f5e5e;
}

.get-direction {
  width: 100%;
  color: white;
  text-align: center;
  font-size: 13px;
  font-weight: 400;
  cursor: pointer;
  height: 50px;
  text-decoration: underline;
  text-underline-offset: 10px;
  text-transform: none;
  color: #4b4b4b;

  padding: 10px;

  span {
    letter-spacing: 0.5px;
    color: #4b4b4b;
  }
}
</style>
