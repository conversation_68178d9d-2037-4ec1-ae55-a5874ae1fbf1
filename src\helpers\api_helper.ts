import axios, { AxiosError } from "axios";
import localStorageHelper from "./localstorage_helper";
import RandomHelper from "./random_helper";

axios.defaults.withCredentials = true;
class ApiHelper {
  // Set withCredentials to true for all requests
  private static getHeaders(): object {
    var body = <any>{
      "Content-Type": "application/json",
    };
    var token: string | null = null;

    token = localStorageHelper.getUserToken();
    if (token !== null) {
      body["Authorization"] = "Bearer " + token;
    }

    //Header and Token Handler
    return body;
  }

  //Check if current path is dashboard redirect to dashboard
  private static goBackToHome(): string {
    return "/";
  }

  //Check Response Code
  private static checkResponseCode(status: any) {
    if (status === 401) {
      localStorageHelper.clearUser();
      localStorageHelper.clearAll();

      window.location.replace(ApiHelper.goBackToHome());
    }
  }

  // Function to process a single file object
  private static async processFileObject(file: any): Promise<File | null> {
    if (file && typeof file === "object" && "url" in file) {
      try {
        const response = await fetch(file.url);
        const blob = await response.blob();
        return new File([blob], file.name, { type: blob.type });
      } catch (error) {
        return null;
      }
    }
    return null;
  }

  // Function to convert body object to FormData
  private static async convertToFormData(body: any): Promise<FormData> {
    const formData = new FormData();

    if (body !== undefined && body !== null) {
      for (const [key, value] of Object.entries(body)) {
        if (Array.isArray(value)) {
          // Handle array of files
          for (let i = 0; i < value.length; i++) {
            const processedFile = await ApiHelper.processFileObject(value[i]);
            if (processedFile) {
              formData.append(`${key}`, processedFile, processedFile.name);
            } else {
              formData.append(`${key}[]`, value[i]);
            }
          }
        } else if (value && typeof value === "object" && "url" in value) {
          // Handle single file object
          const processedFile = await ApiHelper.processFileObject(value);
          if (processedFile) {
            formData.append(key, processedFile, processedFile.name);
          }
        } else if (value instanceof File) {
          formData.append(key, value, value.name);
        } else if (value instanceof Blob) {
          formData.append(key, value, RandomHelper.makeRandomString());
        } else if (typeof value === "object" && !(value instanceof Date)) {
          formData.append(key, JSON.stringify(value));
        } else {
          formData.append(key, `${value}`);
        }
      }
    }

    return formData;
  }

  //Get API Response
  public static async get(
    url: string, //required API URL
    headers?: object, //API Headers
    parameters?: object, //API Parameters
    messageKey: string = "message", //Message Key in Api
    dataKey?: string, //Data Key in Api
    statusKey: string = "status",
    statusValueDefaultTrue: any = true
  ): Promise<any> {
    headers = headers ?? ApiHelper.getHeaders();
    try {
      var successful: boolean = true;
      var message: string = "";
      var data: any = "";

      var tempUrl = url;
      if (parameters !== undefined) {
        tempUrl += "?";
        for (const [key, value] of Object.entries(parameters)) {
          if (value !== undefined && value !== null) {
            tempUrl += `${key}=${value}&`;
          }
        }
      }

      const response = await axios.get(tempUrl, {
        headers: headers,
      });

      //Check API Return status
      if (
        dataKey !== null &&
        dataKey !== undefined &&
        response.data[dataKey] !== undefined &&
        response.data[dataKey] !== null
      ) {
        data = response.data[dataKey];
      } else {
        data = response.data;
      }

      //Get Message
      if (
        response.data[messageKey] !== undefined &&
        response.data[messageKey] !== null
      ) {
        message = response.data[messageKey].toString();
      }

      if (
        statusKey &&
        response.data[statusKey] !== undefined &&
        response.data[statusKey] !== null
      ) {
        successful = response.data[statusKey] === statusValueDefaultTrue;
      }

      //If successful return data else throw error
      if (successful) {
        return {
          message: message,
          data: data,
        };
      } else {
        throw message;
      }
    } catch (e) {
      if (e instanceof AxiosError) {
        ApiHelper.checkResponseCode(e.status);

        if (e.status !== 401) {
          throw e.response?.data["message"] ?? e;
        }
      } else if (e instanceof Error) {
        var message: string = e["message"] ?? e.toString();
        throw message;
      }
      throw e;
    }
  }
  //Get API Response

  //Post API Response
  public static async post(
    url: string, //required API URL
    headers?: any,
    body?: object, //API body
    contentType: string = "application/json", //Content Type
    messageKey: string = "message", //Message Key in Api
    dataKey?: string, //Data Key in Api
    statusKey: string = "status",
    statusValueDefaultTrue: any = true
  ): Promise<any> {
    headers = headers ?? ApiHelper.getHeaders() ?? {};
    try {
      //make post request can upload file
      headers["Content-Type"] = contentType;

      var successful: boolean = true;
      var message: string = "";
      var data: any = "";

      var response: any;

      if (contentType === "multipart/form-data") {
        //body convert to form data
        var formData = new FormData();

        if (body !== undefined && body !== null) {
          formData = await ApiHelper.convertToFormData(body);
        }

        response = await axios.post(url, formData, {
          headers: headers,
        });
      } else {
        response = await axios.post(url, body, {
          headers: headers,
        });

      }

      //Check API Return status
      if (
        dataKey !== null &&
        dataKey !== undefined &&
        response.data[dataKey] !== undefined &&
        response.data[dataKey] !== null
      ) {
        data = response.data[dataKey];
      } else {
        data = response.data;
      }

      //Get Message
      if (
        response.data[messageKey] !== undefined &&
        response.data[messageKey] !== null
      ) {
        message = response.data[messageKey].toString();
      }

      if (
        statusKey &&
        response.data[statusKey] !== undefined &&
        response.data[statusKey] !== null
      ) {
        successful = response.data[statusKey] === statusValueDefaultTrue;
      }

      //If successful return data else throw error
      if (successful) {
        return {
          message: message,
          data: data,
        };
      } else {
        throw message;
      }
    } catch (e) {
      if (e instanceof AxiosError) {
        ApiHelper.checkResponseCode(e.status);

        if (e.status !== 401) {
          throw e.response?.data["message"] ?? e;
        }
      } else if (e instanceof Error) {
        var message: string = e["message"] ?? e.toString();
        throw message;
      }
      throw e;
    }
  }
  //Post API Response

  //Patch API Response
  public static async patch(
    url: string, //required API URL
    headers?: any,
    body?: object, //API body
    contentType: string = "application/json", //Content Type
    messageKey: string = "message", //Message Key in Api
    dataKey?: string, //Data Key in Api
    statusKey: string = "status",
    statusValueDefaultTrue: any = true
  ): Promise<any> {
    headers = headers ?? ApiHelper.getHeaders() ?? {};

    try {
      //make post request can upload file
      headers["Content-Type"] = contentType;

      var successful: boolean = true;
      var message: string = "";
      var data: any = "";

      var response: any;

      if (contentType === "multipart/form-data") {
        //body convert to form data
        var formData = new FormData();

        if (body !== undefined && body !== null) {
          formData = await ApiHelper.convertToFormData(body);
        }

        response = await axios.patch(url, formData, {
          headers: headers,
        });
      } else {
        response = await axios.patch(url, body, {
          headers: headers,
        });
      }
      //Check API Return status
      if (
        dataKey !== null &&
        dataKey !== undefined &&
        response.data[dataKey] !== undefined &&
        response.data[dataKey] !== null
      ) {
        data = response.data[dataKey];
      } else {
        data = response.data;
      }

      //Get Message
      if (
        response.data[messageKey] !== undefined &&
        response.data[messageKey] !== null
      ) {
        message = response.data[messageKey].toString();
      }

      if (
        statusKey &&
        response.data[statusKey] !== undefined &&
        response.data[statusKey] !== null
      ) {
        successful = response.data[statusKey] === statusValueDefaultTrue;
      }

      //If successful return data else throw error
      if (successful) {
        return {
          message: message,
          data: data,
        };
      } else {
        throw message;
      }
    } catch (e) {
      if (e instanceof AxiosError) {
        ApiHelper.checkResponseCode(e.status);

        if (e.status !== 401) {
          throw e.response?.data["message"] ?? e;
        }
      } else if (e instanceof Error) {
        var message: string = e["message"] ?? e.toString();
        throw message;
      }
      throw e;
    }
  }
  //Patch API Response

  //Delete API Response
  public static async delete(
    url: string, //required API URL
    headers?: any,
    body?: object, //API body
    contentType: string = "application/json", //Content Type
    messageKey: string = "message", //Message Key in Api
    dataKey?: string, //Data Key in Api
    statusKey: string = "status",
    statusValueDefaultTrue: any = true
  ): Promise<any> {
    headers = headers ?? ApiHelper.getHeaders() ?? {};
    try {
      //make post request can upload file
      headers["Content-Type"] = contentType;

      var successful: boolean = true;
      var message: string = "";
      var data: any = "";

      const response = await axios.delete(url, {
        headers: headers,
        data: body,
      });

      //Check API Return status
      if (
        dataKey !== null &&
        dataKey !== undefined &&
        response.data[dataKey] !== undefined &&
        response.data[dataKey] !== null
      ) {
        data = response.data[dataKey];
      } else {
        data = response.data;
      }

      //Get Message
      if (
        response.data[messageKey] !== undefined &&
        response.data[messageKey] !== null
      ) {
        message = response.data[messageKey].toString();
      }

      if (
        statusKey &&
        response.data[statusKey] !== undefined &&
        response.data[statusKey] !== null
      ) {
        successful = response.data[statusKey] === statusValueDefaultTrue;
      }

      //If successful return data else throw error
      if (successful) {
        return {
          message: message,
          data: data,
        };
      } else {
        throw message;
      }
    } catch (e) {
      if (e instanceof AxiosError) {
        ApiHelper.checkResponseCode(e.status);

        if (e.status !== 401) {
          throw e.response?.data["message"] ?? e;
        }
      } else if (e instanceof Error) {
        var message: string = e["message"] ?? e.toString();
        throw message;
      }
      throw e;
    }
  }
  //Delete API Response
}

export default ApiHelper;
