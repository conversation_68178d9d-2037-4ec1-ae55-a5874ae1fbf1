class RoutersHelper {
  //Maintanance
  static maintenance: string = `/maintenance`;

  //Auth
  static login: string = `/login`;
  static otp: string = `/otp`;
  static setupProfile: string = `/setup-profile`;
  static referral: string = `/referral`;

  //Home
  static home: string = `/home`;
  static aboutUs: string = '/about-us';
  static privacyPolicy: string = '/privacy-policy';
  static termsAndCondition: string = '/terms-and-condition';
  static settings: string = `/settings`;
  static referralProgram: string = `/referral-program`;

  // Product
  static product: string = `/product`;
  static productDetail: string = `/product-detail`;
  static orderConfirmation: string = `/order-confirmation`;
  static orderDetails: string = `/order-details`;

  static orders: string = `/orders`;
  static rewards: string = `/rewards`;
  static myPoints: string = `/my-points`;
  static rewardDetails: string = `/reward-details`;
  // static myRewardDetails: string = `/my-reward-details`;
  static account: string = `/account`;
  static address: string = `/address`;

  // News and announcement
  static newsAnnouncement: string = `/news-announcement`;
  static newsAnnouncementDetails: string = `/news-announcement-details`;

  // Store Locator
  static storeLocator: string = `/store-locator`;
  static storeLocatorDetails: string = `/store-locator-details`;
  static storeLocatorAvailableStore: string = `/store-locator-available-store`;

  // Profile
  static myProfile: string = `/my-profile`;
  static faq: string = `/faq`;
  static memberBenefits: string = `/member-benefits`;
  static myWishlist: string = `/my-wishlist`;

  // Order Pay (Whatsapp Order)
  static orderPay: string = `/order-pay`;
  static orderPayResult: string = `/order-pay-result`;
}

export default RoutersHelper;
