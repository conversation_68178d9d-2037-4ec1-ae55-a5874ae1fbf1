<script setup lang="ts">
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import { useDisplay } from "vuetify";
import ButtonPlus from "@/components/custom_control/ButtonPlus.vue";
import complete from "@/assets/images/complete.png";
import error from "@/assets/images/error.png";
import RoutersHelper from "@/helpers/routes_helper";
import { computed, onMounted } from "vue";
// import Logo from '@/components/Logo.vue';
import ColorPick from "@/helpers/colorpick";
import localStorageHelper from "@/helpers/localstorage_helper";

const { t } = useI18n();
const { xs } = useDisplay();
const route = useRoute();
const router = useRouter();

const status = computed(() => route.query.status === "success");

const token = localStorageHelper.getUserToken();

onMounted(() => {
  window.scrollTo(0, 0);
});

function back() {
  if (token !== null) {
    router.push(RoutersHelper.orders);
  } else {
    router.push(RoutersHelper.home);
  }
}
</script>

<template>
  <Logo />

  <div class="order-pay-result">
    <div class="order-pay-result-container">
      <VImg
        :src="status ? complete : error"
        maxHeight="120px"
        width="120px"
        :alt="status ? 'success' : 'failed'"
        class="result-image"
      />

      <div class="title">
        {{ t("order_successfully_placed_title") }}
      </div>
      <div class="description">
        {{ t("place_order_success_description") }}
      </div>

      <ButtonPlus
        @click="back()"
        width="100%"
        :color="status ? ColorPick.primaryColor : ColorPick.errorColor"
        class="back-button"
      >
        {{ t("back") }}
      </ButtonPlus>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.order-pay-result {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.order-pay-result-container {
  padding: v-bind('xs ? "20px" : "30px"');
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 90vh;
}

.result-image {
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
  text-align: center;
}

.description {
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
  text-align: center;
  line-height: 1.6;
}

.back-button {
  margin-top: 30px;
  padding: 12px 20px;
}
</style>
