<script setup lang="ts">
interface Props {
  image?: string | undefined;
  title?: string | undefined;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  (e: "click"): void;
}>();

const onClick = () => {
  emit("click");
};
</script>

<template>
  <div class="container" @click="onClick">
    <VImg :src="props.image" :cover="false">
      <span class="title-info">{{ props.title }}</span>
    </VImg>
  </div>
</template>

<style lang="scss" scoped>
.container {
  cursor: pointer;
  opacity: 0.9;
}

.title-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  align-items: center;
  justify-content: center;
  text-align: center;
  display: flex;
  font-size: 15px;
  font-weight: 600;
  color: #ffffff;
}
</style>
