<script setup lang="ts">
// Helpers
import { useI18n } from "vue-i18n";
import { useRoute, useRouter } from "vue-router";
import { ref, onMounted, watch, nextTick } from "vue";
import RoutersHelper from "@/helpers/routes_helper";
import ColorPick from "@/helpers/colorpick";
import { OrderStatusEnum } from "@/enums/OrderStatusEnum";
import localStorageHelper from "@/helpers/localstorage_helper";
import { VPullToRefresh } from "vuetify/labs/VPullToRefresh";

// Components
import OrderItem from "@/components/OrderItem.vue";
import NoData from "@/components/custom_control/NoData.vue";
import RequiredLogin from "@/components/RequiredLogin.vue";

// Dtos
import { MyOrderDto } from "@/dto/order.dto";

// APIs
import MyOrderApi from "@/services/my_order_api";
import CapitalizeHelper from "@/helpers/capitalize_helper";

// Language
const { t } = useI18n();

// Route
const route = useRoute();
const router = useRouter();

// General loading, snackbar
const isLoading = ref<boolean>(false);
const openSnackBar = ref<boolean>(false);
const snackBarMsg = ref<string>("");

const openLoginDialog = ref<boolean>(false);

const myOrderList = ref<MyOrderDto[]>([]);
const myActiveOrderList = ref<MyOrderDto[]>([]);
const myPastOrderList = ref<MyOrderDto[]>([]);

const pullDownThreshold = ref(60);

// Tab bar
const activeTab = ref(route.query.tab);

// tab list
const tabs = [
  { title: t("all"), tab: OrderStatusEnum.all },
  { title: t("active"), tab: OrderStatusEnum.active },
  { title: t("past"), tab: OrderStatusEnum.past },
];

// Reward Pagination
const allPage = ref(1);
const allHasMore = ref(true);
let allDoneCopy: (status: "error" | "loading" | "empty" | "ok") => void;
const allListKey = ref(0);
const allInfiniteScrollRef = ref();

// My Reward Pagination
const activePage = ref(1);
const activeHasMore = ref(true);
let activeDoneCopy: (status: "error" | "loading" | "empty" | "ok") => void;
const activeListKey = ref(0);
const activeInfiniteScrollRef = ref();

// Point History Pagination
const pastPage = ref(1);
const pastHasMore = ref(true);
let pastDoneCopy: (status: "error" | "loading" | "empty" | "ok") => void;
const pastListKey = ref(0);
const pastInfiniteScrollRef = ref();

const token = localStorageHelper.getUserToken();

// Function API
// Fetch All order List
async function loadAllOrder() {
  try {
    if (token === null) return undefined;
    const response = await MyOrderApi.myOrderList(allPage.value, undefined, []);
    return response;
  } catch (_) {}
  return undefined;
}

// Fetch Avtive order List
async function loadActiveOrder() {
  try {
    if (token === null) return undefined;
    const response = await MyOrderApi.myOrderList(activePage.value, undefined, [
      "paid",
      "shipped",
    ]);
    return response;
  } catch (_) {}
  return undefined;
}

// Fetch Avtive order List
async function loadPastOrder() {
  try {
    if (token === null) return undefined;
    const response = await MyOrderApi.myOrderList(pastPage.value, undefined, [
      "completed",
      "cancelled",
      "refunded",
    ]);
    return response;
  } catch (_) {}
  return undefined;
}

// All Order List - Infinite Scroll
async function infiniteload({
  done,
}: {
  done: (status: "error" | "loading" | "empty" | "ok") => void;
}) {
  if (!allHasMore.value) {
    done("empty");
    return;
  }

  try {
    const res = await loadAllOrder();

    if (res?.data !== undefined) {
      const newData = res.data ?? [];
      const existingIds = new Set(myOrderList.value.map((item) => item.id));
      const filteredData = newData.filter(
        (item: any) => !existingIds.has(item.id)
      );

      myOrderList.value.push(...filteredData);

      const lastPage = res.last_page ?? 1;

      if (allPage.value >= lastPage) {
        allHasMore.value = false;
        allDoneCopy = done;
      } else {
        allHasMore.value = true;

        allPage.value += 1;
      }
    } else {
      allHasMore.value = false;
      done("empty");
      allDoneCopy = done;
    }
  } catch (e) {
  } finally {
    done("ok");
  }
}

// Active Order List - Infinite Scroll
async function activeInfiniteload({
  done,
}: {
  done: (status: "error" | "loading" | "empty" | "ok") => void;
}) {
  if (!activeHasMore.value) {
    done("empty");
    return;
  }

  try {
    const res = await loadActiveOrder();

    if (res?.data !== undefined) {
      const newData = res.data ?? [];
      const existingIds = new Set(
        myActiveOrderList.value.map((item) => item.id)
      );
      const filteredData = newData.filter(
        (item: any) => !existingIds.has(item.id)
      );

      myActiveOrderList.value.push(...filteredData);

      const lastPage = res.last_page ?? 1;

      if (activePage.value >= lastPage) {
        activeHasMore.value = false;
        activeDoneCopy = done;
      } else {
        activeHasMore.value = true;

        activePage.value += 1;
      }
    } else {
      activeHasMore.value = false;
      done("empty");
      activeDoneCopy = done;
    }
  } catch (e) {
  } finally {
    done("ok");
  }
}

// Past Order List - Infinite Scroll
async function pastInfiniteload({
  done,
}: {
  done: (status: "error" | "loading" | "empty" | "ok") => void;
}) {
  if (!pastHasMore.value) {
    done("empty");
    return;
  }

  try {
    const res = await loadPastOrder();

    if (res?.data !== undefined) {
      const newData = res.data ?? [];
      const existingIds = new Set(myPastOrderList.value.map((item) => item.id));
      const filteredData = newData.filter(
        (item: any) => !existingIds.has(item.id)
      );

      myPastOrderList.value.push(...filteredData);

      const lastPage = res.last_page ?? 1;

      if (pastPage.value >= lastPage) {
        pastHasMore.value = false;
        pastDoneCopy = done;
      } else {
        pastHasMore.value = true;

        pastPage.value += 1;
      }
    } else {
      pastHasMore.value = false;
      done("empty");
      pastDoneCopy = done;
    }
  } catch (e) {
  } finally {
    done("ok");
  }
}

// On Refresh
async function refresh({ done }: { done: () => void }) {
  try {
    switch (activeTab.value) {
      case OrderStatusEnum.all:
        allPage.value = 1;
        myOrderList.value = [];
        allHasMore.value = true;
        allListKey.value += 1;

        allDoneCopy("ok");
        await infiniteload({ done: allDoneCopy });
        break;
      case OrderStatusEnum.active:
        activePage.value = 1;
        myActiveOrderList.value = [];
        activeHasMore.value = true;
        activeListKey.value += 1;

        activeDoneCopy("ok");
        await activeInfiniteload({ done: activeDoneCopy });
        break;
      case OrderStatusEnum.past:
        pastPage.value = 1;
        myPastOrderList.value = [];
        pastHasMore.value = true;
        pastListKey.value += 1;

        pastDoneCopy("ok");
        await pastInfiniteload({ done: pastDoneCopy });
        break;
    }
  } catch (e) {
  } finally {
    done();
  }
}

onMounted(() => {
  var token: string | null = null;

  token = localStorageHelper.getUserToken();
  if (token === null) {
    openLoginDialog.value = true;
  }
});

watch(activeTab, (newValue) => {
  switch (newValue) {
    case "all":
      allPage.value = 1;
      myOrderList.value = [];
      allHasMore.value = true;
      allListKey.value += 1;

      nextTick(() => {
        if (
          allInfiniteScrollRef.value &&
          typeof allInfiniteScrollRef.value.reset === "function"
        ) {
          allInfiniteScrollRef.value.reset();
        }
      });
      break;

    case "active":
      activePage.value = 1;
      myActiveOrderList.value = [];
      activeHasMore.value = true;
      activeListKey.value += 1;

      nextTick(() => {
        if (
          activeInfiniteScrollRef.value &&
          typeof activeInfiniteScrollRef.value.reset === "function"
        ) {
          activeInfiniteScrollRef.value.reset();
        }
      });
      break;

    case "past":
      pastPage.value = 1;
      myPastOrderList.value = [];
      pastHasMore.value = true;
      pastListKey.value += 1;

      nextTick(() => {
        if (
          pastInfiniteScrollRef.value &&
          typeof pastInfiniteScrollRef.value.reset === "function"
        ) {
          pastInfiniteScrollRef.value.reset();
        }
      });
      break;
  }

  router.replace({ query: { tab: newValue, page: allPage.value } });
});

function goBack() {
  if (window.history.state.back === null) {
    router.push("/");
  } else {
    router.back();
  }
}

function toHomePage() {
  router.push(RoutersHelper.home);
}
</script>
<template>
  <!-- Loading -->
  <LoadingPlus v-if="isLoading" />

  <!-- Snack Bar -->
  <SnackBarPlus v-model="openSnackBar">
    {{ snackBarMsg }}
  </SnackBarPlus>

  <AppBarPlus :title="t('my_orders')" />

  <!-- AppBarPlus Spacing -->
  <div style="height: 60px" />

  <div class="my-order">
    <div class="my-order-container">
      <!-- Fixed Tabs -->
      <div class="tabs-wrapper">
        <VContainer class="tab-container">
          <VTabs
            v-model="activeTab"
            class="custom-tabs"
            hide-slider
            align-tabs="center"
            height="40px"
          >
            <VTab
              v-for="item in tabs"
              :key="item.tab"
              :value="item.tab"
              class="custom-tab"
            >
              {{ item.title }}
            </VTab>
          </VTabs>
        </VContainer>
      </div>

      <!-- Tab Content -->
      <div class="content">
        <VWindow v-model="activeTab">
          <VWindowItem :value="OrderStatusEnum.all">
            <VPullToRefresh
              @load="refresh"
              :pullDownThreshold="pullDownThreshold"
            >
              <VContainer>
                <VInfiniteScroll
                  ref="allInfiniteScrollRef"
                  :key="allListKey"
                  :height="'100%'"
                  :items="myOrderList"
                  @load="infiniteload"
                  v-model="allHasMore"
                >
                  <template v-slot:empty>
                    <NoData v-if="myOrderList?.length <= 0" />
                    <span v-else>{{ t("no_more") }}</span>
                  </template>

                  <template
                    v-for="(item, index) in myOrderList"
                    :key="item"
                    v-if="myOrderList?.length ?? 0 > 0"
                  >
                    <OrderItem
                      :orderId="item.order_no"
                      :orderDate="item.created_at.toString()"
                      :totalPrice="item.total"
                      :status="item.status"
                      :order-type="
                        CapitalizeHelper.capitalize(item.delivery_method ?? '')
                      "
                      :productList="item.products"
                      @click="
                        () => {
                          router.push(
                            `${RoutersHelper.orderDetails}/${item.id}`
                          );
                        }
                      "
                    ></OrderItem>
                  </template>
                </VInfiniteScroll>
              </VContainer>
            </VPullToRefresh>
          </VWindowItem>
          <VWindowItem :value="OrderStatusEnum.active">
            <VPullToRefresh
              @load="refresh"
              :pullDownThreshold="pullDownThreshold"
            >
              <VContainer>
                <VInfiniteScroll
                  ref="activeInfiniteScrollRef"
                  :key="activeListKey"
                  :height="'100%'"
                  :items="myActiveOrderList"
                  @load="activeInfiniteload"
                  v-model="activeHasMore"
                >
                  <template v-slot:empty>
                    <NoData v-if="myActiveOrderList?.length <= 0" />
                    <span v-else>{{ t("no_more") }}</span>
                  </template>

                  <template
                    v-for="(item, index) in myActiveOrderList"
                    :key="item"
                    v-if="myActiveOrderList?.length ?? 0 > 0"
                  >
                    <OrderItem
                      :orderId="item.order_no"
                      :orderDate="item.created_at.toString()"
                      :totalPrice="item.total"
                      :status="item.status"
                      :order-type="
                        CapitalizeHelper.capitalize(item.delivery_method ?? '')
                      "
                      :productList="item.products"
                      @click="
                        () => {
                          router.push(
                            `${RoutersHelper.orderDetails}/${item.id}`
                          );
                        }
                      "
                    ></OrderItem>
                  </template>
                </VInfiniteScroll>
              </VContainer>
            </VPullToRefresh>
          </VWindowItem>
          <VWindowItem :value="OrderStatusEnum.past">
            <VPullToRefresh
              @load="refresh"
              :pullDownThreshold="pullDownThreshold"
            >
              <VContainer>
                <VInfiniteScroll
                  ref="pastInfiniteScrollRef"
                  :key="pastListKey"
                  :height="'100%'"
                  :items="myPastOrderList"
                  @load="pastInfiniteload"
                  v-model="pastHasMore"
                >
                  <template v-slot:empty>
                    <NoData v-if="myPastOrderList?.length <= 0" />
                    <span v-else>{{ t("no_more") }}</span>
                  </template>

                  <template
                    v-for="(item, index) in myPastOrderList"
                    :key="item"
                    v-if="myPastOrderList?.length ?? 0 > 0"
                  >
                    <OrderItem
                      :orderId="item.order_no"
                      :orderDate="item.created_at.toString()"
                      :totalPrice="item.total"
                      :status="item.status"
                      :order-type="
                        CapitalizeHelper.capitalize(item.delivery_method ?? '')
                      "
                      :productList="item.products"
                      @click="
                        () => {
                          router.push(
                            `${RoutersHelper.orderDetails}/${item.id}`
                          );
                        }
                      "
                    ></OrderItem>
                  </template>
                </VInfiniteScroll>
              </VContainer>
            </VPullToRefresh>
          </VWindowItem>
        </VWindow>
      </div>
    </div>
  </div>

  <!-- Required login dialog -->
  <RequiredLogin v-model="openLoginDialog" :isBack="false" />
</template>
<style lang="scss" scoped>
.custom-tab {
  flex: 1;
  text-align: center;
}

.flex {
  display: flex;
}

.pointer {
  cursor: pointer;
}

.my-order {
  width: 100%;
  display: inline-table;
  text-align: -webkit-center;
}

.my-order-container {
  max-width: 600px;
  overflow-y: auto;
  padding: 0px 0px;
  padding-bottom: 70px; //
}

.tabs-wrapper {
  position: fixed;
  top: 64px;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 600px;
  background: white;
  z-index: 1000;
}

.tab-container {
  display: flex;
  justify-content: center;
  max-width: 600px;
  margin: 0 auto;
}

.custom-tabs {
  width: 100%;
  max-width: 600px;
  background-color: white;
}

/* Divider Below Tabs */
.tabs-divider {
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
}

.content {
  margin-top: 50px;
}

.v-tab {
  border: 1px solid #2e1e50;
  border-radius: 20px !important;
  margin: 0px 10px;
  font-weight: 500;
  color: v-bind("ColorPick.primaryColor");
  text-transform: uppercase;
}

.v-tab--selected {
  background-color: #2e1e50 !important;
  /* Adjust to match your design */
  color: white !important;
}

.pagination {
  position: fixed;
  width: 100%;
  max-width: 600px;
  bottom: 70px;
  background-color: v-bind("ColorPick.backgroundColor");
  padding: 10px 0;
}
</style>
