import type { App } from "vue";
import "vue-tel-input/vue-tel-input.css";
import  VueTelInput  from "vue-tel-input";
import { translate } from "./i18n";

export default function (app: App) {
  const globalOptions = {
    mode: "international",
    onlyCountries: [
      "MY",
      "SG",
      "HK",
      "MO",
    ],
    defaultCountry: "MY",
    validCharactersOnly: true,
    inputOptions: {
      placeholder: translate('please_enter_your_phone_number'),
    }
  };

  app.use(VueTelInput, globalOptions);
}
