export class BundleDto {
  bundle_id?: number | undefined;
  bundle_name?: string;
  current_tier: boolean;
  is_mix_match: boolean;
  packages: string[];
  tier_id?: number | undefined;
  tier_name?: string;

  constructor(data: any) {
    try {
      this.packages = data.packages.map((item: any) => item.toString());

    } catch (_) {
      this.packages = [];
    }

    this.bundle_id = data.bundle_id;
    this.bundle_name = data.bundle_name;
    this.current_tier = data.current_tier;
    this.is_mix_match = data.is_mix_match;
    this.tier_id = data.tier_id;
    this.tier_name = data.tier_name;
  }
}
